<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getUser } from '$lib/User';
	import { getCurrentConnectionState, sseStats } from '$lib/services';
	import { getCsrfToken, isAuthenticated } from '$lib/utils/authUtils';
	import { diagnose429Error, checkCookies } from '$lib/utils/sseDebug';

	import type { Breadcrumb } from '$lib/types/types';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import AppLayout from '$components/Layouts/AppLayout.svelte';

	const user = getUser();

	let connectionState = $state({
		status: 'disconnected',
		reconnectAttempts: 0,
		lastError: '',
		latency: 0,
		connectionUptime: 0
	});

	let stats = $state({
		totalConnections: 0,
		totalMessages: 0,
		totalErrors: 0,
		averageLatency: 0
	});

	let isManualReconnecting = $state(false);
	let connectionStartTime = $state<Date | null>(null);
	let diagnosticsResult = $state<any>(null);
	let isDiagnosticRunning = $state(false);

	// 연결 상태 업데이트
	function updateConnectionState() {
		if (browser) {
			const state = getCurrentConnectionState();

			// 연결 상태가 변경되었을 때 시작 시간 기록
			if (state.status === 'connected' && connectionState.status !== 'connected') {
				connectionStartTime = new Date();
			} else if (state.status !== 'connected') {
				connectionStartTime = null;
			}

			// 연결 시간 계산
			let uptime = 0;
			if (connectionStartTime && state.status === 'connected') {
				uptime = Date.now() - connectionStartTime.getTime();
			}

			connectionState = {
				status: state.status,
				reconnectAttempts: state.reconnectAttempts,
				lastError: state.lastError || '',
				latency: state.latency || 0,
				connectionUptime: uptime
			};
		}
	}

	// 통계 업데이트
	function updateStats() {
		if (browser) {
			// sseStats는 writable store이므로 subscribe로 값을 가져와야 합니다
			sseStats.subscribe((currentStats) => {
				stats = {
					totalConnections: currentStats.totalConnections,
					totalMessages: currentStats.totalMessages,
					totalErrors: currentStats.totalErrors,
					averageLatency: Math.round(currentStats.averageLatency)
				};
			})();
		}
	}

	// 수동 재연결 시도
	async function handleManualReconnect() {
		if (isManualReconnecting) return;

		isManualReconnecting = true;
		try {
			const { manualReconnect } = await import('$lib/services/sseConnection');
			await manualReconnect();
			alert('재연결 성공!');
		} catch (error) {
			alert(`재연결 실패: ${error}`);
		} finally {
			isManualReconnecting = false;
		}
	}

	// 서버 연결 테스트
	async function testServerConnection() {
		try {
			const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;

			// CSRF 토큰 가져오기
			const csrfToken = getCsrfToken();
			const headers: Record<string, string> = {
				'Content-Type': 'application/json'
			};

			if (csrfToken) {
				headers['X-CSRF-TOKEN'] = csrfToken;
			}

			const response = await fetch(`${apiEndpoint}/health`, {
				method: 'GET',
				headers,
				credentials: 'include' // Sanctum 쿠키 포함
			});

			if (response.ok) {
				alert('서버 연결 성공!');
			} else {
				alert(`서버 연결 실패: ${response.status} ${response.statusText}`);
			}
		} catch (error) {
			alert(`서버 연결 오류: ${error}`);
		}
	}

	// 테스트 알림 전송 (서버에 POST 요청)
	async function sendTestNotification() {
		try {
			const apiEndpoint = import.meta.env.VITE_SSE_NOTIFICATION_ENDPOINT;

			// CSRF 토큰 가져오기
			const csrfToken = getCsrfToken();
			const headers: Record<string, string> = {
				'Content-Type': 'application/json'
			};

			if (csrfToken) {
				headers['X-CSRF-TOKEN'] = csrfToken;
			}

			const response = await fetch(`${apiEndpoint}/test-notification`, {
				method: 'POST',
				headers,
				credentials: 'include', // Sanctum 쿠키 포함
				body: JSON.stringify({
					message: '테스트 알림입니다!',
					type: 'info'
				})
			});

			if (response.ok) {
				alert('테스트 알림이 전송되었습니다.');
			} else {
				const errorText = await response.text();
				alert(`테스트 알림 전송 실패: ${response.status} ${errorText}`);
			}
		} catch (error) {
			alert(`테스트 알림 전송 오류: ${error}`);
		}
	}

	// SSE 연결 직접 테스트
	async function testDirectSseConnection() {
		try {
			const sseEndpoint = import.meta.env.VITE_SSE_ENDPOINT;

			// Sanctum 쿠키 기반에서는 인증 상태만 확인
			if (!isAuthenticated()) {
				alert('인증되지 않은 상태입니다. 로그인이 필요합니다.');
				return;
			}

			// 쿠키 기반이므로 URL에 토큰 없이 연결
			const url = sseEndpoint || 'https://csp-api.freshlove.net/api/sse/stream';

			// 직접 EventSource 생성하여 테스트 (withCredentials: true)
			const testEventSource = new EventSource(url, {
				withCredentials: true
			});

			testEventSource.onopen = () => {
				alert('직접 SSE 연결 성공!');
				testEventSource.close();
			};

			testEventSource.onerror = (error) => {
				alert('직접 SSE 연결 실패');
				console.error('SSE 연결 오류:', error);
				testEventSource.close();
			};

			// 5초 후 타임아웃
			setTimeout(() => {
				if (testEventSource.readyState !== EventSource.CLOSED) {
					testEventSource.close();
					alert('SSE 연결 타임아웃');
				}
			}, 5000);
		} catch (error) {
			alert(`직접 SSE 연결 오류: ${error}`);
		}
	}

	// 429 에러 진단
	async function runDiagnosis() {
		if (isDiagnosticRunning) return;

		isDiagnosticRunning = true;
		try {
			const result = await diagnose429Error();
			diagnosticsResult = {
				issue: result.issue,
				solution: result.solution,
				details: result.details,
				cookies: checkCookies()
			};
		} catch (error) {
			alert(`진단 실행 오류: ${error}`);
		} finally {
			isDiagnosticRunning = false;
		}
	}

	onMount(() => {
		if (!browser) return;

		// 초기 상태 업데이트
		updateConnectionState();
		updateStats();

		// 주기적 업데이트
		const interval = setInterval(() => {
			updateConnectionState();
			updateStats();
		}, 1000);

		return () => clearInterval(interval);
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '실시간 연결', url: '/sse/test' },
		{ title: '테스트', url: '/sse/test' },
	];
</script>

<svelte:head>
	<title>설정 > 수리비 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">

<div class="container mx-auto p-6">
	<h1 class="text-3xl font-bold mb-6">SSE 연결 테스트</h1>

	<!-- 연결 상태 카드 -->
	<div class="card bg-base-100 shadow-xl mb-6">
		<div class="card-body">
			<h2 class="card-title">연결 상태</h2>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div class="stat">
					<div class="stat-title">상태</div>
					<div
						class="stat-value text-lg {connectionState.status === 'connected'
							? 'text-success'
							: connectionState.status === 'connecting' || connectionState.status === 'reconnecting'
								? 'text-warning'
								: 'text-error'}"
					>
						{connectionState.status}
					</div>
				</div>

				<div class="stat">
					<div class="stat-title">재연결 시도</div>
					<div class="stat-value text-lg">{connectionState.reconnectAttempts}</div>
				</div>

				<div class="stat">
					<div class="stat-title">지연시간</div>
					<div class="stat-value text-lg">{connectionState.latency}ms</div>
				</div>

				<div class="stat">
					<div class="stat-title">연결 시간</div>
					<div class="stat-value text-lg">
						{Math.floor(connectionState.connectionUptime / 1000)}초
					</div>
				</div>
			</div>

			{#if connectionState.lastError}
				<div class="alert alert-error mt-4">
					<span>마지막 오류: {connectionState.lastError}</span>
				</div>
			{/if}

			<div class="card-actions justify-end mt-4">
				<button
					class="btn btn-primary"
					class:loading={isManualReconnecting}
					onclick={handleManualReconnect}
					disabled={isManualReconnecting || connectionState.status === 'connected'}
				>
					{isManualReconnecting ? '재연결 중...' : '수동 재연결'}
				</button>
			</div>
		</div>
	</div>

	<!-- 통계 카드 -->
	<div class="card bg-base-100 shadow-xl mb-6">
		<div class="card-body">
			<h2 class="card-title">연결 통계</h2>

			<div class="grid grid-cols-2 md:grid-cols-4 gap-4">
				<div class="stat">
					<div class="stat-title">총 연결</div>
					<div class="stat-value text-lg">{stats.totalConnections}</div>
				</div>

				<div class="stat">
					<div class="stat-title">총 메시지</div>
					<div class="stat-value text-lg">{stats.totalMessages}</div>
				</div>

				<div class="stat">
					<div class="stat-title">총 오류</div>
					<div class="stat-value text-lg">{stats.totalErrors}</div>
				</div>

				<div class="stat">
					<div class="stat-title">평균 지연시간</div>
					<div class="stat-value text-lg">{stats.averageLatency}ms</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 테스트 기능 -->
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h2 class="card-title">테스트 기능</h2>

			<div class="flex flex-wrap gap-4"></div>
			<button class="btn btn-primary" onclick={testServerConnection}> 서버 연결 테스트 </button>

			<button class="btn btn-secondary" onclick={testDirectSseConnection}>
				직접 SSE 연결 테스트
			</button>

			<button
				class="btn btn-accent"
				onclick={sendTestNotification}
				disabled={connectionState.status !== 'connected'}
			>
				테스트 알림 전송
			</button>

			<button class="btn btn-outline" onclick={() => window.location.reload()}>
				페이지 새로고침
			</button>

			<button
				class="btn btn-error"
				class:loading={isDiagnosticRunning}
				onclick={runDiagnosis}
				disabled={isDiagnosticRunning}
			>
				{isDiagnosticRunning ? '진단 중...' : '429 에러 진단'}
			</button>
		</div>

		<div class="mt-4">
			<h3 class="font-semibold mb-2">연결 정보:</h3>
			<div class="mockup-code text-xs">
				<pre><code>{JSON.stringify(connectionState, null, 2)}</code></pre>
			</div>
		</div>

		<div class="mt-4">
			<h3 class="font-semibold mb-2">환경 설정:</h3>
			<div class="text-sm space-y-1">
				<div><strong>SSE 엔드포인트:</strong> {import.meta.env.VITE_SSE_ENDPOINT}</div>
				<div><strong>API 엔드포인트:</strong> {import.meta.env.VITE_API_ENDPOINT}</div>
				<div><strong>환경:</strong> {import.meta.env.VITE_NODE_ENV}</div>
				<div>
					<strong>인증 방식:</strong> Sanctum 쿠키 기반
				</div>
				<div>
					<strong>인증 상태:</strong>
					{browser && isAuthenticated() ? '인증됨' : '인증 안됨'}
				</div>
				<div>
					<strong>사용자 정보:</strong>
					{browser && sessionStorage.getItem('user') ? '있음' : '없음'}
				</div>
				<div>
					<strong>HTTP-only 쿠키:</strong>
					클라이언트에서 확인 불가 (서버에서 처리)
				</div>
			</div>
		</div>
	</div>

	<!-- 진단 결과 -->
	{#if diagnosticsResult}
		<div class="card bg-base-100 shadow-xl mt-6">
			<div class="card-body">
				<h2 class="card-title">진단 결과</h2>

				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
					<div class="stat">
						<div class="stat-title">API 서버</div>
						<div
							class="stat-value text-lg {diagnosticsResult.server.api
								? 'text-success'
								: 'text-error'}"
						>
							{diagnosticsResult.server.api ? '정상' : '오류'}
						</div>
						<div class="stat-desc">상태: {diagnosticsResult.server.details.apiStatus || 'N/A'}</div>
					</div>

					<div class="stat">
						<div class="stat-title">인증</div>
						<div
							class="stat-value text-lg {diagnosticsResult.server.auth
								? 'text-success'
								: 'text-error'}"
						>
							{diagnosticsResult.server.auth ? '인증됨' : '인증 실패'}
						</div>
						<div class="stat-desc">
							상태: {diagnosticsResult.server.details.authStatus || 'N/A'}
						</div>
					</div>

					<div class="stat">
						<div class="stat-title">CSRF</div>
						<div
							class="stat-value text-lg {diagnosticsResult.server.csrf
								? 'text-success'
								: 'text-error'}"
						>
							{diagnosticsResult.server.csrf ? '정상' : '오류'}
						</div>
						<div class="stat-desc">
							상태: {diagnosticsResult.server.details.csrfStatus || 'N/A'}
						</div>
					</div>

					<div class="stat">
						<div class="stat-title">SSE</div>
						<div
							class="stat-value text-lg {diagnosticsResult.server.sse
								? 'text-success'
								: 'text-error'}"
						>
							{diagnosticsResult.server.sse ? '정상' : '오류'}
						</div>
						<div class="stat-desc">상태: {diagnosticsResult.server.details.sseStatus || 'N/A'}</div>
					</div>
				</div>

				<div class="alert alert-error mb-4">
					<h3 class="font-bold">문제: {diagnosticsResult.issue || '알 수 없음'}</h3>
					<p>해결방법: {diagnosticsResult.solution || '서버 로그 확인 필요'}</p>
				</div>

				<div class="collapse collapse-arrow bg-base-200">
					<input type="checkbox" />
					<div class="collapse-title text-lg font-medium">상세 정보</div>
					<div class="collapse-content">
						<div class="mockup-code text-xs">
							<pre><code>{JSON.stringify(diagnosticsResult, null, 2)}</code></pre>
						</div>
					</div>
				</div>

				<div class="card-actions justify-end mt-4">
					<button class="btn btn-sm btn-outline" onclick={() => (diagnosticsResult = null)}>
						결과 닫기
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>
			</section>
		</div>
	{/snippet}
</AppLayout>