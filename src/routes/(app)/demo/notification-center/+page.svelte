<script lang="ts">
	import NotificationCenter from '$lib/components/NotificationCenter.svelte';
	import { onMount } from 'svelte';

	let showCenter = false;

	onMount(() => {
		showCenter = true;
	});
</script>

<svelte:head>
	<title>알림 센터 - SSE 직원 알림 시스템</title>
</svelte:head>

<div class="container mx-auto p-6">
	<div class="mb-6">
		<h1 class="text-3xl font-bold text-center mb-2">알림 센터</h1>
		<p class="text-center text-base-content/70">실시간 알림을 확인하고 관리할 수 있습니다</p>
	</div>

	{#if showCenter}
		<NotificationCenter />
	{:else}
		<div class="flex justify-center items-center min-h-[400px]">
			<span class="loading loading-spinner loading-lg"></span>
		</div>
	{/if}
</div>

<style>
	.container {
		max-width: 1200px;
	}
</style>
