<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	let user: User = getUser();

	const breadcrumbs: Breadcrumb[] = [
		{ title: '대시보드', url: '/dashboard' },
		{ title: '생산 현황 그래프', url: '/statistics/production-status' },
	];
</script>

<svelte:head>
	<title>대시보드 > 생산 현황 그래프</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full px-3 flex flex-col-reverse xl:flex-row items-center justify-center xl:justify-between">
					
					생산 현황 그래프
				
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>