<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types/types';

	import { formatDateTimeToFullString, formatDateToString, getNumberFormat } from '$lib/Functions';
	import { boardStore, loadBoard } from '$stores/boardStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';

	let user: User = getUser();

	const apiUrl = '/wms/board';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') ?? '2021-12-01');
	let endAt = $state(page.url.searchParams.get('endAt') ?? formatDateToString(new Date()));
	let searchType = $state(page.url.searchParams.get('searchType') ?? 'subject');
	let keyword = $state(page.url.searchParams.get('keyword') ?? ''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');

	let searchParams = $state('');
	let apiSearchParams = '';
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			beginAt: beginAt,
			endAt: endAt,
			searchType: searchType,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadBoard(`${apiUrl}?${apiSearchParams}`, user);

		if ($boardStore) {
			startNo = $boardStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;

		makeData();
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '공지사항', url: '/board/notice' },
		{ title: '공지사항 리스트', url: '/board/notice' }
	];
</script>

<svelte:head>
	<title>공지사항</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<SearchUI>
					<SearchWarehousingDate beginAt={beginAt}
																 endAt={endAt}
																 onUpdate={changeSearchParams}
																 title={`작성 시각`}
					/>
					
					<SearchField>
						<SearchFieldTitle title="검색 항목" />
						<SearchFieldContent>
							<select bind:value={searchType}
											class="select select-sm py-1 px-4 border-none focus:ring-0 focus:outline-none"
							>
								<option value="subject">제목</option>
								<option value="content">내용</option>
								<option value="name">작성자</option>
							</select>
							
							<input bind:value={keyword}
										 class="input input-sm input-bordered w-48 focus:ring-0 focus:outline-none"
										 onkeydown={e => {if (e.key === "Enter") { makeData() }}}
										 type="text">
							
							<SearchButton onclick={makeData} tooltipData="검색" useTooltip={true} />
						</SearchFieldContent>
					</SearchField>
				</SearchUI>
				
				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					{#if user.role === 'Admin' || user.role === 'Super-Admin'}
						<TableTop onUpdate={changeSearchParams} {pageSize} total={$boardStore.pageTotal ?? 0}>
							{#snippet right()}
												<div class="pl-3" >
									<button class="btn btn-error btn-sm"
													onclick={() => goto(`${localUrl}/create?${searchParams}`)}
													type="button"
									>
										<Icon data={faPlus} />
										글쓰기
									</button>
								</div>
											{/snippet}
						</TableTop>
					{:else}
						<TableTop onUpdate={changeSearchParams} {pageSize} total={$boardStore.pageTotal ?? 0} />
					{/if}
					
					<table class="table text-xs table-pin-rows">
						<colgroup>
							<col style="width: 7%">
							<col>
							<col style="width: 10%">
							<col style="width: 8%">
							<col style="width: 15%">
						</colgroup>
						
						<thead class="uppercase">
						<tr class="bg-base-content text-base-300 text-center">
							<th>번호</th>
							<th>제목</th>
							<th>작성자</th>
							<th>조회수</th>
							<th>작성일</th>
						</tr>
						</thead>
						
						<tbody>
						{#if $boardStore.articles}
							{#if $boardStore.pageCurrentPage === 1}
								{#each $boardStore.notices as item}
									<!-- 공지루프 -->
									<tr class="bg-base-300/60 hover:bg-base-content/10">
										<td class="p-1 text-center">
											<button class="btn btn-info btn-sm" type="button">
												중요
											</button>
										</td>
										<td class="p-2 max-w-[400px]">
											<a href="{localUrl}/view?id={item.id}&{searchParams}">
												{item.subject}
											</a>
										</td>
										<td class="p-1">
											{item.name}
										</td>
										<td class="p-1 text-right">
											{getNumberFormat(item.hits)}
										</td>
										<td class="p-1 text-center">
											{formatDateTimeToFullString(item.created_at)}
										</td>
									</tr>
								{/each}
							{/if}
							
							{#each $boardStore.articles as item, index}
								<!--전체 게시물 루프-->
								<tr class="hover:bg-base-content/10">
									<td class="p-1 text-center">
										{getNumberFormat(startNo - index)}
									</td>
									<td class="p-2 max-w-[400px]">
										<a href="{localUrl}/view?id={item.id}&{searchParams}">
											{item.subject}
										</a>
									</td>
									<td class="p-1">
										{item.name}
									</td>
									<td class="p-1 text-right">
										{getNumberFormat(item.hits)}
									</td>
									<td class="p-1 text-center">
										{formatDateTimeToFullString(item.created_at)}
									</td>
								</tr>
							{/each}
						{/if}
						</tbody>
					</table>
				</div>
				
				<!-- Pagination -->
				{#if $boardStore.pageTotal && $boardStore.pageTotal > 0}
					<Paginate
						store={boardStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>