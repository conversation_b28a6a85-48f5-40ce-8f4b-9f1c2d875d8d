<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';

	import type { NotificationMessage } from '$lib/types/sseTypes';
	import {
		getCurrentConnectionState,
		sseConnectionState,
		startSseSystem,
		stopSseSystem
	} from '$lib/services';
	import { registerMessageHandler } from '$lib/services/sseMessageRouter';

	import { Toasts } from 'svoast';

	import { executeAsk, executeMessage, redirectUrl } from '$lib/Functions';
	import { getUser, type User } from '$lib/User';

	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	let user: boolean | User = false;
	const channelRole = ['Super-Admin', 'Admin'];

	// SSE 연결 관련 변수
	let sseEventSource: EventSource | null = null;
	let connectionStatus = $state('disconnected');
	let reconnectAttempts = $state(0);
	let lastError = $state('');

	// SSE 연결 상태 구독
	$effect(() => {
		if (browser) {
			return sseConnectionState.subscribe((state) => {
				connectionStatus = state.status;
				reconnectAttempts = state.reconnectAttempts;
				lastError = state.lastError || '';
			});
		}
	});

	/**
	 * SSE 알림 메시지 핸들러
	 */
	async function handleNotificationMessage(notification: NotificationMessage) {
		try {
			console.log('알림 메시지 수신:', notification);

			// 사용자 역할 기반 필터링
			if (notification.target_type === 'role' && notification.target_ids) {
				const userRole = typeof user === 'object' ? user.role : '';
				if (!notification.target_roles.includes(userRole)) {
					console.log('역할 기반 필터링으로 알림 무시:', userRole);
					return;
				}
			}

			// 메시지 포맷팅
			let msg = notification.message.replace(/\\n/g, '\n').replace(/<br>/g, '\n');

			// 액션 URL이 있는 경우 사용자 확인 후 리다이렉트
			if (notification.action_url) {
				const answer = await executeAsk(msg);
				if (answer) {
					await redirectUrl(notification.action_url);
				}
			} else {
				// 일반 알림 표시 (notification.type을 적절한 타입으로 변환)
				const messageType = ['info', 'warning', 'error'].includes(notification.type)
					? (notification.type as 'info' | 'warning' | 'error')
					: 'info';
				await executeMessage(msg, messageType);
			}
		} catch (error) {
			console.error('알림 메시지 처리 오류:', error);
			await executeMessage('알림 처리 중 오류가 발생했습니다.', 'error');
		}
	}

	/**
	 * SSE 연결 상태 변경 핸들러
	 */
	function handleConnectionStatusChange() {
		const state = getCurrentConnectionState();

		switch (state.status) {
			case 'connected':
				console.log('SSE 연결 성공');
				break;
			case 'connecting':
				console.log('SSE 연결 시도 중...');
				break;
			case 'reconnecting':
				console.log(`SSE 재연결 시도 중... (${state.reconnectAttempts}회)`);
				break;
			case 'error':
				console.error('SSE 연결 오류:', state.lastError);
				break;
			case 'closed':
				console.log('SSE 연결 종료됨');
				break;
		}
	}

	onMount(async () => {
		// 브라우저 환경에서만 실행
		if (!browser) return;

		try {
			// 사용자 정보 가져오기
			user = getUser();

			if (!user) {
				console.error('사용자 정보를 찾을 수 없습니다');
				await goto('/login');
				return;
			}

			// HMR 방지: 이미 SSE 시스템이 초기화되어 있는지 확인
			if (import.meta.env.DEV && (window as any).__SSE_INITIALIZED__) {
				console.log('HMR 감지: 기존 SSE 연결 재사용');

				// 기존 연결 상태 확인
				const currentState = getCurrentConnectionState();
				if (currentState.status === 'connected') {
					console.log('기존 SSE 연결이 활성 상태입니다');

					// 메시지 핸들러만 다시 등록
					registerMessageHandler('notification', handleNotificationMessage);
					registerMessageHandler('heartbeat', (data: any) => {
						console.log('하트비트 수신:', data);
					});

					return;
				}
			}

			console.log('SSE 시스템 초기화 시작...');

			// SSE 시스템 시작 (간단한 버전)
			sseEventSource = await startSseSystem();

			if (!sseEventSource) {
				console.error('SSE 연결 생성 실패');
				await executeMessage('실시간 알림 연결에 실패했습니다.', 'warning');
				return;
			}

			// HMR 방지: 초기화 완료 플래그 설정
			if (import.meta.env.DEV) {
				(window as any).__SSE_INITIALIZED__ = true;
			}

			// SSE 메시지 핸들러 등록
			registerMessageHandler('notification', handleNotificationMessage);
			// data_update는 이미 sseDataHandlers에서 등록되므로 중복 등록하지 않음

			// 하트비트 핸들러 (연결 상태 확인용)
			registerMessageHandler('heartbeat', (data: any) => {
				console.log('하트비트 수신:', data);
			});

			// 연결 상태 변경 이벤트 리스너 (중복 등록 방지)
			if (!(window as any).__SSE_LISTENERS_ADDED__) {
				window.addEventListener('sse-connection-status-change', handleConnectionStatusChange);

				// 수동 재연결 요청 이벤트 리스너
				window.addEventListener('sse-manual-reconnect-required', (event: any) => {
					const detail = event.detail;
					executeAsk(`연결이 끊어졌습니다. 다시 연결하시겠습니까?\n오류: ${detail.lastError}`).then(
						async (answer) => {
							if (answer) {
								const { manualReconnect } = await import('$lib/services/sseConnection');
								try {
									await manualReconnect();
									await executeMessage('연결이 복구되었습니다.', 'info');
								} catch (error) {
									await executeMessage('수동 재연결에 실패했습니다.', 'error');
								}
							}
						}
					);
				});

				(window as any).__SSE_LISTENERS_ADDED__ = true;
			}

			console.log('SSE 시스템 초기화 완료');
		} catch (error) {
			console.error('SSE 시스템 초기화 실패:', error);
			await executeMessage('실시간 알림 시스템 초기화에 실패했습니다.', 'error');

			// 사용자 인증 오류인 경우 로그인 페이지로 이동
			if (error instanceof Error && error.message.includes('인증')) {
				await goto('/login');
			}
		}
	});

	onDestroy(() => {
		if (!browser) return;

		console.log('레이아웃 정리 시작...');

		try {
			// HMR 시 재연결의 번거로움이 있지만, 연결 누수를 막기 위해 항상 정리 로직을 실행합니다.
			if (import.meta.env.DEV) {
				console.log('개발 환경: SSE 연결 유지 (HMR 대응)');
				return;
			}

			// 이벤트 리스너 제거
			window.removeEventListener('sse-connection-status-change', handleConnectionStatusChange);

			// SSE 시스템 종료
			if (sseEventSource) {
				stopSseSystem();
				sseEventSource = null;
				console.log('SSE 연결이 정리되었습니다.');
			}

			// 초기화 플래그 제거
			delete (window as any).__SSE_INITIALIZED__;
			delete (window as any).__SSE_LISTENERS_ADDED__;

			console.log('레이아웃 정리 완료');
		} catch (error) {
			console.error('레이아웃 정리 중 오류:', error);
		}
	});
</script>

<Toasts position="bottom-right" />

<div id="container">
	{@render children?.()}
</div>
