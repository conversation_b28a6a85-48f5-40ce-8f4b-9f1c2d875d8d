<script lang="ts">
	import type { Breadcrumb } from '$lib/types/types';

	import { executeMessage, handleCatch, redirectUrl } from '$lib/Functions';
	import { getUser, removeUser, setUser } from '$lib/User';
	import { authClient } from '$lib/services/AxiosBackend';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import Back from '$components/UI/Back.svelte';
	import ProfileInfoForm from '$components/Profile/ProfileInfoForm.svelte';
	import PasswordChangeForm from '$components/Profile/PasswordChangeForm.svelte';

	const user = getUser();

	// 내 정보 수정용 상태 - user가 있을 때만 초기화
	let profileInfo = $state({
		name: user?.name || '',
		email: user?.email || '',
		cellphone: user?.cellphone || ''
	});

	// 비밀번호 변경용 상태
	let passwordChange = $state({
		currentPassword: '',
		newPassword: '',
		passwordConfirmation: ''
	});

	// 활성 탭 상태
	let activeTab = $state(0);

	// 내 정보 수정 처리
	async function handleProfileInfoSubmit(data: { name: string; email: string; cellphone: string }) {
		if (!user) {
			await executeMessage('사용자 정보를 찾을 수 없습니다.');
			return;
		}
		
		const { name, email, cellphone } = data;

		if (!name) {
			await executeMessage('이름을 입력해 주세요.');
			return;
		}

		if (!email) {
			await executeMessage('메일 주소를 입력해 주세요.');
			return;
		}

		if (!cellphone) {
			await executeMessage('연락처를 입력해 주세요.');
			return;
		}

		try {
			const payload = {
				id: user.id,
				name,
				email,
				cellphone
			};

			const endpoint = `/wms/user/profile`;
			const response = await authClient.put(endpoint, payload);

			if (response.status === 200 && response.data.success) {
				await executeMessage('정보가 수정되었습니다.');
			} else {
				const errors = response.data.data.errors;
				const keys = ['name', 'email', 'cellphone'];

				for (const key of keys) {
					if (errors[key]) {
						await executeMessage(errors[key], 'error');
					}
				}
			}
		} catch (e: any) {
			const errors = e.response.data.errors;
			const keys = ['current_password', 'password'];

			for (const key of keys) {
				if (errors[key]) {
					await executeMessage(errors[key], 'error');
				}
			}
		}
	}

	// 비밀번호 변경 처리
	async function handlePasswordChangeSubmit(data: { currentPassword: string; newPassword: string; passwordConfirmation: string }) {
		const { currentPassword, newPassword, passwordConfirmation } = data;

		if (!user) {
			await executeMessage('사용자 정보를 찾을 수 없습니다.');
			return;
		}

		try {
			const payload = {
				id: user.id,
				current_password: currentPassword,
				password: newPassword,
				password_confirmation: passwordConfirmation
			};

			const endpoint = `/wms/user/password`;
			const response = await authClient.put(endpoint, payload);

			if (response.status === 200 && response.data.success) {
				await executeMessage('비밀번호가 변경되었습니다.');

				// 폼 초기화
				passwordChange = {
					currentPassword: '',
					newPassword: '',
					passwordConfirmation: ''
				};
			} else {
				const errors = response.data.data.errors;
				const keys = ['current_password', 'password'];

				for (const key of keys) {
					if (errors[key]) {
						await executeMessage(errors[key], 'error');
					}
				}
			}
		} catch (e: any) {
			const errors = e.response.data.errors;
			const keys = ['current_password', 'password'];

			for (const key of keys) {
				if (errors[key]) {
					await executeMessage(errors[key], 'error');
				}
			}
		}
	}

	const breadcrumbs: Breadcrumb[] = [
		{ title: '내 정보 수정', url: '/me/edit/#' },
	];
</script>

<svelte:head>
	<title>내 정보 수정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="p-4 md:px-6 2xl:px-0 flex justify-start items-center 2xl:container">
					<div class="w-full 2xl:w-1/2 flex flex-col justify-start items-start space-y-9">
						<div class="w-full 2xl:mx-auto flex flex-col xl:flex-row justify-center space-y-6 xl:space-y-0 xl:space-x-6">
							{#if user}
								<div class="w-full">
									<!-- 탭 헤더 -->
									<div class="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-2xl">
										<button
											class="flex-1 py-3 px-6 rounded-xl font-medium text-sm transition-all duration-200 {activeTab === 0 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-green-600 hover:bg-white/50'}"
											onclick={() => activeTab = 0}
											type="button"
										>
											<div class="flex items-center justify-center space-x-2">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
												</svg>
												<span>내 정보 수정</span>
											</div>
										</button>
										<button
											class="flex-1 py-3 px-6 rounded-xl font-medium text-sm transition-all duration-200 {activeTab === 1 ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-blue-600 hover:bg-white/50'}"
											onclick={() => activeTab = 1}
											type="button"
										>
											<div class="flex items-center justify-center space-x-2">
												<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
													<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
												</svg>
												<span>비밀번호 변경</span>
											</div>
										</button>
									</div>

									<!-- 탭 내용 -->
									<div>
										{#if activeTab === 0}
											<div class="tab-panel">
												<ProfileInfoForm 
													{user} 
													{profileInfo} 
													onSubmit={handleProfileInfoSubmit} 
												/>
											</div>
										{:else if activeTab === 1}
											<div class="tab-panel">
												<PasswordChangeForm 
													{passwordChange} 
													onSubmit={handlePasswordChangeSubmit} 
												/>
											</div>
										{/if}
									</div>
								</div>
							{:else}
								<div class="w-full p-8 text-center bg-white rounded-xl shadow-sm border border-gray-200">
									<p>사용자 정보를 불러오는 중...</p>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
