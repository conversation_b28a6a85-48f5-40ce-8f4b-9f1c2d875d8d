<script lang="ts">
	import { onMount } from 'svelte';
	import restorePrintSettings from '$lib/services/printSettingRestore';

	let backupJson = '';
	let isRestoring = false;
	let restoreStatus = '';

	// 기본 백업 데이터 (당신이 제공한 데이터)
	const defaultBackupData = {
		'기본(default)': [
			{ name: '폰트(글꼴)', code: 'font', settings: { fontFamily: 'Roboto' } },
			{ name: '바코드', code: 'barcode', settings: { x: 0, y: 3 } },
			{ name: 'QAID', code: 'qaid', settings: { fontSize: 10 } },
			{
				name: '출력일 형식',
				code: 'date',
				settings: {
					x: 71,
					y: 67,
					format: 'MM/DD/YY',
					fontSize: 6,
					textColor: '#000000',
					bold: true,
					italics: true
				}
			},
			{
				name: '번호',
				code: 'user',
				settings: { x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true }
			}
		],
		빅솔론: [
			{ name: '폰트(글꼴)', code: 'font', settings: { fontFamily: 'Roboto' } },
			{ name: '바코드', code: 'barcode', settings: { x: 0, y: 3 } },
			{ name: 'QAID', code: 'qaid', settings: { fontSize: 20 } },
			{
				name: '출력일 형식',
				code: 'date',
				settings: {
					x: 71,
					y: 67,
					format: 'MM/DD/YY',
					fontSize: 6,
					textColor: '#000000',
					bold: true,
					italics: true
				}
			},
			{
				name: '번호',
				code: 'user',
				settings: { x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true }
			}
		]
	};

	onMount(() => {
		backupJson = JSON.stringify(defaultBackupData, null, 2);
	});

	async function handleRestore() {
		if (!backupJson.trim()) {
			alert('백업 데이터를 입력해주세요.');
			return;
		}

		try {
			isRestoring = true;
			restoreStatus = '복구 중...';

			const backupData = JSON.parse(backupJson);
			await restorePrintSettings(backupData);

			restoreStatus = '✅ 복구 완료!';
			setTimeout(() => {
				restoreStatus = '';
			}, 3000);
		} catch (error) {
			console.error('복구 실패:', error);
			restoreStatus = `❌ 복구 실패: ${error}`;
		} finally {
			isRestoring = false;
		}
	}

	function loadDefaultData() {
		backupJson = JSON.stringify(defaultBackupData, null, 2);
	}
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h1 class="card-title text-2xl mb-4">🔧 PrintSetting 데이터 복구</h1>

			<div class="alert alert-warning mb-4">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="stroke-current shrink-0 h-6 w-6"
					fill="none"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
					/>
				</svg>
				<span
					>삭제된 PrintSetting 데이터를 복구합니다. 백업 JSON 데이터를 아래에 붙여넣어 주세요.</span
				>
			</div>

			<div class="form-control mb-4">
				<label class="label" for="backup-json">
					<span class="label-text">백업 JSON 데이터</span>
				</label>
				<textarea
					id="backup-json"
					class="textarea textarea-bordered h-64 font-mono text-sm"
					bind:value={backupJson}
					placeholder="백업 JSON 데이터를 여기에 붙여넣어 주세요..."
				></textarea>
			</div>

			<div class="flex gap-2 mb-4">
				<button class="btn btn-secondary" on:click={loadDefaultData}> 기본 데이터 로드 </button>

				<button
					class="btn btn-primary"
					class:loading={isRestoring}
					disabled={isRestoring || !backupJson.trim()}
					on:click={handleRestore}
				>
					{isRestoring ? '복구 중...' : '데이터 복구 실행'}
				</button>
			</div>

			{#if restoreStatus}
				<div
					class="alert"
					class:alert-success={restoreStatus.includes('✅')}
					class:alert-error={restoreStatus.includes('❌')}
				>
					<span>{restoreStatus}</span>
				</div>
			{/if}

			<div class="divider">미리보기</div>

			{#if backupJson}
				<div class="mockup-code">
					<pre><code>{backupJson}</code></pre>
				</div>
			{/if}
		</div>
	</div>
</div>
