export interface SettingItem {
	name: string;
	code: string;
	settings: Record<string, any>;
}

export class PrinterDatabase {
	private db: IDBDatabase | null = null;
	private readonly DB_NAME = 'EmployeeNotificationDB';
	private readonly STORE_NAME = 'print_settings';

	// 기본 설정값 정의
	private readonly DEFAULT_SETTINGS: SettingItem[] = [
		{
			name: '폰트(글꼴)',
			code: 'font',
			settings: { fontFamily: 'Roboto' }
		},
		{
			name: '바코드',
			code: 'barcode',
			settings: {
				x: 0, // 바코드 이미지의 시작위치
				y: 3 // 바코드 이미지의 시작위치(높이)
			}
		},
		{
			name: 'QAID',
			code: 'qaid',
			settings: { fontSize: 10 }
		},
		{
			name: '출력일 형식',
			code: 'date',
			settings: {
				x: 71,
				y: 67,
				format: 'MM/DD/YY',
				fontSize: 6,
				textColor: '#000000',
				bold: true,
				italics: true
			}
		},
		{
			name: '번호',
			code: 'user',
			settings: { x: 120, y: 67, fontSize: 6, textColor: '#000000', bold: true, italics: true }
		}
	];

	async initDatabase(): Promise<void> {
		try {
			// 새로운 통합 DB 사용
			const { initializeWithMigration } = await import('$lib/services/indexedDBManager');
			this.db = await initializeWithMigration();
			console.log('PrinterDatabase: 통합 DB 연결 완료');
		} catch (error) {
			console.error('PrinterDatabase 초기화 실패:', error);
			throw error;
		}
	}

	async saveSettings(settingName: string, settings: SettingItem[]): Promise<void> {
		try {
			if (!this.db) throw new Error('데이터베이스가 초기화되지 않았습니다.');
			if (!settingName.trim()) throw new Error('설정 이름은 필수입니다.');

			const { putData } = await import('$lib/services/indexedDBManager');

			const printSetting = {
				settingName,
				settings,
				updatedAt: new Date().toISOString(),
				migrated: false
			};

			await putData(this.db, this.STORE_NAME, printSetting);
		} catch (error) {
			console.error('설정 저장 실패:', error);
			throw error;
		}
	}

	async getSettings(settingName: string): Promise<SettingItem[]> {
		try {
			if (!this.db) throw new Error('데이터베이스가 초기화되지 않았습니다.');

			const { getData } = await import('$lib/services/indexedDBManager');
			const result = await getData(this.db, this.STORE_NAME, settingName);

			return result?.settings || this.DEFAULT_SETTINGS;
		} catch (error) {
			console.error('설정 조회 실패:', error);
			return this.DEFAULT_SETTINGS;
		}
	}

	async getAllSettings(): Promise<string[]> {
		try {
			if (!this.db) throw new Error('데이터베이스가 초기화되지 않았습니다.');

			const { getAllData } = await import('$lib/services/indexedDBManager');
			const allData = await getAllData(this.db, this.STORE_NAME);

			return allData.map((item) => item.settingName);
		} catch (error) {
			console.error('전체 설정 목록 조회 실패:', error);
			return [];
		}
	}

	async deleteSettings(settingName: string): Promise<void> {
		try {
			if (!this.db) throw new Error('데이터베이스가 초기화되지 않았습니다.');
			if (!settingName.trim()) throw new Error('설정 이름은 필수입니다.');

			// "기본(default)" 설정은 삭제할 수 없도록 보호
			if (settingName === '기본(default)') {
				throw new Error('기본 설정은 삭제할 수 없습니다.');
			}

			const { deleteData } = await import('$lib/services/indexedDBManager');
			await deleteData(this.db, this.STORE_NAME, settingName);
		} catch (error) {
			console.error('설정 삭제 실패:', error);
			throw error;
		}
	}

	/**
	 * 기본 설정이 없으면 생성
	 */
	async ensureDefaultSettings(): Promise<void> {
		try {
			const allSettings = await this.getAllSettings();

			if (!allSettings.includes('기본(default)')) {
				await this.saveSettings('기본(default)', this.DEFAULT_SETTINGS);
				console.log('기본 설정 생성 완료');
			}
		} catch (error) {
			console.error('기본 설정 생성 실패:', error);
		}
	}
}
