export interface ProcessItem {
	code: string;
	name: string;
}

export interface ProcessElement {
	id: string,
	data: ProcessItem[];
}

const DB_NAME = 'myDatabase';
const DB_VERSION = 1;

interface DBStore {
	name: string;
	keyPath: string;
	indexes?: { name: string; keyPath: string; options?: IDBIndexParameters }[];
}

let dbPromise: Promise<IDBDatabase>;

export function initDB(storeName: string, keyPath: string = 'id'): Promise<IDBDatabase> {
	const store: DBStore = { name: storeName, keyPath };

	if (!dbPromise) {
		dbPromise = new Promise((resolve, reject) => {
			const request = indexedDB.open(DB_NAME, DB_VERSION);

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(store.name)) {
					const objectStore = db.createObjectStore(store.name, { keyPath: store.keyPath });

					// 인덱스가 필요한 경우 여기에 추가
					if (store.indexes) {
						store.indexes.forEach(index => {
							objectStore.createIndex(index.name, index.keyPath, index.options);
						});
					}
				}
			};

			request.onsuccess = () => resolve(request.result);
			request.onerror = () => reject(request.error);
		});
	}
	return dbPromise;
}

export async function saveData(storeName: string, data: any[]) {
	const db = await initDB(storeName);
	return new Promise<void>((resolve, reject) => {
		const transaction = db.transaction(storeName, 'readwrite');
		const store = transaction.objectStore(storeName);

		// Clear existing data
		const clearRequest = store.clear();
		clearRequest.onsuccess = () => {
			// Add new data
			Object.entries(data).forEach(([key, value]) => {
				store.add({ id: key, data: value });
			});
		};

		transaction.oncomplete = () => resolve();
		transaction.onerror = () => reject(transaction.error);
	});
}

export async function getData(storeName: string, key: string): Promise<ProcessElement> {
	const db = await initDB(storeName);
	return new Promise((resolve, reject) => {
		const transaction = db.transaction(storeName, 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.get(key);

		request.onsuccess = () => resolve(request.result);
		request.onerror = () => reject(request.error);
	});
}

export async function getAllData(storeName: string) {
	const db = await initDB(storeName);
	return new Promise((resolve, reject) => {
		const transaction = db.transaction(storeName, 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.getAll();

		request.onsuccess = () => resolve(request.result);
		request.onerror = () => reject(request.error);
	});
}