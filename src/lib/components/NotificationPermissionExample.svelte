<!--
  알림 권한 관리 사용 예제 컴포넌트
  
  다양한 시나리오에서 알림 권한 관리 시스템을 사용하는 방법을 보여줍니다.
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import NotificationPermissionManager from './NotificationPermissionManager.svelte';
	import {
		initializeNotificationSystem,
		requestPermissionWithGuidance,
		getNotificationSystemStatus,
		diagnoseNotificationSystem,
		type NotificationSystemStatus,
		type NotificationDiagnostic
	} from '$lib/services/notificationInitializer';
	import { processNotificationWithPermission } from '$lib/services/notifications/processor';

	// 상태 변수
	let systemStatus: NotificationSystemStatus;
	let diagnostic: NotificationDiagnostic;
	let initializationComplete = false;

	// 시스템 초기화
	onMount(async () => {
		try {
			await initializeNotificationSystem({
				autoRequestPermission: false, // 수동으로 제어
				enableMonitoring: true,
				onInitialized: (status) => {
					console.log('알림 시스템 초기화됨:', status);
					updateSystemInfo();
					initializationComplete = true;
				},
				onPermissionChanged: (status) => {
					console.log('권한 상태 변경됨:', status);
					updateSystemInfo();
				}
			});
		} catch (error) {
			console.error('초기화 실패:', error);
			initializationComplete = true;
		}
	});

	// 시스템 정보 업데이트
	function updateSystemInfo() {
		systemStatus = getNotificationSystemStatus();
		diagnostic = diagnoseNotificationSystem();
	}

	// 안내와 함께 권한 요청
	async function handleGuidedPermissionRequest() {
		const result = await requestPermissionWithGuidance(
			'실시간 업무 알림을 받으려면 브라우저 알림을 허용해주세요.\n' +
				'중요한 공지사항이나 긴급 상황을 놓치지 않을 수 있습니다.'
		);

		updateSystemInfo();

		if (result === 'granted') {
			alert('알림 권한이 허용되었습니다! 이제 중요한 알림을 받을 수 있습니다.');
		}
	}

	// 테스트 알림 전송
	function sendTestNotification(priority: string) {
		const testNotification = {
			id: Date.now(),
			title: `테스트 알림 (${priority})`,
			message: `이것은 ${priority} 우선순위 테스트 알림입니다.`,
			priority,
			action_url: null,
			received_at: new Date().toISOString()
		};

		processNotificationWithPermission(testNotification, {
			enableBrowserNotifications: true,
			enableSounds: true,
			enableVibration: true
		});
	}

	// 시스템 진단 실행
	function runDiagnostic() {
		updateSystemInfo();

		const report = [
			'=== 알림 시스템 진단 결과 ===',
			`심각도: ${diagnostic.severity}`,
			'',
			'발견된 문제:',
			...diagnostic.issues.map((issue) => `- ${issue}`),
			'',
			'권장 해결방안:',
			...diagnostic.recommendations.map((rec) => `- ${rec}`)
		].join('\n');

		alert(report);
	}
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<h1 class="text-3xl font-bold mb-6">알림 권한 관리 시스템</h1>

	{#if !initializationComplete}
		<div class="flex items-center gap-3 mb-6">
			<span class="loading loading-spinner loading-md"></span>
			<span>알림 시스템 초기화 중...</span>
		</div>
	{:else}
		<!-- 권한 관리 컴포넌트 -->
		<div class="mb-8">
			<h2 class="text-xl font-semibold mb-4">현재 알림 상태</h2>
			<NotificationPermissionManager showCard={true} autoRequest={false} showTestButton={true} />
		</div>

		<!-- 시스템 상태 정보 -->
		{#if systemStatus}
			<div class="mb-8">
				<h2 class="text-xl font-semibold mb-4">시스템 정보</h2>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div class="stats shadow">
						<div class="stat">
							<div class="stat-title">브라우저 지원</div>
							<div class="stat-value text-sm">
								{systemStatus.isSupported ? '지원됨' : '지원 안됨'}
							</div>
						</div>
					</div>

					<div class="stats shadow">
						<div class="stat">
							<div class="stat-title">권한 상태</div>
							<div class="stat-value text-sm">
								{#if systemStatus.permission === 'granted'}
									<span class="text-success">허용됨</span>
								{:else if systemStatus.permission === 'denied'}
									<span class="text-error">거부됨</span>
								{:else}
									<span class="text-warning">미설정</span>
								{/if}
							</div>
						</div>
					</div>

					<div class="stats shadow">
						<div class="stat">
							<div class="stat-title">사용 가능</div>
							<div class="stat-value text-sm">
								{systemStatus.isAvailable ? '예' : '아니오'}
							</div>
						</div>
					</div>

					<div class="stats shadow">
						<div class="stat">
							<div class="stat-title">대체 모드</div>
							<div class="stat-value text-sm">
								{systemStatus.recommendAlternative ? '활성' : '비활성'}
							</div>
						</div>
					</div>
				</div>
			</div>
		{/if}

		<!-- 액션 버튼들 -->
		<div class="mb-8">
			<h2 class="text-xl font-semibold mb-4">테스트 및 관리</h2>
			<div class="flex flex-wrap gap-3">
				<button class="btn btn-primary" on:click={handleGuidedPermissionRequest}>
					<i class="fas fa-hand-paper mr-2"></i>
					안내와 함께 권한 요청
				</button>

				<button class="btn btn-info" on:click={runDiagnostic}>
					<i class="fas fa-stethoscope mr-2"></i>
					시스템 진단
				</button>

				<button class="btn btn-ghost" on:click={updateSystemInfo}>
					<i class="fas fa-sync mr-2"></i>
					상태 새로고침
				</button>
			</div>
		</div>

		<!-- 테스트 알림 -->
		<div class="mb-8">
			<h2 class="text-xl font-semibold mb-4">테스트 알림</h2>
			<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
				<button class="btn btn-outline btn-sm" on:click={() => sendTestNotification('low')}>
					낮음 우선순위
				</button>

				<button class="btn btn-outline btn-sm" on:click={() => sendTestNotification('normal')}>
					보통 우선순위
				</button>

				<button class="btn btn-outline btn-sm" on:click={() => sendTestNotification('high')}>
					높음 우선순위
				</button>

				<button class="btn btn-outline btn-sm" on:click={() => sendTestNotification('urgent')}>
					긴급 우선순위
				</button>
			</div>
		</div>

		<!-- 진단 결과 -->
		{#if diagnostic}
			<div class="mb-8">
				<h2 class="text-xl font-semibold mb-4">진단 결과</h2>
				<div class="alert alert-info">
					<i class="fas fa-info-circle"></i>
					<div>
						<div class="font-semibold">
							심각도:
							<span
								class="badge badge-{diagnostic.severity === 'high'
									? 'error'
									: diagnostic.severity === 'medium'
										? 'warning'
										: 'success'}"
							>
								{diagnostic.severity}
							</span>
						</div>

						{#if diagnostic.issues.length > 0}
							<div class="mt-2">
								<strong>문제점:</strong>
								<ul class="list-disc list-inside ml-4">
									{#each diagnostic.issues as issue}
										<li class="text-sm">{issue}</li>
									{/each}
								</ul>
							</div>
						{/if}

						{#if diagnostic.recommendations.length > 0}
							<div class="mt-2">
								<strong>권장사항:</strong>
								<ul class="list-disc list-inside ml-4">
									{#each diagnostic.recommendations as recommendation}
										<li class="text-sm">{recommendation}</li>
									{/each}
								</ul>
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	{/if}
</div>
