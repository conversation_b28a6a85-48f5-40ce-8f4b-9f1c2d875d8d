<script lang="ts">
	import { onMount } from 'svelte';
	import { allNotificationsStore } from '$lib/stores';
	import { markNotificationAsRead } from '$lib/services/notificationHandler';
	import { getAllData } from '$lib/services/indexedDBManager';
	import Icon from 'svelte-awesome';
	import { bell, check, trash, externalLink, chevronLeft, chevronRight } from 'svelte-awesome/icons';

	// 알림 데이터 및 상태 (Svelte 5 Runes)
	let currentPage = $state(1);
	let itemsPerPage = $state(10);
	let selectedFilter = $state('all'); // all, unread, read
	let selectedPriority = $state('all'); // all, low, normal, high, urgent
	let isLoading = $state(false);
	let db = $state<IDBDatabase | null>(null);

	// 파생 상태 (Derived State)
	const filteredNotifications = $derived(
		($allNotificationsStore || []).filter((notification) => {
			// 읽음 상태 필터
			if (selectedFilter === 'unread' && notification.read) return false;
			if (selectedFilter === 'read' && !notification.read) return false;

			// 우선순위 필터
			return !(selectedPriority !== 'all' && notification.priority !== selectedPriority);
		})
	);

	const totalPages = $derived(Math.ceil(filteredNotifications.length / itemsPerPage));

	const paginatedNotifications = $derived(
		filteredNotifications.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
	);

	// 부수 효과 (Side Effects)
	$effect(() => {
		if (currentPage > totalPages && totalPages > 0) {
			currentPage = totalPages;
		}
	});

	onMount(async () => {
		await initializeDatabase();
		await loadNotifications();
	});

	/**
	 * 데이터베이스 초기화
	 */
	async function initializeDatabase(): Promise<void> {
		try {
			db = await new Promise<IDBDatabase>((resolve, reject) => {
				const request = indexedDB.open('EmployeeNotificationDB', 1);
				request.onerror = () => reject(request.error);
				request.onsuccess = () => resolve(request.result);
			});
		} catch (error) {
			console.error('데이터베이스 초기화 실패:', error);
		}
	}

	/**
	 * 알림 목록 로드
	 */
	async function loadNotifications(): Promise<void> {
		if (!db) return;

		isLoading = true;
		try {
			const allNotifications = await getAllData(db, 'notifications');
			allNotificationsStore.set(
				allNotifications.sort(
					(a, b) => new Date(b.received_at).getTime() - new Date(a.received_at).getTime()
				)
			);
		} catch (error) {
			console.error('알림 로드 실패:', error);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 알림 읽음 처리
	 */
	async function handleMarkAsRead(event: Event, notificationId: number): Promise<void> {
		event.stopPropagation();
		
		if (!db) return;

		try {
			await markNotificationAsRead(notificationId);
			await loadNotifications();
		} catch (error) {
			console.error('알림 읽음 처리 실패:', error);
		}
	}

	/**
	 * 알림 삭제
	 */
	async function handleDeleteNotification(event: Event, notificationId: number): Promise<void> {
		event.stopPropagation();
		if (!db) return;

		try {
			const transaction = db.transaction(['notifications'], 'readwrite');
			const store = transaction.objectStore('notifications');
			await store.delete(notificationId);
			await loadNotifications();
		} catch (error) {
			console.error('알림 삭제 실패:', error);
		}
	}

	/**
	 * 모든 알림 읽음 처리
	 */
	async function handleMarkAllAsRead(): Promise<void> {
		if (!db) return;

		try {
			const unreadNotifications = ($allNotificationsStore || []).filter((n) => !n.read);
			for (const notification of unreadNotifications) {
				await markNotificationAsRead(notification.id);
			}
			await loadNotifications();
		} catch (error) {
			console.error('모든 알림 읽음 처리 실패:', error);
		}
	}

	/**
	 * 알림 클릭 처리
	 */
	function handleNotificationClick(event: Event, notification: any): void {
		// 읽음 처리
		if (!notification.read) {
			handleMarkAsRead(event, notification.id);
		}

		// 액션 URL이 있으면 새 창에서 열기
		if (notification.action_url) {
			window.open(notification.action_url, '_blank');
		}
	}

	/**
	 * 우선순위별 스타일 클래스
	 */
	function getPriorityClass(priority: string): string {
		switch (priority) {
			case 'urgent':
				return 'border-l-4 border-l-error bg-error/10';
			case 'high':
				return 'border-l-4 border-l-warning bg-warning/10';
			case 'normal':
				return 'border-l-4 border-l-info bg-info/10';
			case 'low':
				return 'border-l-4 border-l-success bg-success/10';
			default:
				return 'border-l-4 border-l-base-300';
		}
	}

	/**
	 * 우선순위 텍스트
	 */
	function getPriorityText(priority: string): string {
		switch (priority) {
			case 'urgent':
				return '긴급';
			case 'high':
				return '높음';
			case 'normal':
				return '보통';
			case 'low':
				return '낮음';
			default:
				return '알 수 없음';
		}
	}

	/**
	 * 시간 포맷팅
	 */
	function formatTime(dateString: string): string {
		const date = new Date(dateString);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMins = Math.floor(diffMs / (1000 * 60));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

		if (diffMins < 1) return '방금 전';
		if (diffMins < 60) return `${diffMins}분 전`;
		if (diffHours < 24) return `${diffHours}시간 전`;
		if (diffDays < 7) return `${diffDays}일 전`;

		return date.toLocaleDateString('ko-KR', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	/**
	 * 페이지 변경
	 */
	function changePage(page: number): void {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}
</script>

<div class="notification-center bg-base-100 rounded-lg shadow-lg max-w-4xl mx-auto">
	<!-- 헤더 -->
	<div class="p-6 border-b border-base-200">
		<div class="flex items-center justify-between mb-4">
			<h2 class="text-2xl font-bold flex items-center gap-2">
				<Icon data={bell} class="text-primary" />
				알림 센터
			</h2>
			<div class="flex gap-2">
				<button
					class="btn btn-sm btn-outline"
					onclick={handleMarkAllAsRead}
					disabled={!($allNotificationsStore || []).some((n) => !n.read)}
				>
					<Icon data={check} class="w-4 h-4" />
					모두 읽음
				</button>
				<button class="btn btn-sm btn-primary" onclick={loadNotifications}> 새로고침 </button>
			</div>
		</div>

		<!-- 필터 -->
		<div class="flex flex-wrap gap-4">
			<!-- 읽음 상태 필터 -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">상태</span>
				</label>
				<select class="select select-bordered select-sm" bind:value={selectedFilter}>
					<option value="all">전체</option>
					<option value="unread">읽지 않음</option>
					<option value="read">읽음</option>
				</select>
			</div>

			<!-- 우선순위 필터 -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">우선순위</span>
				</label>
				<select class="select select-bordered select-sm" bind:value={selectedPriority}>
					<option value="all">전체</option>
					<option value="urgent">긴급</option>
					<option value="high">높음</option>
					<option value="normal">보통</option>
					<option value="low">낮음</option>
				</select>
			</div>

			<!-- 통계 정보 -->
			<div class="flex items-end gap-4 ml-auto">
				<div class="stats stats-horizontal shadow">
					<div class="stat">
						<div class="stat-title">전체</div>
						<div class="stat-value text-sm">{$allNotificationsStore?.length || 0}</div>
					</div>
					<div class="stat">
						<div class="stat-title">읽지 않음</div>
						<div class="stat-value text-sm text-warning">
							{($allNotificationsStore || []).filter((n) => !n.read).length}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 알림 목록 -->
	<div class="p-6">
		{#if isLoading}
			<div class="flex justify-center items-center py-12">
				<span class="loading loading-spinner loading-lg"></span>
			</div>
		{:else if paginatedNotifications.length === 0}
			<div class="text-center py-12">
				<Icon data={bell} class="w-16 h-16 text-base-300 mx-auto mb-4" />
				<p class="text-base-content/60">
					{selectedFilter === 'all' ? '알림이 없습니다.' : '해당 조건의 알림이 없습니다.'}
				</p>
			</div>
		{:else}
			<div class="space-y-3">
				{#each paginatedNotifications as notification (notification.id)}
					<div
						class="card {getPriorityClass(notification.priority)} {notification.read
							? 'opacity-60'
							: ''} hover:shadow-md transition-all cursor-pointer"
						onclick={(e) => handleNotificationClick(e, notification)}
						onkeydown={(e) => e.key === 'Enter' && handleNotificationClick(e, notification)}
						role="button"
						tabindex="0"
					>
						<div class="card-body p-4">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<!-- 제목 및 우선순위 -->
									<div class="flex items-center gap-2 mb-2">
										<h3 class="font-semibold {notification.read ? '' : 'text-primary'}">
											{notification.title}
										</h3>
										<div class="badge badge-sm badge-outline">
											{getPriorityText(notification.priority)}
										</div>
										{#if !notification.read}
											<div class="badge badge-primary badge-sm">새 알림</div>
										{/if}
									</div>

									<!-- 메시지 -->
									<p class="text-base-content/80 mb-2">
										{notification.message}
									</p>

									<!-- 시간 및 액션 -->
									<div class="flex items-center justify-between">
										<span class="text-sm text-base-content/60">
											{formatTime(notification.received_at)}
										</span>
										{#if notification.action_url}
											<div class="flex items-center gap-1 text-sm text-primary">
												<Icon data={externalLink} class="w-3 h-3" />
												링크 열기
											</div>
										{/if}
									</div>
								</div>

								<!-- 액션 버튼 -->
								<div class="flex gap-1 ml-4">
									{#if !notification.read}
										<button
											class="btn btn-ghost btn-sm"
											onclick={(event) => handleMarkAsRead(event, notification.id)}
											title="읽음 처리"
										>
											<Icon data={check} class="w-4 h-4" />
										</button>
									{/if}
									<button
										class="btn btn-ghost btn-sm text-error"
										onclick={(event) => handleDeleteNotification(event, notification.id)}
										title="삭제"
									>
										<Icon data={trash} class="w-4 h-4" />
									</button>
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<!-- 페이지네이션 -->
	{#if totalPages > 1}
		<div class="p-6 border-t border-base-200">
			<div class="flex justify-center">
				<div class="join">
					<button
						class="join-item btn btn-sm"
						disabled={currentPage === 1}
						onclick={() => changePage(currentPage - 1)}
					>
						<Icon data={chevronLeft} class="w-4 h-4" />
					</button>

					{#each Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
						const startPage = Math.max(1, currentPage - 2);
						const endPage = Math.min(totalPages, startPage + 4);
						return startPage + i;
					}).filter((page) => page <= totalPages) as page}
						<button
							class="join-item btn btn-sm {currentPage === page ? 'btn-active' : ''}"
							onclick={() => changePage(page)}
						>
							{page}
						</button>
					{/each}

					{#if totalPages > 5 && currentPage < totalPages - 2}
						<button class="join-item btn btn-sm btn-disabled">...</button>
						<button class="join-item btn btn-sm" onclick={() => changePage(totalPages)}>
							{totalPages}
						</button>
					{/if}

					<button
						class="join-item btn btn-sm"
						disabled={currentPage === totalPages}
						onclick={() => changePage(currentPage + 1)}
					>
						<Icon data={chevronRight} class="w-4 h-4" />
					</button>
				</div>
			</div>

			<div class="text-center mt-2 text-sm text-base-content/60">
				{filteredNotifications.length}개 중 {(currentPage - 1) * itemsPerPage + 1}-{Math.min(
					currentPage * itemsPerPage,
					filteredNotifications.length
				)}개 표시
			</div>
		</div>
	{/if}
</div>

<style>
	.notification-center {
		max-height: 80vh;
		overflow-y: auto;
	}

	.card {
		transition: all 0.2s ease-in-out;
	}

	.card:hover {
		transform: translateY(-1px);
	}
</style>