<script lang="ts">
	/**
	 * 실시간 카테고리 목록 컴포넌트 데모
	 *
	 * 컴포넌트 테스트 및 사용법 예시를 제공합니다.
	 */

	import { onMount } from 'svelte';
	import RealtimeCategoryList from './RealtimeCategoryList.svelte';
	import { categoryStore } from '$lib/stores/dataStores';
	import {
		highlightedItemsStore,
		updatedItemsStore,
		updateMessageStore
	} from '$lib/stores/uiStores';

	// 데모 상태
	let searchQuery = '';
	let categoryType: 'cate4' | 'cate5' = 'cate4';
	let containerHeight = 400;
	let showAnimations = true;

	// 데모 데이터
	const demoCategories = {
		cate4: [
			{
				id: 1,
				name: '전자제품',
				code: 'ELEC001',
				description: '전자제품 카테고리',
				status: 'active'
			},
			{
				id: 2,
				name: '가전제품',
				code: 'APPL001',
				description: '가전제품 카테고리',
				status: 'active'
			},
			{ id: 3, name: '컴퓨터', code: 'COMP001', description: '컴퓨터 관련 제품', status: 'active' },
			{
				id: 4,
				name: '스마트폰',
				code: 'PHON001',
				description: '스마트폰 및 액세서리',
				status: 'inactive'
			},
			{ id: 5, name: '태블릿', code: 'TABL001', description: '태블릿 PC', status: 'active' }
		],
		cate5: [
			{ id: 11, name: '노트북', code: 'LAPT001', description: '노트북 컴퓨터', status: 'active' },
			{
				id: 12,
				name: '데스크톱',
				code: 'DESK001',
				description: '데스크톱 컴퓨터',
				status: 'active'
			},
			{ id: 13, name: '모니터', code: 'MONI001', description: '컴퓨터 모니터', status: 'active' },
			{ id: 14, name: '키보드', code: 'KEYB001', description: '컴퓨터 키보드', status: 'active' },
			{ id: 15, name: '마우스', code: 'MOUS001', description: '컴퓨터 마우스', status: 'inactive' }
		]
	};

	/**
	 * 컴포넌트 마운트 시 데모 데이터 설정
	 */
	onMount(() => {
		// 데모 데이터로 스토어 초기화
		categoryStore.set({
			cate4: demoCategories.cate4,
			cate5: demoCategories.cate5,
			lastUpdated: new Date().toISOString(),
			totalCount: demoCategories.cate4.length + demoCategories.cate5.length
		});
	});

	/**
	 * 항목 클릭 핸들러
	 */
	function handleItemClick(item: any) {
		console.log('항목 클릭:', item);

		// 업데이트 메시지 표시
		updateMessageStore.update((messages) => [
			{
				id: crypto.randomUUID(),
				message: `"${item.name}" 항목을 선택했습니다.`,
				type: 'info',
				timestamp: new Date().toISOString()
			},
			...messages.slice(0, 4) // 최대 5개 유지
		]);
	}

	/**
	 * 항목 더블클릭 핸들러
	 */
	function handleItemDoubleClick(item: any) {
		console.log('항목 더블클릭:', item);

		// 업데이트 메시지 표시
		updateMessageStore.update((messages) => [
			{
				id: crypto.randomUUID(),
				message: `"${item.name}" 항목을 편집 모드로 열었습니다.`,
				type: 'success',
				timestamp: new Date().toISOString()
			},
			...messages.slice(0, 4)
		]);
	}

	/**
	 * 새 항목 추가 시뮬레이션
	 */
	function simulateNewItem() {
		const newItem = {
			id: Date.now(),
			name: `새 카테고리 ${Date.now()}`,
			code: `NEW${Date.now()}`,
			description: '새로 추가된 카테고리',
			status: 'active'
		};

		categoryStore.update((data) => {
			const updated = { ...data };
			if (categoryType === 'cate4') {
				updated.cate4 = [...updated.cate4, newItem];
			} else {
				updated.cate5 = [...updated.cate5, newItem];
			}
			updated.totalCount = updated.cate4.length + updated.cate5.length;
			updated.lastUpdated = new Date().toISOString();
			return updated;
		});

		// 하이라이트 효과
		highlightedItemsStore.update((items) => new Set([...items, newItem.id]));

		// 3초 후 하이라이트 제거
		setTimeout(() => {
			highlightedItemsStore.update((items) => {
				const updated = new Set(items);
				updated.delete(newItem.id);
				return updated;
			});
		}, 3000);
	}

	/**
	 * 항목 업데이트 시뮬레이션
	 */
	function simulateUpdateItem() {
		categoryStore.update((data) => {
			const updated = { ...data };
			const items = categoryType === 'cate4' ? updated.cate4 : updated.cate5;

			if (items.length > 0) {
				const randomIndex = Math.floor(Math.random() * items.length);
				const item = items[randomIndex];

				// 이름 업데이트
				item.name = `${item.name} (수정됨)`;
				item.description = `${new Date().toLocaleTimeString()}에 업데이트됨`;

				updated.lastUpdated = new Date().toISOString();

				// 업데이트 효과
				updatedItemsStore.update((items) => new Set([...items, item.id]));

				// 2초 후 효과 제거
				setTimeout(() => {
					updatedItemsStore.update((items) => {
						const updated = new Set(items);
						updated.delete(item.id);
						return updated;
					});
				}, 2000);
			}

			return updated;
		});
	}

	/**
	 * 검색어 초기화
	 */
	function clearSearch() {
		searchQuery = '';
	}
</script>

<div class="realtime-category-demo p-6 max-w-6xl mx-auto">
	<div class="mb-6">
		<h2 class="text-2xl font-bold mb-4">실시간 카테고리 목록 컴포넌트 데모</h2>
		<p class="text-base-content/70">
			실시간 업데이트, 가상 스크롤링, 애니메이션 효과를 포함한 카테고리 목록 컴포넌트입니다.
		</p>
	</div>

	<!-- 컨트롤 패널 -->
	<div class="control-panel bg-base-200 p-4 rounded-lg mb-6">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			<!-- 카테고리 타입 선택 -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">카테고리 타입</span>
				</label>
				<select class="select select-bordered" bind:value={categoryType}>
					<option value="cate4">4차 카테고리</option>
					<option value="cate5">5차 카테고리</option>
				</select>
			</div>

			<!-- 컨테이너 높이 -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">높이: {containerHeight}px</span>
				</label>
				<input
					type="range"
					min="200"
					max="800"
					bind:value={containerHeight}
					class="range range-primary"
				/>
			</div>

			<!-- 애니메이션 토글 -->
			<div class="form-control">
				<label class="label cursor-pointer">
					<span class="label-text">애니메이션</span>
					<input type="checkbox" bind:checked={showAnimations} class="toggle toggle-primary" />
				</label>
			</div>

			<!-- 액션 버튼들 -->
			<div class="form-control">
				<label class="label">
					<span class="label-text">테스트 액션</span>
				</label>
				<div class="flex gap-2">
					<button class="btn btn-success btn-sm" on:click={simulateNewItem}> 추가 </button>
					<button class="btn btn-info btn-sm" on:click={simulateUpdateItem}> 수정 </button>
				</div>
			</div>
		</div>

		<!-- 검색 -->
		<div class="form-control mt-4">
			<label class="label">
				<span class="label-text">검색</span>
			</label>
			<div class="input-group">
				<input
					type="text"
					placeholder="카테고리명, 코드, 설명으로 검색..."
					class="input input-bordered flex-1"
					bind:value={searchQuery}
				/>
				{#if searchQuery}
					<button class="btn btn-square btn-outline" on:click={clearSearch}> ✕ </button>
				{/if}
			</div>
		</div>
	</div>

	<!-- 실시간 카테고리 목록 컴포넌트 -->
	<div class="bg-base-100 rounded-lg shadow-lg overflow-hidden">
		<RealtimeCategoryList
			{categoryType}
			{containerHeight}
			{searchQuery}
			{showAnimations}
			onItemClick={handleItemClick}
			onItemDoubleClick={handleItemDoubleClick}
		/>
	</div>

	<!-- 사용법 안내 -->
	<div class="mt-8 bg-base-200 p-6 rounded-lg">
		<h3 class="text-lg font-semibold mb-4">사용법</h3>
		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h4 class="font-medium mb-2">기본 기능</h4>
				<ul class="list-disc list-inside space-y-1 text-sm">
					<li>실시간 데이터 동기화</li>
					<li>가상 스크롤링으로 성능 최적화</li>
					<li>검색 및 필터링</li>
					<li>항목 클릭/더블클릭 이벤트</li>
				</ul>
			</div>
			<div>
				<h4 class="font-medium mb-2">애니메이션 효과</h4>
				<ul class="list-disc list-inside space-y-1 text-sm">
					<li>새 항목 추가 시 하이라이트 (초록색)</li>
					<li>항목 업데이트 시 펄스 효과 (파란색)</li>
					<li>항목 삭제 시 페이드 효과 (빨간색)</li>
					<li>부드러운 스크롤 애니메이션</li>
				</ul>
			</div>
		</div>
	</div>
</div>

<style>
	.realtime-category-demo {
		min-height: 100vh;
	}

	.control-panel {
		border: 1px solid theme('colors.base-300');
	}
</style>
