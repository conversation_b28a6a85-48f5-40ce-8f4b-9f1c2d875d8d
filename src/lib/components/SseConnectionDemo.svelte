<!--
SSE 연결 데모 컴포넌트
SSE 연결 상태와 통계를 시각적으로 보여주는 데모 컴포넌트입니다.
-->

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		createDefaultSseConnection,
		closeSseConnection
	} from '$lib/services/sseConnection';
	import {
		sseConnectionState,
		isConnected,
		isConnecting,
		hasError,
		initializeNetworkMonitoring,
		startAllMonitoring,
		stopAllMonitoring,
		performAdvancedHealthCheck,
		getDebugInfo,
		connectionQuality,
		currentLatency,
		lastHeartbeat
	} from '$lib/services/sseConnectionState';
	import { sseStats, formatStats } from '$lib/services/sseStats';
	import { registerMessageHandler } from '$lib/services/sseMessageRouter';
	import { initializeDataHandlers } from '$lib/services/sseDataHandlers';
	import {
		notifications,
		unreadCount,
		initializeNotificationStore
	} from '$lib/stores/notificationStore';

	let eventSource: EventSource | null = null;
	let notificationEventSource: EventSource | null = null;
	let healthCheckResult: any = null;
	let debugInfo: any = null;
	let messages: string[] = [];

	onMount(() => {
		// 모든 모니터링 시작
		startAllMonitoring();

		// 데이터 핸들러 초기화
		initializeDataHandlers();

		// 알림 스토어 초기화
		initializeNotificationStore();

		// 테스트용 메시지 핸들러 등록
		registerMessageHandler('notification', (data: any) => {
			messages = [...messages, `알림: ${data.title} - ${data.message}`];
		});
		
		registerMessageHandler('data_update', (data: any) => {
			messages = [...messages, `데이터 업데이트: ${data.model} - ${data.action}`];
		});
		
		registerMessageHandler('heartbeat', (_data: any) => {
			messages = [...messages, `하트비트: ${new Date().toLocaleTimeString()}`];
		});
	});

	onDestroy(() => {
		if (eventSource) {
			closeSseConnection(eventSource);
		}
		if (notificationEventSource) {
			closeSseConnection(notificationEventSource);
		}
	});

	function connectDefault() {
		if (eventSource) {
			closeSseConnection(eventSource);
		}

		eventSource = createDefaultSseConnection();
		if (eventSource) {
			eventSource.addEventListener('message', (event) => {
				messages = [...messages, `기본: ${event.data}`];
			});
		}
	}

	function connectNotification() {
		if (notificationEventSource) {
			closeSseConnection(notificationEventSource);
		}

		notificationEventSource = createDefaultSseConnection();
		if (notificationEventSource) {
			notificationEventSource.addEventListener('message', (event) => {
				messages = [...messages, `알림: ${event.data}`];
			});
		}
	}

	function disconnect() {
		if (eventSource) {
			closeSseConnection(eventSource);
			eventSource = null;
		}
		if (notificationEventSource) {
			closeSseConnection(notificationEventSource);
			notificationEventSource = null;
		}
	}

	function clearMessages() {
		messages = [];
	}

	async function runHealthCheck() {
		healthCheckResult = await performAdvancedHealthCheck();
	}

	function showDebugInfo() {
		debugInfo = getDebugInfo();
	}

	function hideDebugInfo() {
		debugInfo = null;
	}

	// 상태에 따른 스타일 클래스
	$: statusClass =
		{
			disconnected: 'badge-error',
			connecting: 'badge-warning',
			reconnecting: 'badge-warning',
			connected: 'badge-success',
			error: 'badge-error',
			closed: 'badge-neutral'
		}[$sseConnectionState.status] || 'badge-neutral';

	$: formattedStats = formatStats($sseStats);
</script>

<div class="card bg-base-100 shadow-xl">
	<div class="card-body">
		<h2 class="card-title">SSE 연결 데모</h2>

		<!-- 연결 상태 -->
		<div class="flex items-center gap-4 mb-4">
			<div class="badge {statusClass}">
				{$sseConnectionState.status}
			</div>

			{#if $sseConnectionState.isOnline}
				<div class="badge badge-success">온라인</div>
			{:else}
				<div class="badge badge-error">오프라인</div>
			{/if}

			{#if $sseConnectionState.reconnectAttempts > 0}
				<div class="badge badge-warning">
					재연결 시도: {$sseConnectionState.reconnectAttempts}
				</div>
			{/if}
		</div>

		<!-- 고급 연결 정보 -->
		{#if $isConnected}
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
				<div class="stat bg-base-200 rounded-lg p-3">
					<div class="stat-title text-xs">연결 품질</div>
					<div class="stat-value text-sm">
						<span
							class="badge badge-{$connectionQuality === 'excellent'
								? 'success'
								: $connectionQuality === 'good'
									? 'info'
									: 'warning'}"
						>
							{$connectionQuality}
						</span>
					</div>
				</div>

				{#if $currentLatency !== null}
					<div class="stat bg-base-200 rounded-lg p-3">
						<div class="stat-title text-xs">지연시간</div>
						<div class="stat-value text-sm">{$currentLatency}ms</div>
					</div>
				{/if}

				{#if $lastHeartbeat}
					<div class="stat bg-base-200 rounded-lg p-3">
						<div class="stat-title text-xs">마지막 하트비트</div>
						<div class="stat-value text-xs">{$lastHeartbeat.toLocaleTimeString('ko-KR')}</div>
					</div>
				{/if}
			</div>
		{/if}

		<!-- 연결 버튼들 -->
		<div class="flex gap-2 mb-4">
			<button
				class="btn btn-primary"
				class:loading={$isConnecting}
				disabled={$isConnected}
				onclick={connectDefault}
			>
				기본 연결
			</button>

			<button
				class="btn btn-secondary"
				class:loading={$isConnecting}
				disabled={$isConnected}
				onclick={connectNotification}
			>
				알림 연결
			</button>

			<button class="btn btn-error" disabled={!$isConnected} onclick={disconnect}>
				연결 해제
			</button>

			<button class="btn btn-info btn-sm" onclick={runHealthCheck}> 건강성 검사 </button>

			<button class="btn btn-ghost btn-sm" onclick={showDebugInfo}> 디버그 정보 </button>
		</div>

		<!-- 오류 메시지 -->
		{#if $hasError && $sseConnectionState.lastError}
			<div class="alert alert-error mb-4">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="stroke-current shrink-0 h-6 w-6"
					fill="none"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
				<span>{$sseConnectionState.lastError}</span>
			</div>
		{/if}

		<!-- 통계 정보 -->
		<div class="stats stats-vertical lg:stats-horizontal shadow mb-4">
			{#each Object.entries(formattedStats) as [key, value]}
				<div class="stat">
					<div class="stat-title">{key}</div>
					<div class="stat-value text-sm">{value}</div>
				</div>
			{/each}
		</div>

		<!-- 건강성 검사 결과 -->
		{#if healthCheckResult}
			<div class="mb-4">
				<div class="flex justify-between items-center mb-2">
					<h3 class="text-lg font-semibold">연결 건강성 검사</h3>
					<button class="btn btn-sm btn-outline" onclick={() => (healthCheckResult = null)}>
						닫기
					</button>
				</div>

				<div class="alert {healthCheckResult.isHealthy ? 'alert-success' : 'alert-warning'}">
					<div class="flex-1">
						<div class="font-semibold">
							{healthCheckResult.isHealthy ? '✅ 연결 상태 양호' : '⚠️ 연결 문제 감지'}
						</div>

						{#if healthCheckResult.issues.length > 0}
							<div class="mt-2">
								<div class="text-sm font-medium">문제점:</div>
								<ul class="list-disc list-inside text-sm">
									{#each healthCheckResult.issues as issue}
										<li>{issue}</li>
									{/each}
								</ul>
							</div>
						{/if}

						{#if healthCheckResult.recommendations.length > 0}
							<div class="mt-2">
								<div class="text-sm font-medium">권장사항:</div>
								<ul class="list-disc list-inside text-sm">
									{#each healthCheckResult.recommendations as recommendation}
										<li>{recommendation}</li>
									{/each}
								</ul>
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}

		<!-- 디버그 정보 -->
		{#if debugInfo}
			<div class="mb-4">
				<div class="flex justify-between items-center mb-2">
					<h3 class="text-lg font-semibold">디버그 정보</h3>
					<button class="btn btn-sm btn-outline" onclick={hideDebugInfo}> 닫기 </button>
				</div>

				<div class="bg-base-200 p-4 rounded-lg">
					<pre class="text-xs overflow-x-auto">{JSON.stringify(debugInfo, null, 2)}</pre>
				</div>
			</div>
		{/if}

		<!-- 메시지 로그 -->
		<div class="mb-4">
			<div class="flex justify-between items-center mb-2">
				<h3 class="text-lg font-semibold">수신 메시지</h3>
				<button class="btn btn-sm btn-outline" onclick={clearMessages}> 지우기 </button>
			</div>

			<div class="bg-base-200 p-4 rounded-lg max-h-60 overflow-y-auto">
				{#if messages.length === 0}
					<p class="text-base-content/60">수신된 메시지가 없습니다.</p>
				{:else}
					{#each messages as message, index}
						<div class="text-sm mb-1 font-mono">
							<span class="text-base-content/60">[{index + 1}]</span>
							{message}
						</div>
					{/each}
				{/if}
			</div>
		</div>

		<!-- 알림 목록 -->
		{#if $notifications.length > 0}
			<div class="mb-4">
				<div class="flex justify-between items-center mb-2">
					<h3 class="text-lg font-semibold">
						수신된 알림
						{#if $unreadCount > 0}
							<span class="badge badge-error badge-sm">{$unreadCount}</span>
						{/if}
					</h3>
				</div>

				<div class="space-y-2 max-h-40 overflow-y-auto">
					{#each $notifications.slice(0, 5) as notification}
						<div class="alert alert-info text-sm">
							<div class="flex-1">
								<div class="font-semibold">{notification.title}</div>
								<div class="text-xs opacity-70">{notification.message}</div>
								<div class="text-xs opacity-50">
									{notification.receivedAt.toLocaleTimeString('ko-KR')}
								</div>
							</div>
							<div
								class="badge badge-{notification.priority === 'urgent'
									? 'error'
									: notification.priority === 'high'
										? 'warning'
										: 'info'} badge-sm"
							>
								{notification.priority}
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}

		<!-- 연결 정보 -->
		{#if $sseConnectionState.lastConnected}
			<div class="text-sm text-base-content/60">
				마지막 연결: {$sseConnectionState.lastConnected.toLocaleString('ko-KR')}
			</div>
		{/if}
	</div>
</div>
