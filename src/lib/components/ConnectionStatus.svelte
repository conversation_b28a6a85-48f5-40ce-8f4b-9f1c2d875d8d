<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { sseConnectionState, performAdvancedHealthCheck } from '$lib/services/sseConnectionState';
	import { manualReconnect, isOfflineMode } from '$lib/services/sseConnection';
	import { sseStatsStore } from '$stores';
	import type { SseConnectionState } from '$lib/types/sseTypes';

	// 컴포넌트 상태 (Svelte 5 Runes)
	let showDetails = $state(false);
	let isReconnectLoading = $state(false);
	let healthCheckResult = $state<any>(null);
	let lastHealthCheck = $state<Date | null>(null);
	let healthCheckInterval = $state<NodeJS.Timeout | null>(null);
	let networkStatus = $state(navigator.onLine);

	// 연결 상태 파생 스토어들 (Svelte 5 Runes)
	const isConnected = $derived($sseConnectionState.status === 'connected');
	const isConnecting = $derived($sseConnectionState.status === 'connecting');
	const isReconnecting = $derived($sseConnectionState.status === 'reconnecting');
	const hasError = $derived($sseConnectionState.status === 'error');
	const canReconnect = $derived(
		$sseConnectionState.isOnline && !['connecting', 'reconnecting'].includes($sseConnectionState.status)
	);

	/**
	 * 수동 재연결 실행
	 */
	async function handleManualReconnect(): Promise<void> {
		if (isReconnectLoading) return;

		try {
			isReconnectLoading = true;
			console.log('수동 재연결 시도 시작');
			await manualReconnect();
			console.log('수동 재연결 완료');
		} catch (error) {
			console.error('수동 재연결 실패:', error);
		} finally {
			isReconnectLoading = false;
		}
	}

	/**
	 * 연결 상태 세부 정보 토글
	 */
	function toggleDetails(): void {
		showDetails = !showDetails;
		if (showDetails && !healthCheckResult) {
			performHealthCheck();
		}
	}

	/**
	 * 건강성 검사 실행
	 */
	async function performHealthCheck(): Promise<void> {
		try {
			healthCheckResult = await performAdvancedHealthCheck();
			lastHealthCheck = new Date();
		} catch (error) {
			console.error('건강성 검사 실패:', error);
			healthCheckResult = {
				isHealthy: false,
				issues: ['건강성 검사 실행 실패'],
				recommendations: ['나중에 다시 시도하세요']
			};
		}
	}

	/**
	 * 연결 상태에 따른 스타일 클래스 반환
	 */
	function getStatusClass(state: SseConnectionState): string {
		switch (state.status) {
			case 'connected':
				return 'badge-success';
			case 'connecting':
			case 'reconnecting':
				return 'badge-warning';
			case 'error':
				return 'badge-error';
			case 'closed':
			case 'disconnected':
			default:
				return 'badge-neutral';
		}
	}

	/**
	 * 연결 상태 텍스트 반환
	 */
	function getStatusText(state: SseConnectionState): string {
		switch (state.status) {
			case 'connected':
				return '연결됨';
			case 'connecting':
				return '연결 중...';
			case 'reconnecting':
				return `재연결 중... (${state.reconnectAttempts}회 시도)`;
			case 'error':
				return '연결 오류';
			case 'closed':
				return '연결 종료';
			case 'disconnected':
			default:
				return '연결 안됨';
		}
	}

	/**
	 * 연결 품질 아이콘 반환
	 */
	function getQualityIcon(quality: string): string {
		switch (quality) {
			case 'excellent':
				return '🟢';
			case 'good':
				return '🟡';
			case 'poor':
				return '🔴';
			default:
				return '⚪';
		}
	}

	/**
	 * 시간 포맷팅
	 */
	function formatTime(date: Date | null): string {
		if (!date) return '없음';
		return new Intl.DateTimeFormat('ko-KR', {
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		}).format(date);
	}

	/**
	 * 지연시간 포맷팅
	 */
	function formatLatency(latency: number | null): string {
		if (latency === null) return '측정 안됨';
		return `${latency}ms`;
	}

	/**
	 * 네트워크 상태 업데이트
	 */
	function updateNetworkStatus(): void {
		networkStatus = navigator.onLine;
	}

	/**
	 * 컴포넌트 마운트 시 초기화
	 */
	onMount(() => {
		// 네트워크 상태 이벤트 리스너 등록
		window.addEventListener('online', updateNetworkStatus);
		window.addEventListener('offline', updateNetworkStatus);

		// 주기적 건강성 검사 (5분마다)
		if (showDetails) {
			healthCheckInterval = setInterval(performHealthCheck, 5 * 60 * 1000);
		}

		console.log('연결 상태 컴포넌트 초기화 완료');
	});

	/**
	 * 컴포넌트 언마운트 시 정리
	 */
	onDestroy(() => {
		// 이벤트 리스너 제거
		window.removeEventListener('online', updateNetworkStatus);
		window.removeEventListener('offline', updateNetworkStatus);

		// 인터벌 정리
		if (healthCheckInterval) {
			clearInterval(healthCheckInterval);
		}

		console.log('연결 상태 컴포넌트 정리 완료');
	});

	// 세부 정보 표시 상태가 변경될 때 건강성 검사 인터벌 관리
	$effect(() => {
		if (showDetails && !healthCheckInterval) {
			healthCheckInterval = setInterval(performHealthCheck, 5 * 60 * 1000);
		} else if (!showDetails && healthCheckInterval) {
			clearInterval(healthCheckInterval);
			healthCheckInterval = null;
		}
	});
</script>

<!-- 연결 상태 표시 컴포넌트 -->
<div class="connection-status-container">
	<!-- 메인 상태 표시 -->
	<div class="flex items-center gap-2 p-2 bg-base-200 rounded-lg">
		<!-- 연결 상태 배지 -->
		<div class="badge {getStatusClass($sseConnectionState)} gap-1">
			{#if isConnecting || isReconnecting}
				<span class="loading loading-spinner loading-xs"></span>
			{/if}
			{getStatusText($sseConnectionState)}
		</div>

		<!-- 네트워크 상태 표시 -->
		<div class="tooltip" data-tip="네트워크 상태">
			<div class="badge {networkStatus ? 'badge-success' : 'badge-error'} badge-sm">
				{networkStatus ? '온라인' : '오프라인'}
			</div>
		</div>

		<!-- 연결 품질 표시 -->
		{#if $sseConnectionState.connectionQuality !== 'unknown'}
			<div class="tooltip" data-tip="연결 품질: {$sseConnectionState.connectionQuality}">
				<span class="text-sm">
					{getQualityIcon($sseConnectionState.connectionQuality)}
				</span>
			</div>
		{/if}

		<!-- 수동 재연결 버튼 -->
		{#if canReconnect && !isReconnectLoading}
			<button
				class="btn btn-xs btn-primary"
				onclick={handleManualReconnect}
				disabled={!networkStatus}
				title="수동 재연결"
			>
				재연결
			</button>
		{:else if isReconnectLoading}
			<button class="btn btn-xs btn-primary loading" disabled> 재연결 중... </button>
		{/if}

		<!-- 세부 정보 토글 버튼 -->
		<button
			class="btn btn-xs btn-ghost"
			onclick={toggleDetails}
			title="세부 정보 {showDetails ? '숨기기' : '보기'}"
		>
			{showDetails ? '▲' : '▼'}
		</button>
	</div>

	<!-- 세부 정보 패널 -->
	{#if showDetails}
		<div class="mt-2 p-3 bg-base-100 rounded-lg border border-base-300">
			<h4 class="font-semibold mb-2">연결 상태 세부 정보</h4>

			<!-- 기본 연결 정보 -->
			<div class="grid grid-cols-2 gap-2 text-sm mb-3">
				<div>
					<span class="font-medium">상태:</span>
					{getStatusText($sseConnectionState)}
				</div>
				<div>
					<span class="font-medium">네트워크:</span>
					{networkStatus ? '온라인' : '오프라인'}
				</div>
				<div>
					<span class="font-medium">마지막 연결:</span>
					{formatTime($sseConnectionState.lastConnected)}
				</div>
				<div>
					<span class="font-medium">지연시간:</span>
					{formatLatency($sseConnectionState.latency)}
				</div>
				<div>
					<span class="font-medium">재연결 시도:</span>
					{$sseConnectionState.reconnectAttempts}회
				</div>
				<div>
					<span class="font-medium">마지막 하트비트:</span>
					{formatTime($sseConnectionState.lastHeartbeat)}
				</div>
			</div>

			<!-- 오류 정보 -->
			{#if $sseConnectionState.lastError}
				<div class="alert alert-error alert-sm mb-3">
					<span class="text-xs">오류: {$sseConnectionState.lastError}</span>
				</div>
			{/if}

			<!-- SSE 통계 -->
			<div class="mb-3">
				<h5 class="font-medium mb-1">메시지 통계</h5>
				<div class="grid grid-cols-2 gap-2 text-xs">
					<div>총 메시지: {$sseStatsStore.totalMessagesReceived}개</div>
					<div>알림: {$sseStatsStore.notificationsReceived}개</div>
					<div>데이터 업데이트: {$sseStatsStore.dataUpdatesReceived}개</div>
					<div>오류: {$sseStatsStore.errorsCount}개</div>
				</div>
			</div>

			<!-- 건강성 검사 결과 -->
			{#if healthCheckResult}
				<div class="mb-3">
					<div class="flex items-center justify-between mb-1">
						<h5 class="font-medium">건강성 검사</h5>
						<button
							class="btn btn-xs btn-ghost"
							onclick={performHealthCheck}
							title="건강성 검사 다시 실행"
						>
							🔄
						</button>
					</div>

					<div class="text-xs text-base-content/70 mb-2">
						마지막 검사: {lastHealthCheck ? formatTime(lastHealthCheck) : '없음'}
					</div>

					{#if healthCheckResult.isHealthy}
						<div class="alert alert-success alert-sm">
							<span class="text-xs">연결 상태가 양호합니다</span>
						</div>
					{:else}
						<div class="alert alert-warning alert-sm mb-2">
							<span class="text-xs">연결 상태에 문제가 있습니다</span>
						</div>

						{#if healthCheckResult.issues.length > 0}
							<div class="mb-2">
								<div class="font-medium text-xs mb-1">문제점:</div>
								<ul class="text-xs list-disc list-inside space-y-1">
									{#each healthCheckResult.issues as issue}
										<li>{issue}</li>
									{/each}
								</ul>
							</div>
						{/if}

						{#if healthCheckResult.recommendations.length > 0}
							<div>
								<div class="font-medium text-xs mb-1">권장사항:</div>
								<ul class="text-xs list-disc list-inside space-y-1">
									{#each healthCheckResult.recommendations as recommendation}
										<li>{recommendation}</li>
									{/each}
								</ul>
							</div>
						{/if}
					{/if}
				</div>
			{:else}
				<div class="mb-3">
					<button
						class="btn btn-xs btn-outline"
						onclick={performHealthCheck}
						title="건강성 검사 실행"
					>
						건강성 검사 실행
					</button>
				</div>
			{/if}

			<!-- 오프라인 모드 표시 -->
			{#if isOfflineMode()}
				<div class="alert alert-info alert-sm">
					<span class="text-xs">
						오프라인 모드에서 실행 중입니다. 캐시된 데이터를 사용합니다.
					</span>
				</div>
			{/if}
		</div>
	{/if}
</div>

<style>
	.connection-status-container {
		@apply w-full max-w-md;
	}

	/* 연결 상태별 애니메이션 */
	.badge.badge-warning {
		animation: pulse 2s infinite;
	}

	.badge.badge-error {
		animation: blink 1s infinite;
	}

	@keyframes pulse {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.7;
		}
	}

	@keyframes blink {
		0%,
		50% {
			opacity: 1;
		}
		51%,
		100% {
			opacity: 0.5;
		}
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.connection-status-container {
			@apply max-w-full;
		}

		.grid-cols-2 {
			@apply grid-cols-1;
		}
	}
</style>