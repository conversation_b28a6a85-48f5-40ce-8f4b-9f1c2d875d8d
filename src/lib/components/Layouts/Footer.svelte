<script lang="ts">
	import { sseConnectionState } from '$lib/services';
	import { browser } from '$app/environment';

	const isDevelopment = import.meta.env.VITE_NODE_ENV === 'development';

	let connectionStatus = $state('disconnected');
	let reconnectAttempts = $state(0);
	let lastError = $state('');

	// SSE 연결 상태 구독
	$effect(() => {
		if (browser) {
			return sseConnectionState.subscribe((state) => {
				connectionStatus = state.status;
				reconnectAttempts = state.reconnectAttempts;
				lastError = state.lastError || '';
			});
		}
	});
</script>

{#if isDevelopment}
	<footer class="grid grid-cols-3 items-center p-2 bg-base-300 text-base-content">
		<div>
			<!-- Left spacer -->
		</div>
		<div class="justify-self-center">
			<span class="badge badge-warning badge-sm">개발 버전</span>
		</div>
		<div class="justify-self-end">
			<!-- SSE Status Indicator -->
			{#if browser}
				<div
					class="tooltip tooltip-left"
					data-tip={`SSE: ${connectionStatus}${reconnectAttempts > 0 ? ` (${reconnectAttempts})` : ''}${lastError ? `\n오류: ${lastError}` : ''}`}
				>
					<div
						class="w-3 h-3 rounded-full {connectionStatus === 'connected'
								? 'bg-success'
								: connectionStatus === 'connecting' || connectionStatus === 'reconnecting'
									? 'bg-warning'
									: 'bg-error'}"
					></div>
				</div>
			{/if}
		</div>
	</footer>
{:else}
	<footer></footer>
{/if}
