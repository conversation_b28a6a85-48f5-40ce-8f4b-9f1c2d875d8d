<script lang="ts">
	import { onMount } from 'svelte';
	import { loadWorkHistory, workHistoryStore } from '$stores/workHistoryStore';
	import { getNumberFormat, formatDateTimeToFullString } from '$lib/Functions';
	import { getUser } from '$lib/User';

	import Paginate from '$components/UI/Paginate.svelte';

	import Icon from 'svelte-awesome';
	import { faHistory } from '@fortawesome/free-solid-svg-icons/faHistory';
	import { faCalendarDay } from '@fortawesome/free-solid-svg-icons/faCalendarDay';
	import { faListCheck } from '@fortawesome/free-solid-svg-icons/faListCheck';
	import { getProcessGradeColorButton } from '$stores/processStore';

	let user = getUser();

	// 상태 관리
	let p = $state('1');
	let pageSize = $state('5');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let localUrl = $state('/works/history');
	let startNo = $derived($workHistoryStore?.pageStartNo ?? 0);
	
	// 날짜 선택 상태
	let selectedDate = $state(new Date().toISOString().split('T')[0]); // 오늘 날짜를 기본값으로 설정
	
	// 파생된 값들
	let averageWorkTime = $derived($workHistoryStore?.workLogs && $workHistoryStore.workLogs.length > 0 
		? Math.round($workHistoryStore.workLogs.length / 8) 
		: 0);
	
	let hasSummary = $derived(!!$workHistoryStore?.summary);
	let hasPagination = $derived($workHistoryStore?.pageTotal && $workHistoryStore.pageTotal > 0);

	// 데이터 로딩 함수
	async function makeData(search: boolean = false) {
		if (search) {
			p = '1';
		}

		const params = new URLSearchParams({ p, pageSize });
		const api_params = new URLSearchParams({ page: p, per_page: pageSize });

		searchParams = params.toString();
		apiSearchParams = api_params.toString();

		await loadWorkHistory(parseInt(p), parseInt(pageSize), selectedDate);
	}
	
	// 날짜 변경 핸들러
	async function handleDateChange(event: Event) {
		const target = event.target as HTMLInputElement;
		selectedDate = target.value;
		await makeData(true);
	}

	// 페이지 변경 핸들러
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await makeData();
	}

	// 컴포넌트 마운트 시 초기 데이터 로드
	onMount(async () => {
		await makeData();
	});
</script>

<!-- 작업 내역 컨테이너 -->
<div class="work-history-container">
	<!-- 헤더 섹션 -->
	<div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg mb-6 shadow-lg">
		<div class="flex items-center justify-between">
			<div class="flex items-center space-x-4">
				<div class="bg-white/20 p-3 rounded-full">
					<Icon data={faHistory} class="text-2xl" />
				</div>
				<div>
					<h1 class="text-2xl font-bold">나의 작업 내역</h1>
					<p class="text-blue-100 mt-1">오늘 완료한 모든 작업을 확인하세요</p>
				</div>
			</div>
			<!-- {#if $workHistoryStore.summary}
				<div class="text-right">
					<div class="flex items-center space-x-2 text-blue-100">
						<Icon data={faListCheck} />
						<span class="font-semibold">총 {$workHistoryStore.summary.total_count}건</span>
					</div>
					<div class="flex items-center space-x-2 text-blue-100 mt-1">
						<Icon data={faCalendarDay} />
						<span>{$workHistoryStore.summary.today_date}</span>
					</div>
				</div>
			{/if} -->
		</div>
	</div>
	
	<!-- 통계 카드 -->
	{#if hasSummary}
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
			<div class="bg-white p-4 rounded-lg shadow-md border border-gray-200">
				<div class="flex items-center">
					<div class="bg-purple-100 p-2 rounded-lg">
						<Icon data={faCalendarDay} class="text-purple-600" />
					</div>
					<div class="ml-3">
						<p class="text-sm text-gray-600">작업 날짜</p>
						<p class="text-xl font-bold text-gray-900">{$workHistoryStore.summary.today_date}</p>
					</div>
				</div>
			</div>
			
			<div class="bg-white p-4 rounded-lg shadow-md border border-gray-200">
				<div class="flex items-center">
					<div class="bg-blue-100 p-2 rounded-lg">
						<Icon data={faListCheck} class="text-blue-600" />
					</div>
					<div class="ml-3">
						<p class="text-sm text-gray-600">총 작업 수</p>
						<p class="text-2xl font-bold text-gray-900">{$workHistoryStore.summary.total_count}</p>
					</div>
				</div>
			</div>
			
			<div class="bg-white p-4 rounded-lg shadow-md border border-gray-200">
				<div class="flex items-center">
					<div class="bg-orange-100 p-2 rounded-lg">
						<Icon data={faHistory} class="text-orange-600" />
					</div>
					<div class="ml-3">
						<p class="text-sm text-gray-600">평균 작업 시간</p>
						<p class="text-2xl font-bold text-gray-900">
							{averageWorkTime}시간
						</p>
					</div>
				</div>
			</div>
		</div>
	{/if}
	
	<!-- 리스트 섹션 -->
	<div class="bg-white rounded-lg shadow-md border border-gray-200">
		<!-- 테이블 헤더 -->
		<div class="px-6 py-4 border-b border-gray-200">
			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-3">
					<label for="date-selector" class="text-sm font-medium text-gray-700">작업 날짜 선택</label>
					<input
						id="date-selector"
						type="date"
						bind:value={selectedDate}
						onchange={handleDateChange}
						class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
						max={new Date().toISOString().split('T')[0]}
					/>
				</div>
			</div>
		</div>
		
		<!-- 테이블 -->
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead class="bg-gray-50">
				<tr>
					<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">번호</th>
					<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">QAID</th>
					<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">상품명</th>
					<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">작업 시간</th>
				</tr>
				</thead>
				<tbody class="bg-white divide-y divide-gray-200">
				{#if $workHistoryStore.workLogs && $workHistoryStore.workLogs.length > 0}
					{#each $workHistoryStore.workLogs as item, index}
						<tr class="hover:bg-gray-50 transition-colors duration-200">
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium w-20 text-center">
								{getNumberFormat(startNo - index)}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono w-32">
								{item.product.qaid}
							</td>
							<td class="px-6 py-4">
								<div class="text-sm font-medium text-gray-900 max-w-48 truncate" title={item.product.name}>
									{item.product.name.length > 50 ? item.product.name.substring(0, 50) + '...' : item.product.name}
								</div>
								<div class="flex items-center gap-2 mt-1">
									{@html getProcessGradeColorButton(item.product.repair_product)}
									{#if item.memo}
											<span class="text-xs text-gray-500 truncate max-w-48" title={item.memo}>
												📝 {item.memo}
											</span>
									{:else}
										<span class="text-xs text-gray-400 italic">메모 없음</span>
									{/if}
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 w-40">
								{formatDateTimeToFullString(item.created_at)}
							</td>
						</tr>
					{/each}
				{:else}
					<tr>
						<td colspan="4" class="px-6 py-12 text-center">
							<div class="flex flex-col items-center">
								<Icon data={faHistory} class="text-4xl text-gray-300 mb-4" />
								<p class="text-lg font-medium text-gray-500">선택한 날짜의 작업 내역이 없습니다</p>
								<p class="text-sm text-gray-400 mt-1">다른 날짜를 선택해보세요!</p>
							</div>
						</td>
					</tr>
				{/if}
				</tbody>
			</table>
		</div>
	</div>
	
	<!-- Pagination -->
	{#if hasPagination}
		<div class="mt-6">
			<Paginate
				store={workHistoryStore}
				{localUrl}
				onPageChange={handlePageChange}
				{searchParams}
			/>
		</div>
	{/if}
</div>

<style>
    .work-history-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        background-color: #f8fafc;
    }
</style> 