<script lang="ts">
	/**
	 * 실시간 카테고리 목록 컴포넌트
	 *
	 * 요구사항:
	 * - 1.1, 1.2, 1.3: 실시간 알림 시스템 통합
	 * - 4.9: 데이터 업데이트 시 UI 반영
	 * - 5.2: 실시간 데이터 동기화
	 * - 9.2: 성능 최적화 (가상 스크롤링)
	 */

	import { onMount, onDestroy, tick } from 'svelte';
	import { categoryStore } from '$lib/stores/dataStores';
	import {
		highlightedItemsStore,
		updatedItemsStore,
		deletedItemsStore,
		updateMessageStore
	} from '$lib/stores/uiStores';
	import type { CategoryStoreData } from '$lib/types/types';

	// Props
	export let categoryType: 'cate4' | 'cate5' = 'cate4';
	export let itemHeight: number = 60; // 각 항목의 높이 (px)
	export let containerHeight: number = 400; // 컨테이너 높이 (px)
	export let searchQuery: string = '';
	export let showAnimations: boolean = true;
	export let onItemClick: ((item: any) => void) | null = null;
	export let onItemDoubleClick: ((item: any) => void) | null = null;

	// 상태 변수들
	let categoryData: CategoryStoreData = { cate4: [], cate5: [], lastUpdated: null, totalCount: 0 };
	let filteredItems: any[] = [];
	let highlightedItems = new Set<number>();
	let updatedItems = new Set<number>();
	let deletedItems = new Set<number>();

	// 가상 스크롤링 관련 변수들
	let scrollContainer: HTMLDivElement;
	let scrollTop = 0;
	let visibleStartIndex = 0;
	let visibleEndIndex = 0;
	let visibleItemCount = 0;
	let totalHeight = 0;
	let paddingTop = 0;
	let paddingBottom = 0;

	// 애니메이션 관련
	let animatingItems = new Set<number>();
	let animationTimeouts = new Map<number, number>();

	// 구독 해제 함수들
	let unsubscribeFunctions: (() => void)[] = [];

	/**
	 * 컴포넌트 마운트 시 초기화
	 */
	onMount(() => {
		// 스토어 구독
		const unsubscribeCategory = categoryStore.subscribe((data) => {
			categoryData = data;
			updateFilteredItems();
		});

		const unsubscribeHighlighted = highlightedItemsStore.subscribe((items) => {
			highlightedItems = items;
			if (showAnimations) {
				handleHighlightAnimation(items);
			}
		});

		const unsubscribeUpdated = updatedItemsStore.subscribe((items) => {
			updatedItems = items;
			if (showAnimations) {
				handleUpdateAnimation(items);
			}
		});

		const unsubscribeDeleted = deletedItemsStore.subscribe((items) => {
			deletedItems = items;
			if (showAnimations) {
				handleDeleteAnimation(items);
			}
		});

		unsubscribeFunctions = [
			unsubscribeCategory,
			unsubscribeHighlighted,
			unsubscribeUpdated,
			unsubscribeDeleted
		];

		// 초기 가상 스크롤링 계산
		calculateVisibleItems();
	});

	/**
	 * 컴포넌트 언마운트 시 정리
	 */
	onDestroy(() => {
		// 스토어 구독 해제
		unsubscribeFunctions.forEach((fn) => fn());

		// 애니메이션 타이머 정리
		animationTimeouts.forEach((timeout) => clearTimeout(timeout));
		animationTimeouts.clear();
	});

	/**
	 * 필터링된 항목 업데이트
	 */
	function updateFilteredItems() {
		const items = categoryData[categoryType] || [];

		if (searchQuery.trim()) {
			const query = searchQuery.toLowerCase().trim();
			filteredItems = items.filter(
				(item) =>
					item.name?.toLowerCase().includes(query) ||
					item.code?.toLowerCase().includes(query) ||
					item.description?.toLowerCase().includes(query)
			);
		} else {
			filteredItems = [...items];
		}

		// 가상 스크롤링 재계산
		calculateVisibleItems();
	}

	/**
	 * 가상 스크롤링을 위한 가시 항목 계산
	 */
	function calculateVisibleItems() {
		if (!scrollContainer) return;

		visibleItemCount = Math.ceil(containerHeight / itemHeight) + 2; // 버퍼 추가
		visibleStartIndex = Math.floor(scrollTop / itemHeight);
		visibleEndIndex = Math.min(visibleStartIndex + visibleItemCount, filteredItems.length);

		totalHeight = filteredItems.length * itemHeight;
		paddingTop = visibleStartIndex * itemHeight;
		paddingBottom = Math.max(0, totalHeight - visibleEndIndex * itemHeight);
	}

	/**
	 * 스크롤 이벤트 핸들러
	 */
	function handleScroll() {
		if (!scrollContainer) return;

		scrollTop = scrollContainer.scrollTop;
		calculateVisibleItems();
	}

	/**
	 * 하이라이트 애니메이션 처리
	 */
	function handleHighlightAnimation(items: Set<number>) {
		items.forEach((itemId) => {
			if (!animatingItems.has(itemId)) {
				animatingItems.add(itemId);

				// 3초 후 하이라이트 제거
				const timeout = setTimeout(() => {
					animatingItems.delete(itemId);
					animationTimeouts.delete(itemId);
				}, 3000);

				animationTimeouts.set(itemId, timeout);
			}
		});
	}

	/**
	 * 업데이트 애니메이션 처리
	 */
	function handleUpdateAnimation(items: Set<number>) {
		items.forEach((itemId) => {
			if (!animatingItems.has(itemId)) {
				animatingItems.add(itemId);

				// 2초 후 애니메이션 제거
				const timeout = setTimeout(() => {
					animatingItems.delete(itemId);
					animationTimeouts.delete(itemId);
				}, 2000);

				animationTimeouts.set(itemId, timeout);
			}
		});
	}

	/**
	 * 삭제 애니메이션 처리
	 */
	function handleDeleteAnimation(items: Set<number>) {
		items.forEach((itemId) => {
			animatingItems.add(itemId);

			// 1초 후 애니메이션 제거
			const timeout = setTimeout(() => {
				animatingItems.delete(itemId);
				animationTimeouts.delete(itemId);
			}, 1000);

			animationTimeouts.set(itemId, timeout);
		});
	}

	/**
	 * 항목 클릭 핸들러
	 */
	function handleItemClick(item: any, event: MouseEvent) {
		if (onItemClick) {
			onItemClick(item);
		}
	}

	/**
	 * 항목 더블클릭 핸들러
	 */
	function handleItemDoubleClick(item: any, event: MouseEvent) {
		if (onItemDoubleClick) {
			onItemDoubleClick(item);
		}
	}

	/**
	 * 항목의 CSS 클래스 계산
	 */
	function getItemClasses(item: any): string {
		const classes = ['category-item'];

		if (highlightedItems.has(item.id)) {
			classes.push('highlighted');
		}

		if (updatedItems.has(item.id)) {
			classes.push('updated');
		}

		if (deletedItems.has(item.id)) {
			classes.push('deleted');
		}

		if (animatingItems.has(item.id)) {
			classes.push('animating');
		}

		return classes.join(' ');
	}

	/**
	 * 검색어 변경 시 필터링 업데이트
	 */
	$: if (searchQuery !== undefined) {
		updateFilteredItems();
	}

	/**
	 * 컨테이너 높이 변경 시 재계산
	 */
	$: if (containerHeight) {
		calculateVisibleItems();
	}
</script>

<!-- 실시간 카테고리 목록 컨테이너 -->
<div class="realtime-category-list">
	<!-- 헤더 정보 -->
	<div class="list-header">
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center gap-2">
				<h3 class="text-lg font-semibold">
					{categoryType === 'cate4' ? '4차 카테고리' : '5차 카테고리'}
				</h3>
				<div class="badge badge-primary">
					{filteredItems.length}개
				</div>
			</div>

			{#if categoryData.lastUpdated}
				<div class="text-sm text-base-content/70">
					마지막 업데이트: {new Date(categoryData.lastUpdated).toLocaleString('ko-KR')}
				</div>
			{/if}
		</div>
	</div>

	<!-- 가상 스크롤링 컨테이너 -->
	<div
		class="scroll-container"
		style="height: {containerHeight}px;"
		bind:this={scrollContainer}
		on:scroll={handleScroll}
	>
		<!-- 상단 패딩 -->
		{#if paddingTop > 0}
			<div style="height: {paddingTop}px;"></div>
		{/if}

		<!-- 가시 항목들 -->
		{#each filteredItems.slice(visibleStartIndex, visibleEndIndex) as item, index (item.id)}
			<div
				class={getItemClasses(item)}
				style="height: {itemHeight}px;"
				on:click={(e) => handleItemClick(item, e)}
				on:dblclick={(e) => handleItemDoubleClick(item, e)}
				role="button"
				tabindex="0"
				on:keydown={(e) => {
					if (e.key === 'Enter' || e.key === ' ') {
						handleItemClick(item, e);
					}
				}}
			>
				<div class="item-content">
					<!-- 카테고리 아이콘 -->
					<div class="item-icon">
						<div class="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
							<span class="text-primary font-semibold text-sm">
								{item.code ? item.code.charAt(0).toUpperCase() : 'C'}
							</span>
						</div>
					</div>

					<!-- 카테고리 정보 -->
					<div class="item-info">
						<div class="item-name">
							{item.name || '이름 없음'}
						</div>

						{#if item.code}
							<div class="item-code">
								코드: {item.code}
							</div>
						{/if}

						{#if item.description}
							<div class="item-description">
								{item.description}
							</div>
						{/if}
					</div>

					<!-- 상태 표시 -->
					<div class="item-status">
						{#if item.status === 'active'}
							<div class="badge badge-success badge-sm">활성</div>
						{:else if item.status === 'inactive'}
							<div class="badge badge-warning badge-sm">비활성</div>
						{:else}
							<div class="badge badge-ghost badge-sm">미정</div>
						{/if}
					</div>

					<!-- 애니메이션 표시 -->
					{#if highlightedItems.has(item.id)}
						<div class="animation-indicator new">NEW</div>
					{:else if updatedItems.has(item.id)}
						<div class="animation-indicator updated">UPD</div>
					{:else if deletedItems.has(item.id)}
						<div class="animation-indicator deleted">DEL</div>
					{/if}
				</div>
			</div>
		{/each}

		<!-- 하단 패딩 -->
		{#if paddingBottom > 0}
			<div style="height: {paddingBottom}px;"></div>
		{/if}
	</div>

	<!-- 빈 상태 표시 -->
	{#if filteredItems.length === 0}
		<div class="empty-state">
			<div class="text-center py-12">
				<div class="text-4xl mb-4">📂</div>
				<h4 class="text-lg font-semibold mb-2">카테고리가 없습니다</h4>
				<p class="text-base-content/70">
					{searchQuery ? '검색 조건에 맞는 카테고리가 없습니다.' : '등록된 카테고리가 없습니다.'}
				</p>
			</div>
		</div>
	{/if}
</div>

<style>
	.realtime-category-list {
		@apply w-full h-full flex flex-col;
	}

	.list-header {
		@apply flex-shrink-0 px-4 py-2 bg-base-100 border-b border-base-300;
	}

	.scroll-container {
		@apply flex-1 overflow-y-auto overflow-x-hidden;
		scroll-behavior: smooth;
	}

	.category-item {
		@apply border-b border-base-200 hover:bg-base-100 cursor-pointer transition-all duration-200;
		@apply flex items-center px-4 py-3;
	}

	.category-item:hover {
		@apply bg-base-100 shadow-sm;
	}

	.category-item.highlighted {
		@apply bg-success/10 border-success/30;
		animation: highlight-pulse 3s ease-in-out;
	}

	.category-item.updated {
		@apply bg-info/10 border-info/30;
		animation: update-pulse 2s ease-in-out;
	}

	.category-item.deleted {
		@apply bg-error/10 border-error/30 opacity-50;
		animation: delete-fade 1s ease-in-out;
	}

	.category-item.animating {
		transform-origin: center;
	}

	.item-content {
		@apply flex items-center w-full gap-3 relative;
	}

	.item-icon {
		@apply flex-shrink-0;
	}

	.item-info {
		@apply flex-1 min-w-0;
	}

	.item-name {
		@apply font-medium text-base-content truncate;
	}

	.item-code {
		@apply text-sm text-base-content/60 truncate;
	}

	.item-description {
		@apply text-sm text-base-content/50 truncate;
	}

	.item-status {
		@apply flex-shrink-0;
	}

	.animation-indicator {
		@apply absolute -top-1 -right-1 text-xs px-2 py-1 rounded-full font-bold;
		@apply animate-bounce;
	}

	.animation-indicator.new {
		@apply bg-success text-success-content;
	}

	.animation-indicator.updated {
		@apply bg-info text-info-content;
	}

	.animation-indicator.deleted {
		@apply bg-error text-error-content;
	}

	.empty-state {
		@apply flex-1 flex items-center justify-center;
	}

	/* 애니메이션 정의 */
	@keyframes highlight-pulse {
		0%,
		100% {
			background-color: rgb(34 197 94 / 0.1);
			border-color: rgb(34 197 94 / 0.3);
		}
		50% {
			background-color: rgb(34 197 94 / 0.2);
			border-color: rgb(34 197 94 / 0.5);
			transform: scale(1.02);
		}
	}

	@keyframes update-pulse {
		0%,
		100% {
			background-color: rgb(59 130 246 / 0.1);
			border-color: rgb(59 130 246 / 0.3);
		}
		50% {
			background-color: rgb(59 130 246 / 0.2);
			border-color: rgb(59 130 246 / 0.5);
			transform: scale(1.01);
		}
	}

	@keyframes delete-fade {
		0% {
			opacity: 1;
			transform: scale(1);
		}
		50% {
			opacity: 0.3;
			transform: scale(0.98);
		}
		100% {
			opacity: 0.5;
			transform: scale(0.95);
		}
	}

	/* 스크롤바 스타일링 */
	.scroll-container::-webkit-scrollbar {
		width: 8px;
	}

	.scroll-container::-webkit-scrollbar-track {
		@apply bg-base-200 rounded-lg;
	}

	.scroll-container::-webkit-scrollbar-thumb {
		@apply bg-base-300 rounded-lg hover:bg-base-300;
	}

	/* 포커스 스타일 */
	.category-item:focus {
		@apply outline-none ring-2 ring-primary-500 ring-offset-2;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.item-content {
			@apply gap-2;
		}

		.item-icon {
			@apply hidden;
		}

		.item-description {
			@apply hidden;
		}
	}
</style>
