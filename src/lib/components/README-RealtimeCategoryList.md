# RealtimeCategoryList 컴포넌트

실시간 카테고리 목록을 표시하는 Svelte 컴포넌트입니다. SSE를 통한 실시간 업데이트, 가상 스크롤링, 애니메이션 효과를 지원합니다.

## 주요 기능

### 1. 실시간 데이터 동기화

- SSE를 통한 실시간 카테고리 데이터 업데이트
- IndexedDB 캐시와 자동 동기화
- 네트워크 오류 시 캐시된 데이터 표시

### 2. 가상 스크롤링

- 대용량 데이터 처리를 위한 성능 최적화
- 가시 영역의 항목만 DOM에 렌더링
- 부드러운 스크롤 경험 제공

### 3. 실시간 애니메이션 효과

- 새 항목 추가 시 하이라이트 효과 (초록색, 3초)
- 항목 업데이트 시 펄스 효과 (파란색, 2초)
- 항목 삭제 시 페이드 효과 (빨간색, 1초)

### 4. 검색 및 필터링

- 실시간 검색 기능
- 이름, 코드, 설명으로 검색 가능
- 대소문자 구분 없는 검색

### 5. 접근성 지원

- 키보드 네비게이션 (Enter, Space)
- 포커스 관리 및 시각적 표시
- 스크린 리더 지원

## 사용법

### 기본 사용법

```svelte
<script>
	import RealtimeCategoryList from '$lib/components/RealtimeCategoryList.svelte';

	function handleItemClick(item) {
		console.log('선택된 항목:', item);
	}
</script>

<RealtimeCategoryList categoryType="cate4" containerHeight={400} onItemClick={handleItemClick} />
```

### 고급 사용법

```svelte
<script>
	import RealtimeCategoryList from '$lib/components/RealtimeCategoryList.svelte';

	let searchQuery = '';

	function handleItemClick(item) {
		// 항목 선택 처리
	}

	function handleItemDoubleClick(item) {
		// 항목 편집 처리
	}
</script>

<div class="search-container">
	<input
		type="text"
		bind:value={searchQuery}
		placeholder="카테고리 검색..."
		class="input input-bordered"
	/>
</div>

<RealtimeCategoryList
	categoryType="cate5"
	containerHeight={600}
	itemHeight={80}
	{searchQuery}
	showAnimations={true}
	onItemClick={handleItemClick}
	onItemDoubleClick={handleItemDoubleClick}
/>
```

## Props

| 속성                | 타입                          | 기본값    | 설명                        |
| ------------------- | ----------------------------- | --------- | --------------------------- |
| `categoryType`      | `'cate4' \| 'cate5'`          | `'cate4'` | 표시할 카테고리 타입        |
| `itemHeight`        | `number`                      | `60`      | 각 항목의 높이 (px)         |
| `containerHeight`   | `number`                      | `400`     | 컨테이너 높이 (px)          |
| `searchQuery`       | `string`                      | `''`      | 검색어                      |
| `showAnimations`    | `boolean`                     | `true`    | 애니메이션 효과 활성화 여부 |
| `onItemClick`       | `(item: any) => void \| null` | `null`    | 항목 클릭 이벤트 핸들러     |
| `onItemDoubleClick` | `(item: any) => void \| null` | `null`    | 항목 더블클릭 이벤트 핸들러 |

## 이벤트

### onItemClick

항목을 클릭했을 때 발생하는 이벤트입니다.

```typescript
function handleItemClick(item: Category) {
	console.log('클릭된 항목:', item);
	// item.id, item.name, item.code 등 사용 가능
}
```

### onItemDoubleClick

항목을 더블클릭했을 때 발생하는 이벤트입니다.

```typescript
function handleItemDoubleClick(item: Category) {
	console.log('더블클릭된 항목:', item);
	// 편집 모드로 전환 등의 처리
}
```

## 스타일링

### CSS 클래스

컴포넌트는 다음과 같은 CSS 클래스를 사용합니다:

- `.realtime-category-list`: 메인 컨테이너
- `.list-header`: 헤더 영역
- `.scroll-container`: 스크롤 컨테이너
- `.category-item`: 개별 카테고리 항목
- `.category-item.highlighted`: 하이라이트된 항목
- `.category-item.updated`: 업데이트된 항목
- `.category-item.deleted`: 삭제된 항목

### 커스텀 스타일링

```css
/* 항목 호버 효과 커스터마이징 */
.category-item:hover {
	background-color: theme('colors.primary.50');
	transform: translateX(4px);
}

/* 애니메이션 효과 커스터마이징 */
.category-item.highlighted {
	box-shadow: 0 0 20px theme('colors.success.300');
}
```

## 성능 최적화

### 가상 스크롤링

- 대용량 데이터(1000개 이상)에서도 부드러운 스크롤 제공
- 메모리 사용량 최적화
- DOM 노드 수 최소화

### 애니메이션 최적화

- CSS 애니메이션 사용으로 GPU 가속 활용
- 애니메이션 타이머 자동 정리
- 메모리 누수 방지

### 검색 최적화

- 실시간 필터링으로 빠른 검색 결과 제공
- 대소문자 구분 없는 검색
- 다중 필드 검색 지원

## 접근성

### 키보드 지원

- `Tab`: 다음 항목으로 이동
- `Shift + Tab`: 이전 항목으로 이동
- `Enter` 또는 `Space`: 항목 선택
- `Arrow Up/Down`: 항목 간 이동 (향후 추가 예정)

### 스크린 리더 지원

- 적절한 ARIA 레이블 제공
- 의미있는 HTML 구조 사용
- 상태 변경 시 알림 제공

## 브라우저 지원

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 관련 파일

- `src/lib/components/RealtimeCategoryList.svelte`: 메인 컴포넌트
- `src/lib/components/RealtimeCategoryListDemo.svelte`: 데모 컴포넌트
- `src/lib/components/__tests__/RealtimeCategoryList.test.ts`: 테스트 파일
- `src/lib/stores/dataStores.ts`: 데이터 스토어
- `src/lib/stores/uiStores.ts`: UI 상태 스토어

## 예제

### 기본 예제

```svelte
<RealtimeCategoryList
	categoryType="cate4"
	containerHeight={400}
	onItemClick={(item) => console.log(item)}
/>
```

### 검색 기능 포함 예제

```svelte
<script>
	let searchQuery = '';
</script>

<input bind:value={searchQuery} placeholder="검색..." />
<RealtimeCategoryList categoryType="cate4" {searchQuery} containerHeight={500} />
```

### 커스텀 높이 및 애니메이션 예제

```svelte
<RealtimeCategoryList
	categoryType="cate5"
	containerHeight={600}
	itemHeight={80}
	showAnimations={true}
	onItemClick={handleClick}
	onItemDoubleClick={handleDoubleClick}
/>
```

## 문제 해결

### 일반적인 문제

1. **항목이 표시되지 않음**
   - `categoryStore`에 데이터가 있는지 확인
   - `categoryType`이 올바른지 확인

2. **애니메이션이 작동하지 않음**
   - `showAnimations` prop이 `true`인지 확인
   - CSS 애니메이션이 비활성화되지 않았는지 확인

3. **검색이 작동하지 않음**
   - `searchQuery` prop이 올바르게 바인딩되었는지 확인
   - 검색 대상 필드(`name`, `code`, `description`)에 데이터가 있는지 확인

4. **성능 문제**
   - `itemHeight`를 적절히 설정했는지 확인
   - `containerHeight`가 너무 크지 않은지 확인

### 디버깅

개발자 도구 콘솔에서 다음 명령으로 상태를 확인할 수 있습니다:

```javascript
// 카테고리 데이터 확인
$categoryStore;

// UI 상태 확인
$highlightedItemsStore;
$updatedItemsStore;
```

## 기여하기

버그 리포트나 기능 제안은 이슈로 등록해 주세요. 풀 리퀘스트도 환영합니다.

### 개발 환경 설정

1. 저장소 클론
2. `pnpm install` 실행
3. `pnpm run dev` 실행
4. `http://localhost:5174/realtime-category-demo` 접속

### 테스트 실행

```bash
# 전체 테스트
pnpm run test

# 특정 테스트 파일
pnpm run test -- RealtimeCategoryList.test.ts
```
