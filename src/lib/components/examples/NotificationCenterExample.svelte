<script lang="ts">
	import NotificationCenter from '$components/NotificationCenter.svelte';
	import { allNotificationsStore } from '$lib/stores';
	import type { NotificationData } from '$lib/types/notificationTypes';

	// 예제 알림 데이터
	const exampleNotifications: NotificationData[] = [
		{
			id: 1,
			title: '새로운 작업 할당',
			message: '새로운 검수 작업이 할당되었습니다. 확인해주세요.',
			type: 'task',
			priority: 'normal',
			read: false,
			created_at: new Date().toISOString(),
			received_at: new Date().toISOString(),
			action_url: '/tasks/123'
		},
		{
			id: 2,
			title: '시스템 점검 안내',
			message: '오늘 밤 12시부터 2시간 동안 시스템 점검이 예정되어 있습니다.',
			type: 'maintenance',
			priority: 'high',
			read: false,
			created_at: new Date(Date.now() - 3600000).toISOString(),
			received_at: new Date(Date.now() - 3600000).toISOString()
		},
		{
			id: 3,
			title: '긴급 보안 업데이트',
			message: '보안 취약점이 발견되어 즉시 업데이트가 필요합니다.',
			type: 'security',
			priority: 'urgent',
			read: false,
			created_at: new Date(Date.now() - 1800000).toISOString(),
			received_at: new Date(Date.now() - 1800000).toISOString(),
			action_url: '/security/update'
		},
		{
			id: 4,
			title: '작업 완료 알림',
			message: '요청하신 작업이 성공적으로 완료되었습니다.',
			type: 'success',
			priority: 'low',
			read: true,
			created_at: new Date(Date.now() - 86400000).toISOString(),
			received_at: new Date(Date.now() - 86400000).toISOString(),
			read_at: new Date(Date.now() - 3600000).toISOString()
		},
		{
			id: 5,
			title: '회의 일정 변경',
			message: '내일 예정된 팀 회의 시간이 오후 3시로 변경되었습니다.',
			type: 'reminder',
			priority: 'normal',
			read: true,
			created_at: new Date(Date.now() - 7200000).toISOString(),
			received_at: new Date(Date.now() - 7200000).toISOString(),
			read_at: new Date(Date.now() - 1800000).toISOString()
		}
	];

	// 스토어에 예제 데이터 설정
	allNotificationsStore.set(exampleNotifications);

	/**
	 * 새 알림 추가 (테스트용)
	 */
	function addTestNotification() {
		const newNotification: NotificationData = {
			id: Date.now(),
			title: '테스트 알림',
			message: `${new Date().toLocaleTimeString()}에 생성된 테스트 알림입니다.`,
			type: 'message',
			priority: 'normal',
			read: false,
			created_at: new Date().toISOString(),
			received_at: new Date().toISOString()
		};

		allNotificationsStore.update((notifications) => [newNotification, ...notifications]);
	}

	/**
	 * 긴급 알림 추가 (테스트용)
	 */
	function addUrgentNotification() {
		const urgentNotification: NotificationData = {
			id: Date.now(),
			title: '긴급 알림 테스트',
			message: '이것은 긴급 우선순위 알림 테스트입니다.',
			type: 'warning',
			priority: 'urgent',
			read: false,
			created_at: new Date().toISOString(),
			received_at: new Date().toISOString(),
			action_url: 'https://example.com'
		};

		allNotificationsStore.update((notifications) => [urgentNotification, ...notifications]);
	}

	/**
	 * 모든 알림 삭제 (테스트용)
	 */
	function clearAllNotifications() {
		allNotificationsStore.set([]);
	}

	/**
	 * 예제 데이터 복원
	 */
	function restoreExampleData() {
		allNotificationsStore.set(exampleNotifications);
	}
</script>

<div class="container mx-auto p-6 max-w-4xl">
	<div class="card bg-base-100 shadow-xl">
		<div class="card-body">
			<h1 class="card-title text-2xl mb-4">알림 센터 컴포넌트 예제</h1>

			<p class="text-base-content/70 mb-6">
				우측 상단의 알림 센터 버튼을 클릭하여 알림 센터를 확인해보세요. 아래 버튼들로 다양한 알림
				상황을 테스트할 수 있습니다.
			</p>

			<!-- 테스트 버튼들 -->
			<div class="flex flex-wrap gap-3 mb-6">
				<button class="btn btn-primary" on:click={addTestNotification}> 일반 알림 추가 </button>

				<button class="btn btn-warning" on:click={addUrgentNotification}> 긴급 알림 추가 </button>

				<button class="btn btn-error" on:click={clearAllNotifications}> 모든 알림 삭제 </button>

				<button class="btn btn-success" on:click={restoreExampleData}> 예제 데이터 복원 </button>
			</div>

			<!-- 기능 설명 -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div class="card bg-base-200">
					<div class="card-body">
						<h3 class="card-title text-lg">주요 기능</h3>
						<ul class="list-disc list-inside space-y-1 text-sm">
							<li>실시간 알림 표시</li>
							<li>읽음/읽지 않음 상태 관리</li>
							<li>우선순위별 스타일링</li>
							<li>필터링 (전체/읽지 않음/읽음)</li>
							<li>페이지네이션</li>
							<li>알림 삭제</li>
							<li>액션 URL 지원</li>
						</ul>
					</div>
				</div>

				<div class="card bg-base-200">
					<div class="card-body">
						<h3 class="card-title text-lg">우선순위별 스타일</h3>
						<div class="space-y-2 text-sm">
							<div class="flex items-center gap-2">
								<div class="w-4 h-4 bg-success rounded"></div>
								<span>낮음 (Low) - 초록색</span>
							</div>
							<div class="flex items-center gap-2">
								<div class="w-4 h-4 bg-info rounded"></div>
								<span>보통 (Normal) - 파란색</span>
							</div>
							<div class="flex items-center gap-2">
								<div class="w-4 h-4 bg-warning rounded"></div>
								<span>높음 (High) - 주황색</span>
							</div>
							<div class="flex items-center gap-2">
								<div class="w-4 h-4 bg-error rounded"></div>
								<span>긴급 (Urgent) - 빨간색</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 사용법 -->
			<div class="mt-6">
				<h3 class="text-lg font-semibold mb-3">사용법</h3>
				<div class="mockup-code">
					<pre><code
							>{`<script>
  import NotificationCenter from '$components/NotificationCenter.svelte';
</script>

<!-- 헤더나 네비게이션 바에 추가 -->
<div class="navbar">
  <div class="navbar-end">
    <NotificationCenter />
  </div>
</div>`}</code
						></pre>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- 알림 센터 컴포넌트 -->
<div class="fixed top-4 right-4 z-50">
	<NotificationCenter />
</div>

<style>
	:global(body) {
		padding-top: 0;
	}
</style>
