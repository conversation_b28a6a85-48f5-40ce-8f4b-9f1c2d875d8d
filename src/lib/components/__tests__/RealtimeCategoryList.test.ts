/**
 * 실시간 카테고리 목록 컴포넌트 테스트
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/svelte';
import RealtimeCategoryList from '../RealtimeCategoryList.svelte';
import { categoryStore } from '$lib/stores/dataStores';
import { highlightedItemsStore, updatedItemsStore } from '$lib/stores/uiStores';

// 테스트 데이터
const mockCategoryData = {
	cate4: [
		{
			id: 1,
			name: '전자제품',
			code: 'ELEC001',
			description: '전자제품 카테고리',
			status: 'active' as const
		},
		{
			id: 2,
			name: '가전제품',
			code: 'APPL001',
			description: '가전제품 카테고리',
			status: 'active' as const
		},
		{
			id: 3,
			name: '컴퓨터',
			code: 'COMP001',
			description: '컴퓨터 관련 제품',
			status: 'inactive' as const
		}
	],
	cate5: [
		{
			id: 11,
			name: '노트북',
			code: 'LAPT001',
			description: '노트북 컴퓨터',
			status: 'active' as const
		},
		{
			id: 12,
			name: '데스크톱',
			code: 'DESK001',
			description: '데스크톱 컴퓨터',
			status: 'active' as const
		}
	],
	lastUpdated: '2024-01-01T00:00:00Z',
	totalCount: 5
};

describe('RealtimeCategoryList', () => {
	beforeEach(() => {
		// 스토어 초기화
		categoryStore.set(mockCategoryData);
		highlightedItemsStore.set(new Set());
		updatedItemsStore.set(new Set());
	});

	it('카테고리 목록을 올바르게 렌더링한다', () => {
		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400
			}
		});

		// 헤더 확인
		expect(screen.getByText('4차 카테고리')).toBeInTheDocument();
		expect(screen.getByText('3개')).toBeInTheDocument();

		// 카테고리 항목들 확인
		expect(screen.getByText('전자제품')).toBeInTheDocument();
		expect(screen.getByText('가전제품')).toBeInTheDocument();
		expect(screen.getByText('컴퓨터')).toBeInTheDocument();
	});

	it('카테고리 타입 변경 시 올바른 데이터를 표시한다', () => {
		const { component } = render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate5',
				containerHeight: 400
			}
		});

		// 5차 카테고리 확인
		expect(screen.getByText('5차 카테고리')).toBeInTheDocument();
		expect(screen.getByText('2개')).toBeInTheDocument();
		expect(screen.getByText('노트북')).toBeInTheDocument();
		expect(screen.getByText('데스크톱')).toBeInTheDocument();
	});

	it('검색 기능이 올바르게 작동한다', () => {
		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				searchQuery: '전자'
			}
		});

		// 검색 결과 확인
		expect(screen.getByText('1개')).toBeInTheDocument();
		expect(screen.getByText('전자제품')).toBeInTheDocument();
		expect(screen.queryByText('가전제품')).not.toBeInTheDocument();
	});

	it('빈 상태를 올바르게 표시한다', () => {
		// 빈 데이터로 설정
		categoryStore.set({
			cate4: [],
			cate5: [],
			lastUpdated: null,
			totalCount: 0
		});

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400
			}
		});

		expect(screen.getByText('카테고리가 없습니다')).toBeInTheDocument();
		expect(screen.getByText('등록된 카테고리가 없습니다.')).toBeInTheDocument();
	});

	it('검색 결과가 없을 때 빈 상태를 표시한다', () => {
		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				searchQuery: '존재하지않는검색어'
			}
		});

		expect(screen.getByText('카테고리가 없습니다')).toBeInTheDocument();
		expect(screen.getByText('검색 조건에 맞는 카테고리가 없습니다.')).toBeInTheDocument();
	});

	it('항목 클릭 이벤트가 올바르게 발생한다', async () => {
		const mockOnItemClick = vi.fn();

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				onItemClick: mockOnItemClick
			}
		});

		// 첫 번째 항목 클릭
		const firstItem = screen.getByText('전자제품').closest('.category-item');
		expect(firstItem).toBeInTheDocument();

		if (firstItem) {
			await fireEvent.click(firstItem);
			expect(mockOnItemClick).toHaveBeenCalledWith(
				expect.objectContaining({
					id: 1,
					name: '전자제품',
					code: 'ELEC001'
				})
			);
		}
	});

	it('항목 더블클릭 이벤트가 올바르게 발생한다', async () => {
		const mockOnItemDoubleClick = vi.fn();

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				onItemDoubleClick: mockOnItemDoubleClick
			}
		});

		// 첫 번째 항목 더블클릭
		const firstItem = screen.getByText('전자제품').closest('.category-item');
		expect(firstItem).toBeInTheDocument();

		if (firstItem) {
			await fireEvent.dblClick(firstItem);
			expect(mockOnItemDoubleClick).toHaveBeenCalledWith(
				expect.objectContaining({
					id: 1,
					name: '전자제품',
					code: 'ELEC001'
				})
			);
		}
	});

	it('하이라이트 애니메이션이 올바르게 적용된다', () => {
		// 하이라이트된 항목 설정
		highlightedItemsStore.set(new Set([1]));

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				showAnimations: true
			}
		});

		// 하이라이트된 항목 확인
		const highlightedItem = screen.getByText('전자제품').closest('.category-item');
		expect(highlightedItem).toHaveClass('highlighted');
	});

	it('업데이트 애니메이션이 올바르게 적용된다', () => {
		// 업데이트된 항목 설정
		updatedItemsStore.set(new Set([2]));

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				showAnimations: true
			}
		});

		// 업데이트된 항목 확인
		const updatedItem = screen.getByText('가전제품').closest('.category-item');
		expect(updatedItem).toHaveClass('updated');
	});

	it('상태별 배지가 올바르게 표시된다', () => {
		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400
			}
		});

		// 활성 상태 배지 확인
		const activeItems = screen.getAllByText('활성');
		expect(activeItems).toHaveLength(2); // 전자제품, 가전제품

		// 비활성 상태 배지 확인
		const inactiveItems = screen.getAllByText('비활성');
		expect(inactiveItems).toHaveLength(1); // 컴퓨터
	});

	it('키보드 네비게이션이 작동한다', async () => {
		const mockOnItemClick = vi.fn();

		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 400,
				onItemClick: mockOnItemClick
			}
		});

		// 첫 번째 항목에 포커스
		const firstItem = screen.getByText('전자제품').closest('.category-item');
		expect(firstItem).toBeInTheDocument();

		if (firstItem) {
			firstItem.focus();

			// Enter 키 누르기
			await fireEvent.keyDown(firstItem, { key: 'Enter' });
			expect(mockOnItemClick).toHaveBeenCalledWith(
				expect.objectContaining({
					id: 1,
					name: '전자제품'
				})
			);
		}
	});

	it('가상 스크롤링 계산이 올바르게 작동한다', () => {
		render(RealtimeCategoryList, {
			props: {
				categoryType: 'cate4',
				containerHeight: 120, // 2개 항목만 보이도록 설정
				itemHeight: 60
			}
		});

		// 모든 항목이 DOM에 있지 않아야 함 (가상 스크롤링)
		// 실제로는 가시 영역의 항목들만 렌더링됨
		const items = screen.getAllByText(/제품/);
		expect(items.length).toBeLessThanOrEqual(3); // 버퍼 포함하여 최대 3개
	});
});
