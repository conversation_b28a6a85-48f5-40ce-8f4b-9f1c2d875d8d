import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { get } from 'svelte/store';
import { sseConnectionState, updateConnectionStatus } from '../../services';
import { sseStatsStore } from '../../stores';
import * as sseConnection from '../../services/sseConnection';

// SSE 연결 서비스 모킹
vi.mock('$lib/services/sseConnection', () => ({
	manualReconnect: vi.fn(),
	isOfflineMode: vi.fn(() => false)
}));

describe('ConnectionStatus 컴포넌트 로직', () => {
	beforeEach(() => {
		// 각 테스트 전에 상태 초기화
		updateConnectionStatus('disconnected');
		sseStatsStore.set({
			totalMessagesReceived: 0,
			notificationsReceived: 0,
			dataUpdatesReceived: 0,
			errorsCount: 0,
			lastMessageAt: null,
			connectionUptime: 0,
			averageLatency: 0
		});

		// navigator.onLine 모킹
		Object.defineProperty(navigator, 'onLine', {
			writable: true,
			value: true
		});

		// 모킹 함수 초기화
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('연결 상태가 올바르게 업데이트된다', () => {
		updateConnectionStatus('connected');
		const state = get(sseConnectionState);
		expect(state.status).toBe('connected');
	});

	it('오류 상태와 메시지가 올바르게 설정된다', () => {
		updateConnectionStatus('error', '테스트 오류 메시지');
		const state = get(sseConnectionState);
		expect(state.status).toBe('error');
		expect(state.lastError).toBe('테스트 오류 메시지');
	});

	it('재연결 시도 횟수가 올바르게 추적된다', () => {
		// 재연결 상태로 설정하고 시도 횟수 증가
		sseConnectionState.update((state) => ({
			...state,
			status: 'reconnecting',
			reconnectAttempts: 3
		}));

		const state = get(sseConnectionState);
		expect(state.status).toBe('reconnecting');
		expect(state.reconnectAttempts).toBe(3);
	});

	it('연결 품질이 올바르게 설정된다', () => {
		sseConnectionState.update((state) => ({
			...state,
			status: 'connected',
			connectionQuality: 'excellent',
			latency: 50
		}));

		const state = get(sseConnectionState);
		expect(state.connectionQuality).toBe('excellent');
		expect(state.latency).toBe(50);
	});

	it('SSE 통계가 올바르게 업데이트된다', () => {
		const testStats = {
			totalMessagesReceived: 10,
			notificationsReceived: 5,
			dataUpdatesReceived: 3,
			errorsCount: 2,
			lastMessageAt: new Date().toISOString(),
			connectionUptime: 0,
			averageLatency: 0
		};

		sseStatsStore.set(testStats);
		const stats = get(sseStatsStore);

		expect(stats.totalMessagesReceived).toBe(10);
		expect(stats.notificationsReceived).toBe(5);
		expect(stats.dataUpdatesReceived).toBe(3);
		expect(stats.errorsCount).toBe(2);
	});

	it('수동 재연결 함수가 호출된다', async () => {
		const mockManualReconnect = vi.mocked(sseConnection.manualReconnect);
		mockManualReconnect.mockResolvedValue();

		await sseConnection.manualReconnect();
		expect(mockManualReconnect).toHaveBeenCalledOnce();
	});

	it('오프라인 모드 확인이 작동한다', () => {
		const mockIsOfflineMode = vi.mocked(sseConnection.isOfflineMode);
		mockIsOfflineMode.mockReturnValue(true);

		const isOffline = sseConnection.isOfflineMode();
		expect(isOffline).toBe(true);
	});

	it('연결 상태별 텍스트 변환이 올바르다', () => {
		const getStatusText = (status: string, reconnectAttempts: number = 0) => {
			switch (status) {
				case 'connected':
					return '연결됨';
				case 'connecting':
					return '연결 중...';
				case 'reconnecting':
					return `재연결 중... (${reconnectAttempts}회 시도)`;
				case 'error':
					return '연결 오류';
				case 'closed':
					return '연결 종료';
				case 'disconnected':
				default:
					return '연결 안됨';
			}
		};

		expect(getStatusText('connected')).toBe('연결됨');
		expect(getStatusText('connecting')).toBe('연결 중...');
		expect(getStatusText('reconnecting', 3)).toBe('재연결 중... (3회 시도)');
		expect(getStatusText('error')).toBe('연결 오류');
		expect(getStatusText('disconnected')).toBe('연결 안됨');
	});

	it('연결 품질 아이콘 매핑이 올바르다', () => {
		const getQualityIcon = (quality: string) => {
			switch (quality) {
				case 'excellent':
					return '🟢';
				case 'good':
					return '🟡';
				case 'poor':
					return '🔴';
				default:
					return '⚪';
			}
		};

		expect(getQualityIcon('excellent')).toBe('🟢');
		expect(getQualityIcon('good')).toBe('🟡');
		expect(getQualityIcon('poor')).toBe('🔴');
		expect(getQualityIcon('unknown')).toBe('⚪');
	});

	it('시간 포맷팅이 올바르게 작동한다', () => {
		const formatTime = (date: Date | null) => {
			if (!date) return '없음';
			return new Intl.DateTimeFormat('ko-KR', {
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			}).format(date);
		};

		expect(formatTime(null)).toBe('없음');

		const testDate = new Date('2024-01-01T12:30:45');
		const formatted = formatTime(testDate);
		expect(formatted).toMatch(/\d{2}:\d{2}:\d{2}/);
	});

	it('지연시간 포맷팅이 올바르게 작동한다', () => {
		const formatLatency = (latency: number | null) => {
			if (latency === null) return '측정 안됨';
			return `${latency}ms`;
		};

		expect(formatLatency(null)).toBe('측정 안됨');
		expect(formatLatency(150)).toBe('150ms');
		expect(formatLatency(0)).toBe('0ms');
	});

	it('연결 상태별 CSS 클래스가 올바르다', () => {
		const getStatusClass = (status: string) => {
			switch (status) {
				case 'connected':
					return 'badge-success';
				case 'connecting':
				case 'reconnecting':
					return 'badge-warning';
				case 'error':
					return 'badge-error';
				case 'closed':
				case 'disconnected':
				default:
					return 'badge-neutral';
			}
		};

		expect(getStatusClass('connected')).toBe('badge-success');
		expect(getStatusClass('connecting')).toBe('badge-warning');
		expect(getStatusClass('reconnecting')).toBe('badge-warning');
		expect(getStatusClass('error')).toBe('badge-error');
		expect(getStatusClass('disconnected')).toBe('badge-neutral');
	});

	it('재연결 가능 상태 확인이 올바르다', () => {
		const canReconnect = (isOnline: boolean, status: string) => {
			return isOnline && !['connecting', 'reconnecting'].includes(status);
		};

		expect(canReconnect(true, 'error')).toBe(true);
		expect(canReconnect(true, 'disconnected')).toBe(true);
		expect(canReconnect(true, 'connecting')).toBe(false);
		expect(canReconnect(true, 'reconnecting')).toBe(false);
		expect(canReconnect(false, 'error')).toBe(false);
	});
});
