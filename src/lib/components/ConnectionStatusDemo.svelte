<script lang="ts">
	import { onMount } from 'svelte';
	import ConnectionStatus from './ConnectionStatus.svelte';
	import { connect, disconnect, manualReconnect } from '$lib/services/sseConnection';
	import { sseConnectionState, updateConnectionStatus } from '$lib/services/sseConnectionState';

	let isInitialized = false;

	/**
	 * SSE 연결 시작
	 */
	async function startConnection(): Promise<void> {
		try {
			console.log('SSE 연결 시작...');
			await connect();
		} catch (error) {
			console.error('SSE 연결 실패:', error);
		}
	}

	/**
	 * SSE 연결 종료
	 */
	function stopConnection(): void {
		console.log('SSE 연결 종료...');
		disconnect();
	}

	/**
	 * 연결 상태 시뮬레이션 (테스트용)
	 */
	function simulateConnectionStates(): void {
		console.log('연결 상태 시뮬레이션 시작...');

		// 연결 중 상태
		updateConnectionStatus('connecting');

		setTimeout(() => {
			// 연결됨 상태
			updateConnectionStatus('connected');
		}, 2000);

		setTimeout(() => {
			// 오류 상태
			updateConnectionStatus('error', '테스트 오류 메시지');
		}, 5000);

		setTimeout(() => {
			// 재연결 중 상태
			updateConnectionStatus('reconnecting');
		}, 8000);

		setTimeout(() => {
			// 다시 연결됨 상태
			updateConnectionStatus('connected');
		}, 11000);
	}

	/**
	 * 네트워크 상태 시뮬레이션 (테스트용)
	 */
	function simulateNetworkToggle(): void {
		// 오프라인 이벤트 발생
		window.dispatchEvent(new Event('offline'));

		setTimeout(() => {
			// 온라인 이벤트 발생
			window.dispatchEvent(new Event('online'));
		}, 3000);
	}

	onMount(() => {
		isInitialized = true;
		console.log('연결 상태 데모 컴포넌트 초기화 완료');
	});
</script>

<div class="connection-demo-container p-6 max-w-2xl mx-auto">
	<h2 class="text-2xl font-bold mb-6">SSE 연결 상태 관리 데모</h2>

	<!-- 연결 상태 컴포넌트 -->
	<div class="mb-6">
		<h3 class="text-lg font-semibold mb-3">현재 연결 상태</h3>
		<ConnectionStatus />
	</div>

	<!-- 제어 버튼들 -->
	<div class="mb-6">
		<h3 class="text-lg font-semibold mb-3">연결 제어</h3>
		<div class="flex flex-wrap gap-2">
			<button class="btn btn-primary" on:click={startConnection}> SSE 연결 시작 </button>
			<button class="btn btn-secondary" on:click={stopConnection}> SSE 연결 종료 </button>
			<button class="btn btn-accent" on:click={manualReconnect}> 수동 재연결 </button>
		</div>
	</div>

	<!-- 테스트 버튼들 -->
	<div class="mb-6">
		<h3 class="text-lg font-semibold mb-3">테스트 기능</h3>
		<div class="flex flex-wrap gap-2">
			<button class="btn btn-outline btn-warning" on:click={simulateConnectionStates}>
				연결 상태 시뮬레이션
			</button>
			<button class="btn btn-outline btn-info" on:click={simulateNetworkToggle}>
				네트워크 상태 토글
			</button>
		</div>
	</div>

	<!-- 현재 상태 정보 -->
	<div class="mb-6">
		<h3 class="text-lg font-semibold mb-3">상태 정보</h3>
		<div class="bg-base-200 p-4 rounded-lg">
			<pre class="text-sm overflow-auto">{JSON.stringify($sseConnectionState, null, 2)}</pre>
		</div>
	</div>

	<!-- 사용법 안내 -->
	<div class="alert alert-info">
		<div>
			<h4 class="font-semibold">사용법 안내</h4>
			<ul class="text-sm mt-2 space-y-1">
				<li>• "SSE 연결 시작" 버튼으로 실제 SSE 연결을 시작할 수 있습니다</li>
				<li>• "연결 상태 시뮬레이션" 버튼으로 다양한 연결 상태를 테스트할 수 있습니다</li>
				<li>• 연결 상태 컴포넌트의 "▼" 버튼을 클릭하면 세부 정보를 볼 수 있습니다</li>
				<li>• 수동 재연결 버튼으로 연결 문제 시 재연결을 시도할 수 있습니다</li>
			</ul>
		</div>
	</div>
</div>

<style>
	.connection-demo-container {
		@apply space-y-4;
	}

	pre {
		@apply whitespace-pre-wrap break-words;
	}
</style>
