<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { notifications, type NotificationState } from '$lib/stores/notificationStore';
	import NotificationToast from '$components/UI/NotificationToast.svelte';

	interface Props {
		position?:
			| 'top-right'
			| 'top-left'
			| 'bottom-right'
			| 'bottom-left'
			| 'top-center'
			| 'bottom-center';
		maxToasts?: number;
		stackDirection?: 'up' | 'down';
		spacing?: number;
		autoClose?: boolean;
		showProgress?: boolean;
		onToastClick?: (notification: NotificationState) => void;
		onActionClick?: (notification: NotificationState) => void;
	}

	let {
		position = 'top-right',
		maxToasts = 5,
		stackDirection = 'down',
		spacing = 12,
		autoClose = true,
		showProgress = true,
		onToastClick,
		onActionClick
	}: Props = $props();

	let visibleToasts = $state<NotificationState[]>([]);
	let toastQueue = $state<NotificationState[]>([]);
	let unsubscribe: (() => void) | null = null;

	onMount(() => {
		// 알림 스토어 구독
		unsubscribe = notifications.subscribe((allNotifications) => {
			updateVisibleToasts(allNotifications);
		});
	});

	onDestroy(() => {
		if (unsubscribe) {
			unsubscribe();
		}
	});

	function updateVisibleToasts(allNotifications: NotificationState[]) {
		// 최신 알림부터 정렬
		const sortedNotifications = [...allNotifications].sort(
			(a, b) => b.receivedAt.getTime() - a.receivedAt.getTime()
		);

		// 표시할 알림과 대기열 분리
		const newVisibleToasts = sortedNotifications.slice(0, maxToasts);
		const newQueue = sortedNotifications.slice(maxToasts);

		// 긴급 알림은 항상 최상단에 표시
		const urgentToasts = newVisibleToasts.filter((n) => n.priority === 'urgent');
		const normalToasts = newVisibleToasts.filter((n) => n.priority !== 'urgent');

		visibleToasts = [...urgentToasts, ...normalToasts];
		toastQueue = newQueue;
	}

	function handleToastClose(notificationId: number) {
		// 토스트가 닫힐 때 대기열에서 다음 알림 표시
		if (toastQueue.length > 0) {
			const nextToast = toastQueue[0];
			toastQueue = toastQueue.slice(1);

			// 약간의 지연 후 다음 토스트 표시
			setTimeout(() => {
				visibleToasts = [...visibleToasts, nextToast];
			}, 200);
		}
	}

	function getToastStyle(index: number): string {
		const offset = index * (60 + spacing); // 토스트 높이 + 간격

		if (position.includes('top')) {
			return stackDirection === 'down'
				? `top: ${20 + offset}px;`
				: `top: ${20}px; transform: translateY(-${offset}px);`;
		} else {
			return stackDirection === 'up'
				? `bottom: ${20 + offset}px;`
				: `bottom: ${20}px; transform: translateY(${offset}px);`;
		}
	}

	function getZIndex(index: number): number {
		// 긴급 알림은 더 높은 z-index
		const baseZIndex = 9999;
		const toast = visibleToasts[index];

		if (toast?.priority === 'urgent') {
			return baseZIndex + 1000 - index;
		}

		return baseZIndex - index;
	}
</script>

<!-- 토스트 컨테이너 -->
<div class="toast-container-wrapper" role="region" aria-label="알림 영역" aria-live="polite">
	{#each visibleToasts as toast, index (toast.id)}
		<div class="toast-wrapper" style="{getToastStyle(index)} z-index: {getZIndex(index)};">
			<NotificationToast
				notification={toast}
				{position}
				{autoClose}
				{showProgress}
				onClose={handleToastClose}
				onClick={onToastClick}
				{onActionClick}
			/>
		</div>
	{/each}
</div>

<!-- 대기열 표시 (선택적) -->
{#if toastQueue.length > 0}
	<div
		class="queue-indicator"
		class:queue-top={position.includes('top')}
		class:queue-bottom={position.includes('bottom')}
		class:queue-right={position.includes('right')}
		class:queue-left={position.includes('left')}
		class:queue-center={position.includes('center')}
	>
		<div class="queue-content">
			<span class="queue-text">+{toastQueue.length}개 더</span>
		</div>
	</div>
{/if}

<style>
	.toast-container-wrapper {
		position: fixed;
		pointer-events: none;
		z-index: 9999;
	}

	.toast-wrapper {
		position: fixed;
		pointer-events: auto;
		transition: all 0.3s ease-in-out;
	}

	/* 대기열 표시 */
	.queue-indicator {
		position: fixed;
		z-index: 9998;
		pointer-events: none;
	}

	.queue-indicator.queue-top.queue-right {
		top: 20px;
		right: 20px;
		transform: translateY(-40px);
	}

	.queue-indicator.queue-top.queue-left {
		top: 20px;
		left: 20px;
		transform: translateY(-40px);
	}

	.queue-indicator.queue-bottom.queue-right {
		bottom: 20px;
		right: 20px;
		transform: translateY(40px);
	}

	.queue-indicator.queue-bottom.queue-left {
		bottom: 20px;
		left: 20px;
		transform: translateY(40px);
	}

	.queue-indicator.queue-top.queue-center {
		top: 20px;
		left: 50%;
		transform: translate(-50%, -40px);
	}

	.queue-indicator.queue-bottom.queue-center {
		bottom: 20px;
		left: 50%;
		transform: translate(-50%, 40px);
	}

	.queue-content {
		background: rgba(0, 0, 0, 0.8);
		color: white;
		padding: 6px 12px;
		border-radius: 20px;
		font-size: 12px;
		font-weight: 500;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.1);
	}

	.queue-text {
		white-space: nowrap;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.queue-indicator.queue-top.queue-right,
		.queue-indicator.queue-top.queue-left,
		.queue-indicator.queue-top.queue-center {
			top: 10px;
			left: 50%;
			right: auto;
			transform: translate(-50%, -40px);
		}

		.queue-indicator.queue-bottom.queue-right,
		.queue-indicator.queue-bottom.queue-left,
		.queue-indicator.queue-bottom.queue-center {
			bottom: 10px;
			left: 50%;
			right: auto;
			transform: translate(-50%, 40px);
		}
	}

	/* 다크 모드 */
	@media (prefers-color-scheme: dark) {
		.queue-content {
			background: rgba(31, 41, 55, 0.9);
			border-color: rgba(75, 85, 99, 0.3);
		}
	}

	/* 접근성 */
	@media (prefers-reduced-motion: reduce) {
		.toast-wrapper {
			transition: none;
		}
	}
</style>
