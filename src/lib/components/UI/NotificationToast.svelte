<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { fade, fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import Icon from 'svelte-awesome';
	import {
		faXmark,
		faExclamationTriangle,
		faInfoCircle,
		faCheckCircle,
		faBell,
		faExternalLink
	} from '@fortawesome/free-solid-svg-icons';
	import type { NotificationState } from '$lib/stores/notificationStore';
	import { markAsRead, removeNotification } from '$lib/stores/notificationStore';

	interface Props {
		notification: NotificationState;
		position?:
			| 'top-right'
			| 'top-left'
			| 'bottom-right'
			| 'bottom-left'
			| 'top-center'
			| 'bottom-center';
		autoClose?: boolean;
		showProgress?: boolean;
		onClose?: (id: number) => void;
		onClick?: (notification: NotificationState) => void;
		onActionClick?: (notification: NotificationState) => void;
	}

	let {
		notification,
		position = 'top-right',
		autoClose = true,
		showProgress = true,
		onClose,
		onClick,
		onActionClick
	}: Props = $props();

	let toastElement = $state<HTMLDivElement>();
	let progressElement = $state<HTMLDivElement>();
	let timeoutId: number | null = null;
	let progressAnimationId: number | null = null;
	let isVisible = $state(false);
	let isHovered = $state(false);
	let progressWidth = $state(100);

	// 우선순위별 자동 닫기 시간 (밀리초)
	const autoCloseDelays = {
		low: 3000,
		normal: 5000,
		high: 8000,
		urgent: 0 // 수동으로만 닫기
	};

	// 우선순위별 아이콘
	const priorityIcons = {
		low: faInfoCircle,
		normal: faBell,
		high: faExclamationTriangle,
		urgent: faExclamationTriangle
	};

	// 우선순위별 CSS 클래스
	const priorityClasses = {
		low: 'toast-low',
		normal: 'toast-normal',
		high: 'toast-high',
		urgent: 'toast-urgent'
	};

	// 위치별 CSS 클래스
	const positionClasses = {
		'top-right': 'toast-top-right',
		'top-left': 'toast-top-left',
		'bottom-right': 'toast-bottom-right',
		'bottom-left': 'toast-bottom-left',
		'top-center': 'toast-top-center',
		'bottom-center': 'toast-bottom-center'
	};

	onMount(() => {
		// 애니메이션을 위해 약간의 지연 후 표시
		setTimeout(() => {
			isVisible = true;
		}, 50);

		// 자동 닫기 설정
		if (autoClose && notification.priority !== 'urgent') {
			const delay = autoCloseDelays[notification.priority];
			if (delay > 0) {
				startAutoClose(delay);
			}
		}

		// 읽지 않은 알림인 경우 자동으로 읽음 처리 (5초 후)
		if (!notification.isRead) {
			setTimeout(() => {
				if (!notification.isRead) {
					markAsRead(notification.id);
				}
			}, 5000);
		}
	});

	onDestroy(() => {
		clearTimeouts();
	});

	function startAutoClose(delay: number) {
		if (showProgress) {
			startProgressAnimation(delay);
		}

		timeoutId = window.setTimeout(() => {
			if (!isHovered) {
				handleClose();
			}
		}, delay);
	}

	function startProgressAnimation(duration: number) {
		const startTime = Date.now();

		function animate() {
			const elapsed = Date.now() - startTime;
			const remaining = Math.max(0, duration - elapsed);
			progressWidth = (remaining / duration) * 100;

			if (remaining > 0 && !isHovered) {
				progressAnimationId = requestAnimationFrame(animate);
			}
		}

		progressAnimationId = requestAnimationFrame(animate);
	}

	function clearTimeouts() {
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
		if (progressAnimationId) {
			cancelAnimationFrame(progressAnimationId);
			progressAnimationId = null;
		}
	}

	function handleMouseEnter() {
		isHovered = true;
		clearTimeouts();
		if (showProgress) {
			progressWidth = 100; // 진행바 정지
		}
	}

	function handleMouseLeave() {
		isHovered = false;
		// 마우스가 벗어나면 다시 자동 닫기 시작
		if (autoClose && notification.priority !== 'urgent') {
			const delay = autoCloseDelays[notification.priority];
			if (delay > 0) {
				startAutoClose(delay);
			}
		}
	}

	function handleClose() {
		isVisible = false;
		// 애니메이션 완료 후 제거
		setTimeout(() => {
			if (onClose) {
				onClose(notification.id);
			} else {
				removeNotification(notification.id);
			}
		}, 300);
	}

	function handleClick() {
		// 읽음 처리
		if (!notification.isRead) {
			markAsRead(notification.id);
		}

		if (onClick) {
			onClick(notification);
		} else if (notification.actionUrl) {
			// 기본 동작: 액션 URL이 있으면 새 창에서 열기
			window.open(notification.actionUrl, '_blank');
		}
	}

	function handleActionClick(event: Event) {
		event.stopPropagation();

		if (onActionClick) {
			onActionClick(notification);
		} else if (notification.actionUrl) {
			window.open(notification.actionUrl, '_blank');
		}

		// 액션 클릭 후 토스트 닫기
		handleClose();
	}

	function formatTime(date: Date): string {
		const now = new Date();
		const diff = now.getTime() - date.getTime();

		if (diff < 60000) {
			// 1분 미만
			return '방금 전';
		} else if (diff < 3600000) {
			// 1시간 미만
			return `${Math.floor(diff / 60000)}분 전`;
		} else if (diff < 86400000) {
			// 24시간 미만
			return `${Math.floor(diff / 3600000)}시간 전`;
		} else {
			return date.toLocaleDateString('ko-KR', {
				month: 'short',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit'
			});
		}
	}
</script>

{#if isVisible}
	<div
		bind:this={toastElement}
		class="toast-container {positionClasses[position]} {priorityClasses[notification.priority]}"
		class:unread={!notification.isRead}
		role="button"
		aria-live="polite"
		aria-atomic="true"
		aria-label="알림: {notification.title}"
		transition:fly={{
			x: position.includes('right') ? 300 : position.includes('left') ? -300 : 0,
			y: position.includes('top') ? -100 : position.includes('bottom') ? 100 : 0,
			duration: 300,
			easing: quintOut
		}}
		onmouseenter={handleMouseEnter}
		onmouseleave={handleMouseLeave}
		onclick={handleClick}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				handleClick();
			}
		}}
		tabindex="0"
	>
		<!-- 진행바 (자동 닫기 시) -->
		{#if showProgress && autoClose && notification.priority !== 'urgent'}
			<div class="progress-bar">
				<div
					bind:this={progressElement}
					class="progress-fill"
					style="width: {progressWidth}%"
				></div>
			</div>
		{/if}

		<!-- 메인 콘텐츠 -->
		<div class="toast-content">
			<!-- 아이콘 -->
			<div class="toast-icon">
				<Icon data={priorityIcons[notification.priority]} class="w-5 h-5" />
			</div>

			<!-- 텍스트 콘텐츠 -->
			<div class="toast-text">
				<div class="toast-title">
					{notification.title}
					{#if !notification.isRead}
						<span class="unread-indicator"></span>
					{/if}
				</div>
				<div class="toast-message">
					{@html notification.message}
				</div>
				<div class="toast-time">
					{formatTime(notification.receivedAt)}
				</div>
			</div>

			<!-- 액션 버튼 -->
			{#if notification.actionUrl && notification.actionText}
				<div class="toast-actions">
					<button class="action-button" onclick={handleActionClick} title={notification.actionText}>
						<Icon data={faExternalLink} class="w-4 h-4" />
						<span>{notification.actionText}</span>
					</button>
				</div>
			{/if}

			<!-- 닫기 버튼 -->
			<button
				class="close-button"
				onclick={(e) => {
					e.stopPropagation();
					handleClose();
				}}
				title="알림 닫기"
			>
				<Icon data={faXmark} class="w-4 h-4" />
			</button>
		</div>
	</div>
{/if}

<style>
	.toast-container {
		position: fixed;
		z-index: 9999;
		min-width: 320px;
		max-width: 480px;
		background: white;
		border-radius: 12px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
		border: 1px solid #e5e7eb;
		overflow: hidden;
		cursor: pointer;
		transition: all 0.2s ease-in-out;
	}

	.toast-container:hover {
		transform: translateY(-2px);
		box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
	}

	/* 위치별 스타일 */
	.toast-top-right {
		top: 20px;
		right: 20px;
	}

	.toast-top-left {
		top: 20px;
		left: 20px;
	}

	.toast-bottom-right {
		bottom: 20px;
		right: 20px;
	}

	.toast-bottom-left {
		bottom: 20px;
		left: 20px;
	}

	.toast-top-center {
		top: 20px;
		left: 50%;
		transform: translateX(-50%);
	}

	.toast-bottom-center {
		bottom: 20px;
		left: 50%;
		transform: translateX(-50%);
	}

	/* 우선순위별 스타일 */
	.toast-low {
		border-left: 4px solid #6b7280;
		background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
	}

	.toast-normal {
		border-left: 4px solid #3b82f6;
		background: linear-gradient(135deg, #dbeafe 0%, #ffffff 100%);
	}

	.toast-high {
		border-left: 4px solid #f59e0b;
		background: linear-gradient(135deg, #fef3c7 0%, #ffffff 100%);
		animation: pulse-warning 2s infinite;
	}

	.toast-urgent {
		border-left: 4px solid #dc2626;
		background: linear-gradient(135deg, #fecaca 0%, #ffffff 100%);
		animation: pulse-urgent 1s infinite;
		box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
	}

	/* 읽지 않은 알림 강조 */
	.toast-container.unread {
		border-width: 2px;
		box-shadow:
			0 10px 25px rgba(0, 0, 0, 0.2),
			0 0 0 2px rgba(59, 130, 246, 0.3);
	}

	/* 진행바 */
	.progress-bar {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 3px;
		background: rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: linear-gradient(90deg, #3b82f6, #1d4ed8);
		transition: width 0.1s linear;
	}

	.toast-urgent .progress-fill {
		background: linear-gradient(90deg, #dc2626, #991b1b);
	}

	.toast-high .progress-fill {
		background: linear-gradient(90deg, #f59e0b, #d97706);
	}

	/* 메인 콘텐츠 */
	.toast-content {
		display: flex;
		align-items: flex-start;
		padding: 16px;
		gap: 12px;
		position: relative;
	}

	.toast-icon {
		flex-shrink: 0;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		margin-top: 2px;
	}

	.toast-low .toast-icon {
		color: #6b7280;
		background: rgba(107, 114, 128, 0.1);
	}

	.toast-normal .toast-icon {
		color: #3b82f6;
		background: rgba(59, 130, 246, 0.1);
	}

	.toast-high .toast-icon {
		color: #f59e0b;
		background: rgba(245, 158, 11, 0.1);
	}

	.toast-urgent .toast-icon {
		color: #dc2626;
		background: rgba(220, 38, 38, 0.1);
	}

	.toast-text {
		flex: 1;
		min-width: 0;
	}

	.toast-title {
		font-weight: 600;
		font-size: 14px;
		color: #111827;
		margin-bottom: 4px;
		display: flex;
		align-items: center;
		gap: 6px;
	}

	.toast-message {
		font-size: 13px;
		color: #4b5563;
		line-height: 1.4;
		margin-bottom: 6px;
		word-break: break-word;
	}

	.toast-time {
		font-size: 11px;
		color: #9ca3af;
		font-weight: 500;
	}

	.unread-indicator {
		width: 6px;
		height: 6px;
		background: #3b82f6;
		border-radius: 50%;
		flex-shrink: 0;
		animation: pulse-dot 2s infinite;
	}

	/* 액션 버튼 */
	.toast-actions {
		flex-shrink: 0;
		margin-left: 8px;
	}

	.action-button {
		display: flex;
		align-items: center;
		gap: 4px;
		padding: 6px 12px;
		background: #3b82f6;
		color: white;
		border: none;
		border-radius: 6px;
		font-size: 12px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease-in-out;
	}

	.action-button:hover {
		background: #2563eb;
		transform: translateY(-1px);
	}

	.toast-urgent .action-button {
		background: #dc2626;
	}

	.toast-urgent .action-button:hover {
		background: #b91c1c;
	}

	.toast-high .action-button {
		background: #f59e0b;
	}

	.toast-high .action-button:hover {
		background: #d97706;
	}

	/* 닫기 버튼 */
	.close-button {
		position: absolute;
		top: 8px;
		right: 8px;
		width: 24px;
		height: 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: transparent;
		border: none;
		border-radius: 50%;
		color: #9ca3af;
		cursor: pointer;
		transition: all 0.2s ease-in-out;
	}

	.close-button:hover {
		background: rgba(0, 0, 0, 0.1);
		color: #4b5563;
	}

	/* 애니메이션 */
	@keyframes pulse-warning {
		0%,
		100% {
			box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
		}
		50% {
			box-shadow:
				0 10px 25px rgba(0, 0, 0, 0.15),
				0 0 15px rgba(245, 158, 11, 0.4);
		}
	}

	@keyframes pulse-urgent {
		0%,
		100% {
			box-shadow:
				0 10px 25px rgba(0, 0, 0, 0.15),
				0 0 20px rgba(220, 38, 38, 0.3);
		}
		50% {
			box-shadow:
				0 10px 25px rgba(0, 0, 0, 0.15),
				0 0 30px rgba(220, 38, 38, 0.5);
		}
	}

	@keyframes pulse-dot {
		0%,
		100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	/* 다크 모드 지원 */
	@media (prefers-color-scheme: dark) {
		.toast-container {
			background: #1f2937;
			border-color: #374151;
			color: #f9fafb;
		}

		.toast-low {
			background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
		}

		.toast-normal {
			background: linear-gradient(135deg, #1e3a8a 0%, #1f2937 100%);
		}

		.toast-high {
			background: linear-gradient(135deg, #92400e 0%, #1f2937 100%);
		}

		.toast-urgent {
			background: linear-gradient(135deg, #991b1b 0%, #1f2937 100%);
		}

		.toast-title {
			color: #f9fafb;
		}

		.toast-message {
			color: #d1d5db;
		}

		.toast-time {
			color: #9ca3af;
		}

		.close-button:hover {
			background: rgba(255, 255, 255, 0.1);
			color: #d1d5db;
		}
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.toast-container {
			min-width: 280px;
			max-width: calc(100vw - 40px);
			margin: 0 20px;
		}

		.toast-top-right,
		.toast-top-left {
			top: 10px;
			right: 10px;
			left: 10px;
		}

		.toast-bottom-right,
		.toast-bottom-left {
			bottom: 10px;
			right: 10px;
			left: 10px;
		}

		.toast-top-center,
		.toast-bottom-center {
			left: 10px;
			right: 10px;
			transform: none;
		}

		.toast-content {
			padding: 12px;
		}

		.toast-title {
			font-size: 13px;
		}

		.toast-message {
			font-size: 12px;
		}
	}

	/* 접근성 개선 */
	@media (prefers-reduced-motion: reduce) {
		.toast-container {
			transition: none;
		}

		.toast-container:hover {
			transform: none;
		}

		.action-button:hover {
			transform: none;
		}

		.progress-fill {
			transition: none;
		}

		.toast-high {
			animation: none;
		}

		.toast-urgent {
			animation: none;
		}

		.unread-indicator {
			animation: none;
		}
	}

	/* 고대비 모드 지원 */
	@media (prefers-contrast: high) {
		.toast-container {
			border-width: 2px;
			border-color: #000;
		}

		.toast-title {
			font-weight: 700;
		}

		.action-button {
			border: 2px solid #000;
		}
	}
</style>
