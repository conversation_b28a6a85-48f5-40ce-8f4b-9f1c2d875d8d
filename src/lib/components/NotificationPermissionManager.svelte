<!--
  브라우저 알림 권한 관리 컴포넌트
  
  사용자에게 알림 권한 상태를 표시하고 권한 요청을 처리합니다.
-->

<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import {
		requestNotificationPermissionEnhanced as requestNotificationPermission,
		getNotificationPermissionStatusEnhanced as getNotificationPermissionStatus,
		getPermissionGuideMessage,
		openBrowserNotificationSettings,
		canRequestPermission,
		testNotification,
		startPermissionMonitoring,
		notificationPermissionStore,
		permissionRequestInProgressStore,
		alternativeNotificationModeStore,
		type PermissionGuideMessage
	} from '$lib/services/notifications/permissions';

	// 컴포넌트 props
	export let showCard = true;
	export let autoRequest = false;
	export let showTestButton = true;

	// 반응형 상태
	let permissionStatus: NotificationPermission = 'default';
	let guideMessage: PermissionGuideMessage;
	let requestInProgress = false;
	let alternativeMode = false;
	let cleanupMonitoring: (() => void) | null = null;

	// 스토어 구독
	const unsubscribePermission = notificationPermissionStore.subscribe((status) => {
		permissionStatus = status;
		guideMessage = getPermissionGuideMessage();
	});

	const unsubscribeRequestProgress = permissionRequestInProgressStore.subscribe((inProgress) => {
		requestInProgress = inProgress;
	});

	const unsubscribeAlternativeMode = alternativeNotificationModeStore.subscribe((mode) => {
		alternativeMode = mode;
	});

	// 권한 요청 핸들러
	async function handleRequestPermission() {
		if (!canRequestPermission()) {
			return;
		}

		try {
			const result = await requestNotificationPermission();

			if (result === 'granted') {
				// 권한 부여 성공 시 테스트 알림 표시
				setTimeout(() => {
					testNotification();
				}, 500);
			}
		} catch (error) {
			console.error('권한 요청 처리 중 오류:', error);
		}
	}

	// 브라우저 설정 열기 핸들러
	function handleOpenSettings() {
		openBrowserNotificationSettings();
	}

	// 테스트 알림 핸들러
	async function handleTestNotification() {
		const success = await testNotification();
		if (!success) {
			alert('테스트 알림을 표시할 수 없습니다. 브라우저 알림 권한을 확인해주세요.');
		}
	}

	// 컴포넌트 마운트
	onMount(async () => {
		// 권한 상태 모니터링 시작
		cleanupMonitoring = startPermissionMonitoring();

		// 초기 권한 상태 확인
		getNotificationPermissionStatus();

		// 자동 권한 요청 (옵션)
		if (autoRequest && canRequestPermission()) {
			setTimeout(() => {
				handleRequestPermission();
			}, 1000);
		}
	});

	// 컴포넌트 언마운트
	onDestroy(() => {
		unsubscribePermission();
		unsubscribeRequestProgress();
		unsubscribeAlternativeMode();

		if (cleanupMonitoring) {
			cleanupMonitoring();
		}
	});

	// 아이콘 클래스 계산
	$: iconClass =
		{
			success: 'text-success',
			warning: 'text-warning',
			info: 'text-info',
			error: 'text-error'
		}[guideMessage?.type] || 'text-gray-500';

	// 카드 클래스 계산
	$: cardClass =
		{
			success: 'border-success bg-success/5',
			warning: 'border-warning bg-warning/5',
			info: 'border-info bg-info/5',
			error: 'border-error bg-error/5'
		}[guideMessage?.type] || 'border-gray-300';
</script>

{#if showCard && guideMessage}
	<div class="card {cardClass} border-2 shadow-sm">
		<div class="card-body p-4">
			<div class="flex items-start gap-3">
				<!-- 상태 아이콘 -->
				<div class="flex-shrink-0 mt-1">
					{#if guideMessage.type === 'success'}
						<i class="fas fa-check-circle {iconClass} text-xl"></i>
					{:else if guideMessage.type === 'warning'}
						<i class="fas fa-exclamation-triangle {iconClass} text-xl"></i>
					{:else if guideMessage.type === 'info'}
						<i class="fas fa-info-circle {iconClass} text-xl"></i>
					{:else}
						<i class="fas fa-times-circle {iconClass} text-xl"></i>
					{/if}
				</div>

				<!-- 메시지 내용 -->
				<div class="flex-1">
					<h3 class="font-semibold text-base mb-1">
						{guideMessage.title}
					</h3>
					<p class="text-sm text-gray-600 mb-3">
						{guideMessage.message}
					</p>

					<!-- 대체 알림 모드 안내 -->
					{#if alternativeMode}
						<div class="alert alert-info py-2 px-3 text-xs mb-3">
							<i class="fas fa-info-circle"></i>
							<span>현재 화면 내 토스트 알림을 사용하고 있습니다.</span>
						</div>
					{/if}

					<!-- 액션 버튼들 -->
					<div class="flex flex-wrap gap-2">
						{#if guideMessage.actionRequired && guideMessage.actionText}
							{#if permissionStatus === 'default'}
								<!-- 권한 요청 버튼 -->
								<button
									class="btn btn-primary btn-sm"
									class:loading={requestInProgress}
									disabled={requestInProgress}
									on:click={handleRequestPermission}
								>
									{#if requestInProgress}
										<span class="loading loading-spinner loading-xs"></span>
										요청 중...
									{:else}
										<i class="fas fa-bell mr-1"></i>
										{guideMessage.actionText}
									{/if}
								</button>
							{:else if permissionStatus === 'denied'}
								<!-- 설정 열기 버튼 -->
								<button class="btn btn-outline btn-sm" on:click={handleOpenSettings}>
									<i class="fas fa-cog mr-1"></i>
									{guideMessage.actionText}
								</button>
							{/if}
						{/if}

						<!-- 테스트 버튼 (권한이 부여된 경우) -->
						{#if showTestButton && permissionStatus === 'granted'}
							<button class="btn btn-ghost btn-sm" on:click={handleTestNotification}>
								<i class="fas fa-vial mr-1"></i>
								테스트 알림
							</button>
						{/if}
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}

<!-- 간단한 상태 표시 (카드 모드가 아닌 경우) -->
{#if !showCard}
	<div class="flex items-center gap-2 text-sm">
		{#if permissionStatus === 'granted'}
			<i class="fas fa-bell text-success"></i>
			<span class="text-success">알림 허용됨</span>
		{:else if permissionStatus === 'denied'}
			<i class="fas fa-bell-slash text-warning"></i>
			<span class="text-warning">알림 거부됨</span>
		{:else}
			<i class="fas fa-bell text-gray-400"></i>
			<span class="text-gray-600">알림 미설정</span>
		{/if}

		{#if alternativeMode}
			<span class="badge badge-info badge-xs">대체모드</span>
		{/if}
	</div>
{/if}

<style>
	/* 추가 스타일링이 필요한 경우 여기에 작성 */
</style>
