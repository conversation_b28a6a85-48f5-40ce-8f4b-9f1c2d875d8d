/**
 * 알림 시스템 타입 정의
 */

/**
 * 알림 데이터 인터페이스
 */
export interface NotificationData {
	id: number;
	title: string;
	message: string;
	priority: 'low' | 'normal' | 'high' | 'urgent';
	type: string;
	action_url?: string;
	read: boolean;
	received_at: string;
	read_at?: string;
	sender_id?: number;
	sender_name?: string;
	target_type: 'all' | 'group' | 'individual';
	target_ids?: number[];
	expires_at?: string;
	metadata?: Record<string, any>;
}

/**
 * 알림 필터 타입
 */
export type NotificationFilter = 'all' | 'unread' | 'read';

/**
 * 알림 우선순위 타입
 */
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

/**
 * 알림 표시 설정
 */
export interface NotificationDisplayConfig {
	priority: NotificationPriority;
	duration: number;
	style: NotificationStyle;
	requireInteraction: boolean;
}

/**
 * 알림 스타일 설정
 */
export interface NotificationStyle {
	backgroundColor: string;
	borderColor: string;
	textColor: string;
	iconColor: string;
	animation?: string;
}

/**
 * 알림 센터 상태
 */
export interface NotificationCenterState {
	notifications: NotificationData[];
	filteredNotifications: NotificationData[];
	currentFilter: NotificationFilter;
	currentPage: number;
	itemsPerPage: number;
	totalPages: number;
	selectedNotifications: Set<number>;
	selectAll: boolean;
	isLoading: boolean;
	error: string | null;
}

/**
 * 페이지네이션 정보
 */
export interface PaginationInfo {
	currentPage: number;
	totalPages: number;
	itemsPerPage: number;
	totalItems: number;
	startIndex: number;
	endIndex: number;
}

/**
 * 일괄 처리 결과
 */
export interface BulkActionResult {
	success: number[];
	failed: number[];
	total: number;
}

/**
 * 알림 액션 타입
 */
export type NotificationAction = 'read' | 'unread' | 'delete';

/**
 * 알림 이벤트 데이터
 */
export interface NotificationEventData {
	notificationId: number;
	notification?: NotificationData;
	timestamp: string;
	action?: NotificationAction;
	results?: BulkActionResult;
}
