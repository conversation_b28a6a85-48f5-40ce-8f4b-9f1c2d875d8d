// 데이터베이스 설정
export interface DatabaseConfig {
	name: string;
	version: number;
	stores: StoreConfig[];
}

// 스토어 설정
export interface StoreConfig {
	name: string;
	keyPath: string;
	autoIncrement?: boolean;
	indexes?: IndexConfig[];
}

// 인덱스 설정
export interface IndexConfig {
	name: string;
	keyPath: string;
	unique?: boolean;
}

// 카테고리 데이터
export interface CategoryData {
	key: string;
	cate4: Category[];
	cate5: Category[];
	updated_at: string;
}

export interface Category {
	id: number;
	name: string;
	code?: string;
	description?: string;
	status?: 'active' | 'inactive';
	parent_id?: number;
	order?: number;
	created_at?: string;
	updated_at?: string;
}

// 직원 데이터
export interface Employee {
	id: number;
	name: string;
	email: string;
	group_id?: number;
	position?: string;
	department?: string;
	active: boolean;
	created_at: string;
	updated_at: string;
}

// 그룹 데이터
export interface Group {
	id: number;
	name: string;
	description?: string;
	permission_level: number;
	created_at: string;
	updated_at: string;
}

// 알림 데이터
export interface Notification {
	id: number;
	title: string;
	message: string;
	type: string;
	priority: 'low' | 'normal' | 'high' | 'urgent';
	user_id?: number;
	group_id?: number;
	action_url?: string;
	action_text?: string;
	read: boolean;
	read_at?: string;
	received_at: string;
	expires_at?: string;
}

// 설정 데이터
export interface Setting {
	key: string;
	value: any;
	updated_at: string;
}

// 인쇄 설정 데이터 (마이그레이션)
export interface PrintSetting {
	settingName: string;
	settings: SettingItem[];
	updatedAt: string;
	migrated?: boolean;
	migratedAt?: string;
}

export interface SettingItem {
	name: string;
	code: string;
	settings: Record<string, any>;
}

// 수리 등급 데이터
export interface RepairGrade {
	id: number;
	name: string;
	code: string;
	description?: string;
	color?: string;
	order?: number;
	active: boolean;
}

// 업데이트 히스토리
export interface UpdateHistory {
	id: string;
	model: string;
	action: string;
	payload: any;
	affected_ids: number[];
	timestamp: string;
}
