/**
 * SSE 시스템 공통 타입 정의
 */

/**
 * SSE 메시지 기본 인터페이스
 */
export interface SseMessage {
	type: 'notification' | 'data_update' | 'heartbeat' | 'connection' | 'system' | 'connection_established';
	timestamp: string;
	data: any;
	id?: string;
	retry?: number;
}

/**
 * 알림 메시지 데이터
 */
export interface NotificationMessage {
	id: number;
	title: string;
	message: string;
	type: string;
	priority: 'low' | 'normal' | 'high' | 'urgent';
	target_type: 'all' | 'role' | 'group' | 'individual';
	target_roles: 'all' | 'super_admin' | 'admin' | 'receiving_manager' | 'pallet_manager' | 'carryout_manager' | 'employee' | 'guest';
	target_ids?: number[];
	action_url?: string;
	action_text?: string;
	expires_at?: string;
}

/**
 * 데이터 업데이트 메시지
 */
export interface DataUpdateMessage {
	model: 'categories' | 'employees' | 'groups' | 'repair_grades';
	action: 'created' | 'updated' | 'deleted' | 'batch_updated';
	payload: any;
	affected_ids: number[];
	timestamp: string;
}

/**
 * 하트비트 메시지
 */
export interface HeartbeatMessage {
	timestamp: string;
	server_time?: string;
	status?: string;
}

/**
 * 연결 메시지
 */
export interface ConnectionMessage {
	status: 'authenticated' | 'unauthenticated' | 'reconnected';
	user_id?: number;
	session_id?: string;
	message?: string;
}

/**
 * 시스템 메시지
 */
export interface SystemMessage {
	type: 'maintenance' | 'version_update' | 'server_restart' | 'announcement';
	title: string;
	message: string;
	scheduled_time?: string;
	duration?: number;
	severity: 'info' | 'warning' | 'critical';
}

/**
 * SSE 연결 옵션 인터페이스
 */
export interface SseConnectionOptions {
	url?: string;
	withCredentials?: boolean;
	autoReconnect?: boolean;
	maxReconnectAttempts?: number;
	reconnectInterval?: number;
	onOpen?: (event: Event) => void;
	onMessage?: (event: MessageEvent) => void;
	onError?: (event: Event) => void;
	onClose?: () => void;
}

/**
 * 환경별 SSE 설정 인터페이스
 */
export interface SseEnvironmentConfig {
	endpoint: string;
	withCredentials: boolean;
	reconnectInterval: number;
	maxReconnectAttempts: number;
	heartbeatInterval: number;
}

/**
 * SSE 연결 상태 타입
 */
export type SseConnectionStatus =
	| 'disconnected'
	| 'connecting'
	| 'connected'
	| 'reconnecting'
	| 'error'
	| 'closed';

/**
 * SSE 연결 상태 정보 인터페이스
 */
export interface SseConnectionState {
	status: SseConnectionStatus;
	lastConnected: Date | null;
	lastError: string | null;
	reconnectAttempts: number;
	isOnline: boolean;
	latency: number | null;
	lastHeartbeat: Date | null;
	connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown';
}

/**
 * SSE 연결 통계 인터페이스
 */
export interface SseConnectionStats {
	totalConnections: number;
	totalReconnections: number;
	totalMessages: number;
	totalErrors: number;
	averageLatency: number;
	connectionUptime: number;
	lastMessageTime: Date | null;
	messagesPerMinute: number;
}

/**
 * 메시지 핸들러 타입
 */
export type MessageHandler<T = any> = (data: T) => void;

/**
 * 배치 처리 설정
 */
export interface BatchConfig {
	batchSize: number;
	batchDelay: number;
}

/**
 * 히스토리 관리 설정
 */
export interface HistoryConfig {
	maxHistorySize: number;
	cleanupInterval: number;
	maxAge: number;
}

/**
 * 재연결 전략
 */
export interface ReconnectionStrategy {
	name: string;
	baseInterval: number;
	maxInterval: number;
	maxAttempts: number;
	backoffMultiplier: number;
	jitterEnabled: boolean;
}

/**
 * 오류 복구 전략
 */
export interface ErrorRecoveryStrategy {
	type: string;
	maxRetries: number;
	retryDelay: number;
	recoveryAction: () => Promise<void>;
}

/**
 * 통계 인터페이스들
 */
export interface ConnectionStats {
	totalConnections: number;
	successfulConnections: number;
	failedConnections: number;
	currentUptime: number;
	totalUptime: number;
	averageConnectionDuration: number;
	reconnectAttempts: number;
	lastConnectionTime?: Date;
	lastDisconnectionTime?: Date;
}

export interface MessageStats {
	totalMessages: number;
	messagesByType: Record<string, number>;
	messagesPerSecond: number;
	averageMessageSize: number;
	lastMessageTime?: Date;
	processingErrors: number;
}

export interface PerformanceStats {
	averageLatency: number;
	minLatency: number;
	maxLatency: number;
	latencyHistory: number[];
	memoryUsage: number;
	cpuUsage?: number;
}

export interface ErrorStats {
	totalErrors: number;
	errorsByType: Record<string, number>;
	errorRate: number;
	lastError?: {
		type: string;
		message: string;
		timestamp: Date;
	};
}

export interface SseStats {
	connection: ConnectionStats;
	messages: MessageStats;
	performance: PerformanceStats;
	errors: ErrorStats;
	startTime: Date;
	lastUpdated: Date;
}
