/**
 * SSE 직원 알림 시스템 - 시스템 전체 초기화 함수
 *
 * IndexedDB, SSE, 스토어를 순차적으로 초기화하고
 * 초기화 실패 시 복구 로직을 제공합니다.
 *
 * 요구사항 5.1, 5.6을 충족합니다.
 */

import { initializeWithMigration, checkDatabaseHealth, repairDatabase } from './indexedDBManager';
import { connect as connectSSE, disconnect as disconnectSSE, isOfflineMode } from './sseConnection';
import { initializeAllStores, initializeSpecificStores } from './storeUpdaters';
import {
	startGlobalCacheManagement,
	stopGlobalCacheManagement,
	getGlobalCacheManagementStatus
} from './cacheManager';
import { connectionStore, networkStore, sseStatsStore } from '$lib/stores';
import { get } from 'svelte/store';

// 시스템 상태 관리
interface SystemState {
	isInitialized: boolean;
	initializationStartTime: number | null;
	initializationEndTime: number | null;
	database: IDBDatabase | null;
	sseConnection: EventSource | null;
	cacheManager: ReturnType<typeof startGlobalCacheManagement> | null;
	lastError: string | null;
	initializationAttempts: number;
	maxInitializationAttempts: number;
}

let systemState: SystemState = {
	isInitialized: false,
	initializationStartTime: null,
	initializationEndTime: null,
	database: null,
	sseConnection: null,
	cacheManager: null,
	lastError: null,
	initializationAttempts: 0,
	maxInitializationAttempts: 3
};

// 초기화 단계 정의
enum InitializationStep {
	DATABASE = 'database',
	STORES = 'stores',
	CACHE_MANAGEMENT = 'cache_management',
	SSE = 'sse',
	COMPLETE = 'complete'
}

interface InitializationProgress {
	step: InitializationStep;
	progress: number; // 0-100
	message: string;
	error?: string;
}

// 초기화 진행상황 콜백 타입
type ProgressCallback = (progress: InitializationProgress) => void;

/**
 * 시스템 전체 초기화 메인 함수
 *
 * @param options 초기화 옵션
 * @param progressCallback 진행상황 콜백 함수 (선택사항)
 * @returns Promise<SystemInitializationResult>
 */
export async function initializeSystem(
	options: SystemInitializationOptions = {},
	progressCallback?: ProgressCallback
): Promise<SystemInitializationResult> {
	const startTime = Date.now();
	systemState.initializationStartTime = startTime;
	systemState.initializationAttempts++;

	console.log(
		`시스템 초기화 시작 (시도 ${systemState.initializationAttempts}/${systemState.maxInitializationAttempts})`
	);

	try {
		// 이미 초기화된 경우 확인
		if (systemState.isInitialized && !options.forceReinitialize) {
			console.log('시스템이 이미 초기화되어 있습니다.');
			return createSuccessResult(startTime, '시스템이 이미 초기화되어 있습니다.');
		}

		// 1단계: IndexedDB 초기화
		progressCallback?.({
			step: InitializationStep.DATABASE,
			progress: 10,
			message: 'IndexedDB 초기화 중...'
		});

		const database = await initializeDatabase(options);
		systemState.database = database;

		progressCallback?.({
			step: InitializationStep.DATABASE,
			progress: 30,
			message: 'IndexedDB 초기화 완료'
		});

		// 2단계: 스토어 초기화
		progressCallback?.({
			step: InitializationStep.STORES,
			progress: 40,
			message: '데이터 스토어 초기화 중...'
		});

		await initializeStores(database, options);

		progressCallback?.({
			step: InitializationStep.STORES,
			progress: 60,
			message: '데이터 스토어 초기화 완료'
		});

		// 3단계: 캐시 관리 시스템 시작
		progressCallback?.({
			step: InitializationStep.CACHE_MANAGEMENT,
			progress: 65,
			message: '캐시 관리 시스템 시작 중...'
		});

		const cacheManager = await initializeCacheManagement(database, options);
		systemState.cacheManager = cacheManager;

		progressCallback?.({
			step: InitializationStep.CACHE_MANAGEMENT,
			progress: 70,
			message: '캐시 관리 시스템 시작 완료'
		});

		// 4단계: SSE 연결 초기화 (오프라인이 아닌 경우에만)
		progressCallback?.({
			step: InitializationStep.SSE,
			progress: 75,
			message: 'SSE 연결 초기화 중...'
		});

		const sseConnection = await initializeSSEConnection(options);
		systemState.sseConnection = sseConnection;

		progressCallback?.({
			step: InitializationStep.SSE,
			progress: 90,
			message: 'SSE 연결 초기화 완료'
		});

		// 5단계: 초기화 완료
		systemState.isInitialized = true;
		systemState.initializationEndTime = Date.now();
		systemState.lastError = null;

		progressCallback?.({
			step: InitializationStep.COMPLETE,
			progress: 100,
			message: '시스템 초기화 완료'
		});

		const result = createSuccessResult(startTime, '시스템 초기화가 성공적으로 완료되었습니다.');
		console.log('시스템 초기화 성공:', result);

		return result;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		systemState.lastError = errorMessage;
		systemState.initializationEndTime = Date.now();

		console.error('시스템 초기화 실패:', error);

		progressCallback?.({
			step: InitializationStep.DATABASE, // 실패한 단계는 정확히 알기 어려우므로 첫 단계로 설정
			progress: 0,
			message: '시스템 초기화 실패',
			error: errorMessage
		});

		// 복구 시도
		if (
			systemState.initializationAttempts <= systemState.maxInitializationAttempts &&
			options.enableRecovery !== false
		) {
			console.log('시스템 복구 시도...');
			return await attemptSystemRecovery(options, progressCallback);
		}

		return createErrorResult(startTime, errorMessage, error);
	}
}

/**
 * IndexedDB 초기화
 *
 * @param options 초기화 옵션
 * @returns Promise<IDBDatabase>
 */
async function initializeDatabase(options: SystemInitializationOptions): Promise<IDBDatabase> {
	try {
		console.log('IndexedDB 초기화 시작...');

		// 데이터베이스 상태 확인
		const dbName = options.databaseName || 'EmployeeNotificationDB';
		const isHealthy = await checkDatabaseHealth(dbName);

		if (!isHealthy && options.repairDatabaseOnFailure !== false) {
			console.log('데이터베이스 상태가 불량하여 복구를 시도합니다.');
			return await repairDatabase(dbName);
		}

		// 정상적인 초기화 (마이그레이션 포함)
		const database = await initializeWithMigration(dbName, options.databaseVersion || 1);

		console.log('IndexedDB 초기화 완료');
		return database;
	} catch (error) {
		console.error('IndexedDB 초기화 실패:', error);
		throw new Error(
			`IndexedDB 초기화 실패: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * 스토어 초기화
 *
 * @param database IDBDatabase 인스턴스
 * @param options 초기화 옵션
 * @returns Promise<void>
 */
async function initializeStores(
	database: IDBDatabase,
	options: SystemInitializationOptions
): Promise<void> {
	try {
		console.log('스토어 초기화 시작...');

		if (options.storeNames && options.storeNames.length > 0) {
			// 특정 스토어만 초기화
			await initializeSpecificStores(database, options.storeNames);
		} else {
			// 모든 스토어 초기화
			await initializeAllStores(database, options.showProgress !== false);
		}

		console.log('스토어 초기화 완료');
	} catch (error) {
		console.error('스토어 초기화 실패:', error);

		// 부분 실패인 경우 경고만 출력하고 계속 진행
		if (error instanceof Error && error.name === 'PartialInitializationError') {
			console.warn('일부 스토어 초기화 실패, 계속 진행합니다:', error.message);
			return;
		}

		throw new Error(
			`스토어 초기화 실패: ${error instanceof Error ? error.message : String(error)}`
		);
	}
}

/**
 * 캐시 관리 시스템 초기화
 *
 * @param database IDBDatabase 인스턴스
 * @param options 초기화 옵션
 * @returns Promise<ReturnType<typeof startGlobalCacheManagement>>
 */
async function initializeCacheManagement(
	database: IDBDatabase,
	options: SystemInitializationOptions
): Promise<ReturnType<typeof startGlobalCacheManagement>> {
	try {
		console.log('캐시 관리 시스템 초기화 시작...');

		// 캐시 관리 비활성화 옵션 확인
		if (options.disableCacheManagement) {
			console.log('캐시 관리가 비활성화되어 있습니다.');
			throw new Error('캐시 관리가 비활성화되어 있습니다.');
		}

		// 캐시 관리 옵션 설정
		const cacheOptions = {
			cleanupInterval: options.cacheCleanupInterval || 60 * 60 * 1000, // 1시간
			monitoringInterval: options.cacheMonitoringInterval || 10 * 60 * 1000, // 10분
			autoCleanup: options.enableAutoCacheCleanup !== false,
			maxSizeBytes: options.maxCacheSize || 50 * 1024 * 1024, // 50MB
			targetUsageRatio: options.cacheTargetUsageRatio || 0.7,
			enablePageUnloadCleanup: options.enablePageUnloadCleanup !== false,
			verbose: options.verboseCacheLogging || false
		};

		// 전역 캐시 관리 시작
		const cacheManager = startGlobalCacheManagement(database, cacheOptions);

		console.log('캐시 관리 시스템 초기화 완료');
		return cacheManager;
	} catch (error) {
		console.error('캐시 관리 시스템 초기화 실패:', error);

		// 캐시 관리 실패는 치명적이지 않음 (시스템은 계속 동작 가능)
		if (options.requireCacheManagement) {
			throw new Error(
				`캐시 관리 필수 모드에서 초기화 실패: ${error instanceof Error ? error.message : String(error)}`
			);
		}

		console.warn('캐시 관리 초기화 실패 - 캐시 관리 없이 계속 진행');

		// 더미 캐시 관리 객체 반환
		return {
			scheduler: {
				start: () => {},
				stop: () => {},
				runCleanupNow: async () => ({
					totalCleanedItems: 0,
					totalCleanedSize: 0,
					storeResults: [],
					totalItemsBefore: 0,
					totalItemsAfter: 0,
					executionTime: 0,
					warnings: []
				}),
				runManagementNow: async () => ({
					currentUsage: 0,
					maxSize: 0,
					usageRatio: 0,
					cleanupPerformed: false,
					cleanedSize: 0,
					cleanedItems: 0,
					storeResults: [],
					warnings: [],
					executionTime: 0
				}),
				isRunning: () => false,
				getLastRunTime: () => null,
				getNextRunTime: () => null
			},
			cleanup: () => {},
			forceCleanup: async () => {},
			getStatus: () => ({
				isRunning: false,
				lastCleanupTime: null,
				nextCleanupTime: null,
				unloadCleanupEnabled: false,
				settings: {
					cleanupInterval: 0,
					monitoringInterval: 0,
					maxSizeBytes: 0,
					targetUsageRatio: 0,
					autoCleanup: false
				}
			})
		};
	}
}

/**
 * SSE 연결 초기화
 *
 * @param options 초기화 옵션
 * @returns Promise<EventSource | null>
 */
async function initializeSSEConnection(
	options: SystemInitializationOptions
): Promise<EventSource | null> {
	try {
		console.log('SSE 연결 초기화 시작...');

		// 오프라인 모드 확인
		if (isOfflineMode() && !options.forceSSEConnection) {
			console.log('오프라인 모드 - SSE 연결을 건너뜁니다.');
			return null;
		}

		// SSE 연결 비활성화 옵션 확인
		if (options.disableSSE) {
			console.log('SSE 연결이 비활성화되어 있습니다.');
			return null;
		}

		// SSE 연결 시도
		const connection = await connectSSE();

		if (connection) {
			console.log('SSE 연결 초기화 완료');
		} else {
			console.warn('SSE 연결 실패 - 오프라인 모드로 계속 진행');
		}

		return connection;
	} catch (error) {
		console.error('SSE 연결 초기화 실패:', error);

		// SSE 연결 실패는 치명적이지 않음 (오프라인 모드로 동작 가능)
		if (options.requireSSEConnection) {
			throw new Error(
				`SSE 연결 필수 모드에서 연결 실패: ${error instanceof Error ? error.message : String(error)}`
			);
		}

		console.warn('SSE 연결 실패 - 오프라인 모드로 계속 진행');
		return null;
	}
}

/**
 * 시스템 복구 시도
 *
 * @param options 초기화 옵션
 * @param progressCallback 진행상황 콜백
 * @returns Promise<SystemInitializationResult>
 */
async function attemptSystemRecovery(
	options: SystemInitializationOptions,
	progressCallback?: ProgressCallback
): Promise<SystemInitializationResult> {
	const startTime = Date.now();

	try {
		console.log('시스템 복구 시작...');

		progressCallback?.({
			step: InitializationStep.DATABASE,
			progress: 5,
			message: '시스템 복구 중...'
		});

		// 기존 연결 정리
		await cleanupSystem();

		// 복구 옵션으로 재시도
		const recoveryOptions: SystemInitializationOptions = {
			...options,
			forceReinitialize: true,
			repairDatabaseOnFailure: true,
			enableRecovery: false // 무한 루프 방지
		};

		// 짧은 대기 후 재시도
		await new Promise((resolve) => setTimeout(resolve, 1000));

		return await initializeSystem(recoveryOptions, progressCallback);
	} catch (error) {
		const errorMessage = `시스템 복구 실패: ${error instanceof Error ? error.message : String(error)}`;
		console.error(errorMessage);

		return createErrorResult(startTime, errorMessage, error);
	}
}

/**
 * 시스템 정리 및 리소스 해제
 *
 * @returns Promise<void>
 */
export async function cleanupSystem(): Promise<void> {
	try {
		console.log('시스템 정리 시작...');

		// 캐시 관리 시스템 중지
		if (systemState.cacheManager) {
			systemState.cacheManager.cleanup();
			systemState.cacheManager = null;
		}

		// 전역 캐시 관리 중지
		stopGlobalCacheManagement();

		// SSE 연결 해제
		if (systemState.sseConnection) {
			disconnectSSE();
			systemState.sseConnection = null;
		}

		// 데이터베이스 연결 해제
		if (systemState.database) {
			systemState.database.close();
			systemState.database = null;
		}

		// 시스템 상태 초기화
		systemState.isInitialized = false;
		systemState.lastError = null;

		// 스토어 상태 초기화
		connectionStore.set({
			status: 'disconnected',
			lastUpdate: null,
			error: null,
			reconnectAttempts: 0,
			maxReconnectAttempts: 5,
			reconnectDelay: 3000
		});

		console.log('시스템 정리 완료');
	} catch (error) {
		console.error('시스템 정리 중 오류 발생:', error);
	}
}

/**
 * 시스템 상태 확인
 *
 * @returns SystemStatus
 */
export function getSystemStatus(): SystemStatus {
	// 안전한 스토어 값 조회 (테스트 환경 고려)
	let connectionState, networkState, sseStats;

	try {
		connectionState = get(connectionStore);
	} catch {
		connectionState = {
			status: 'disconnected',
			lastUpdate: null,
			error: null,
			reconnectAttempts: 0,
			maxReconnectAttempts: 5,
			reconnectDelay: 3000
		};
	}

	try {
		networkState = get(networkStore);
	} catch {
		networkState = {
			isOnline: navigator?.onLine ?? true,
			lastOnlineAt: new Date().toISOString(),
			lastOfflineAt: null
		};
	}

	try {
		sseStats = get(sseStatsStore);
	} catch {
		sseStats = {
			totalMessagesReceived: 0,
			notificationsReceived: 0,
			dataUpdatesReceived: 0,
			errorsCount: 0,
			lastMessageAt: null,
			connectionUptime: 0,
			averageLatency: 0
		};
	}

	// 캐시 관리 상태 조회
	const cacheManagementStatus = getGlobalCacheManagementStatus();

	return {
		isInitialized: systemState.isInitialized,
		initializationAttempts: systemState.initializationAttempts,
		lastError: systemState.lastError,
		database: {
			isConnected: systemState.database !== null,
			isHealthy: systemState.database !== null
		},
		sse: {
			isConnected: connectionState.status === 'connected',
			status: connectionState.status,
			lastError: connectionState.error,
			stats: sseStats
		},
		network: {
			isOnline: networkState.isOnline,
			lastOnlineAt: networkState.lastOnlineAt,
			lastOfflineAt: networkState.lastOfflineAt
		},
		cacheManagement: cacheManagementStatus,
		performance: {
			initializationTime:
				systemState.initializationEndTime && systemState.initializationStartTime
					? systemState.initializationEndTime - systemState.initializationStartTime
					: null,
			uptime: systemState.initializationEndTime
				? Date.now() - systemState.initializationEndTime
				: null
		}
	};
}

/**
 * 시스템 재시작
 *
 * @param options 초기화 옵션
 * @param progressCallback 진행상황 콜백
 * @returns Promise<SystemInitializationResult>
 */
export async function restartSystem(
	options: SystemInitializationOptions = {},
	progressCallback?: ProgressCallback
): Promise<SystemInitializationResult> {
	console.log('시스템 재시작 시작...');

	// 기존 시스템 정리
	await cleanupSystem();

	// 재시작 옵션 설정
	const restartOptions: SystemInitializationOptions = {
		...options,
		forceReinitialize: true
	};

	// 시스템 재초기화
	return await initializeSystem(restartOptions, progressCallback);
}

/**
 * 부분 시스템 초기화 (특정 컴포넌트만)
 *
 * @param components 초기화할 컴포넌트 목록
 * @param options 초기화 옵션
 * @returns Promise<SystemInitializationResult>
 */
export async function initializePartialSystem(
	components: ('database' | 'stores' | 'cache_management' | 'sse')[],
	options: SystemInitializationOptions = {}
): Promise<SystemInitializationResult> {
	const startTime = Date.now();

	try {
		console.log('부분 시스템 초기화 시작:', components);

		if (components.includes('database') && !systemState.database) {
			systemState.database = await initializeDatabase(options);
		}

		if (components.includes('stores') && systemState.database) {
			await initializeStores(systemState.database, options);
		}

		if (
			components.includes('cache_management') &&
			systemState.database &&
			!systemState.cacheManager
		) {
			systemState.cacheManager = await initializeCacheManagement(systemState.database, options);
		}

		if (components.includes('sse') && !systemState.sseConnection) {
			systemState.sseConnection = await initializeSSEConnection(options);
		}

		return createSuccessResult(startTime, '부분 시스템 초기화 완료');
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		console.error('부분 시스템 초기화 실패:', error);
		return createErrorResult(startTime, errorMessage, error);
	}
}

// ===== 유틸리티 함수들 =====

/**
 * 성공 결과 생성
 */
function createSuccessResult(startTime: number, message: string): SystemInitializationResult {
	return {
		success: true,
		message,
		duration: Date.now() - startTime,
		systemState: getSystemStatus(),
		timestamp: new Date().toISOString()
	};
}

/**
 * 오류 결과 생성
 */
function createErrorResult(
	startTime: number,
	message: string,
	error: unknown
): SystemInitializationResult {
	return {
		success: false,
		message,
		error: error instanceof Error ? error : new Error(String(error)),
		duration: Date.now() - startTime,
		systemState: getSystemStatus(),
		timestamp: new Date().toISOString()
	};
}

// ===== 타입 정의 =====

/**
 * 시스템 초기화 옵션
 */
export interface SystemInitializationOptions {
	/** 강제 재초기화 여부 */
	forceReinitialize?: boolean;
	/** 데이터베이스 이름 */
	databaseName?: string;
	/** 데이터베이스 버전 */
	databaseVersion?: number;
	/** 데이터베이스 실패 시 복구 시도 여부 */
	repairDatabaseOnFailure?: boolean;
	/** 초기화할 특정 스토어 목록 */
	storeNames?: string[];
	/** 진행상황 표시 여부 */
	showProgress?: boolean;
	/** SSE 연결 비활성화 여부 */
	disableSSE?: boolean;
	/** SSE 연결 강제 시도 여부 */
	forceSSEConnection?: boolean;
	/** SSE 연결 필수 여부 */
	requireSSEConnection?: boolean;
	/** 복구 기능 활성화 여부 */
	enableRecovery?: boolean;
	/** 캐시 관리 비활성화 여부 */
	disableCacheManagement?: boolean;
	/** 캐시 관리 필수 여부 */
	requireCacheManagement?: boolean;
	/** 캐시 정리 주기 (밀리초) */
	cacheCleanupInterval?: number;
	/** 캐시 모니터링 주기 (밀리초) */
	cacheMonitoringInterval?: number;
	/** 자동 캐시 정리 활성화 여부 */
	enableAutoCacheCleanup?: boolean;
	/** 최대 캐시 크기 (바이트) */
	maxCacheSize?: number;
	/** 캐시 목표 사용률 */
	cacheTargetUsageRatio?: number;
	/** 페이지 언로드 시 캐시 정리 활성화 여부 */
	enablePageUnloadCleanup?: boolean;
	/** 캐시 관리 상세 로깅 활성화 여부 */
	verboseCacheLogging?: boolean;
}

/**
 * 시스템 초기화 결과
 */
export interface SystemInitializationResult {
	/** 성공 여부 */
	success: boolean;
	/** 결과 메시지 */
	message: string;
	/** 오류 (실패 시) */
	error?: Error;
	/** 소요 시간 (밀리초) */
	duration: number;
	/** 시스템 상태 */
	systemState: SystemStatus;
	/** 타임스탬프 */
	timestamp: string;
}

/**
 * 시스템 상태
 */
export interface SystemStatus {
	/** 초기화 완료 여부 */
	isInitialized: boolean;
	/** 초기화 시도 횟수 */
	initializationAttempts: number;
	/** 마지막 오류 */
	lastError: string | null;
	/** 데이터베이스 상태 */
	database: {
		isConnected: boolean;
		isHealthy: boolean;
	};
	/** SSE 연결 상태 */
	sse: {
		isConnected: boolean;
		status: string;
		lastError: string | null;
		stats: any;
	};
	/** 네트워크 상태 */
	network: {
		isOnline: boolean;
		lastOnlineAt: string;
		lastOfflineAt: string | null;
	};
	/** 캐시 관리 상태 */
	cacheManagement: {
		isRunning: boolean;
		lastCleanupTime: Date | null;
		nextCleanupTime: Date | null;
		unloadCleanupEnabled: boolean;
		settings: {
			cleanupInterval: number;
			monitoringInterval: number;
			maxSizeBytes: number;
			targetUsageRatio: number;
			autoCleanup: boolean;
		};
	} | null;
	/** 성능 정보 */
	performance: {
		initializationTime: number | null;
		uptime: number | null;
	};
}
