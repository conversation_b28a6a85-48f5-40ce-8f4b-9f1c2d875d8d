/**
 * 알림 시스템 데모 및 사용 예제
 *
 * 작업 5.4에서 구현된 브라우저 알림 및 멀티미디어 처리 기능 데모
 */

import {
	showBrowserNotification,
	playNotificationSound,
	vibrateDevice,
	requestNotificationPermission,
	requestNotificationPermissionWithExplanation,
	getNotificationPermissionStatus,
	testNotificationSystem,
	diagnoseNotificationSystem,
	preloadNotificationSounds,
	isVibrationSupported,
	stopVibration,
	vibrateCustomPattern
} from '$lib/services/notificationHandler';
import type { NotificationData } from '$lib/types/notificationTypes';

/**
 * 알림 시스템 초기화 데모
 */
export async function initializeNotificationDemo(): Promise<void> {
	console.log('=== 알림 시스템 데모 시작 ===');

	// 1. 시스템 진단
	console.log('\n1. 시스템 진단 중...');
	const diagnosis = await diagnoseNotificationSystem();
	console.log('진단 결과:', diagnosis);

	// 2. 권한 요청 (사용자 친화적)
	console.log('\n2. 알림 권한 요청...');
	const hasPermission = await requestNotificationPermissionWithExplanation(true);
	console.log('권한 승인 여부:', hasPermission);

	// 3. 사운드 파일 사전 로드
	console.log('\n3. 사운드 파일 사전 로드...');
	await preloadNotificationSounds();

	console.log('\n=== 알림 시스템 초기화 완료 ===');
}

/**
 * 브라우저 알림 데모
 */
export async function demoBrowserNotifications(): Promise<void> {
	console.log('\n=== 브라우저 알림 데모 ===');

	const notifications: NotificationData[] = [
		{
			id: 1,
			title: '낮음 우선순위 알림',
			message: '이것은 낮은 우선순위 알림입니다.',
			type: 'info',
			priority: 'low',
			read: false,
			received_at: '',
			target_type: 'all'
		},
		{
			id: 2,
			title: '보통 우선순위 알림',
			message: '이것은 보통 우선순위 알림입니다.',
			type: 'info',
			priority: 'normal',
			read: false,
			received_at: '',
			target_type: 'all'
		},
		{
			id: 3,
			title: '높음 우선순위 알림',
			message: '이것은 높은 우선순위 알림입니다.',
			type: 'warning',
			priority: 'high',
			read: false,
			received_at: '',
			target_type: 'all'
		},
		{
			id: 4,
			title: '긴급 알림',
			message: '이것은 긴급 알림입니다. 즉시 확인이 필요합니다.',
			type: 'error',
			priority: 'urgent',
			action_url: 'https://example.com/urgent',
			read: false,
			received_at: '',
			target_type: 'all'
		}
	];

	for (const notification of notifications) {
		console.log(`\n${notification.priority} 우선순위 브라우저 알림 표시...`);
		await showBrowserNotification(notification);

		// 각 알림 사이에 2초 간격
		await new Promise((resolve) => setTimeout(resolve, 2000));
	}
}

/**
 * 사운드 알림 데모
 */
export async function demoSoundNotifications(): Promise<void> {
	console.log('\n=== 사운드 알림 데모 ===');

	const priorities = ['low', 'normal', 'high', 'urgent'];

	for (const priority of priorities) {
		console.log(`\n${priority} 우선순위 사운드 재생...`);
		playNotificationSound(priority);

		// 각 사운드 사이에 3초 간격
		await new Promise((resolve) => setTimeout(resolve, 3000));
	}

	// 사용자 지정 사운드 데모
	console.log('\n사용자 지정 사운드 재생...');
	playNotificationSound('normal', '/sounds/custom-notification.mp3');
}

/**
 * 진동 알림 데모
 */
export async function demoVibrationNotifications(): Promise<void> {
	console.log('\n=== 진동 알림 데모 ===');

	if (!isVibrationSupported()) {
		console.log('이 디바이스는 진동을 지원하지 않습니다.');
		return;
	}

	const priorities = ['low', 'normal', 'high', 'urgent'];

	for (const priority of priorities) {
		console.log(`\n${priority} 우선순위 진동 실행...`);
		vibrateDevice(priority);

		// 각 진동 사이에 2초 간격
		await new Promise((resolve) => setTimeout(resolve, 2000));
	}

	// 커스텀 진동 패턴 데모
	console.log('\n커스텀 진동 패턴 실행...');
	vibrateCustomPattern([200, 100, 200, 100, 200], '커스텀 패턴');

	// 3초 후 진동 중지
	setTimeout(() => {
		console.log('진동 중지...');
		stopVibration();
	}, 3000);
}

/**
 * 통합 알림 테스트 데모
 */
export async function demoIntegratedNotifications(): Promise<void> {
	console.log('\n=== 통합 알림 테스트 데모 ===');

	const priorities: Array<'low' | 'normal' | 'high' | 'urgent'> = [
		'low',
		'normal',
		'high',
		'urgent'
	];

	for (const priority of priorities) {
		console.log(`\n${priority} 우선순위 통합 테스트...`);
		await testNotificationSystem(priority, 'all');

		// 각 테스트 사이에 5초 간격
		await new Promise((resolve) => setTimeout(resolve, 5000));
	}
}

/**
 * 권한 관리 데모
 */
export async function demoPermissionManagement(): Promise<void> {
	console.log('\n=== 권한 관리 데모 ===');

	// 현재 권한 상태 확인
	const status = getNotificationPermissionStatus();
	console.log('현재 권한 상태:', status);

	// 권한이 필요한 경우 요청
	if (status.canRequest) {
		console.log('권한 요청 중...');
		const granted = await requestNotificationPermission();
		console.log('권한 요청 결과:', granted ? '승인됨' : '거부됨');
	}

	// 권한이 거부된 경우 안내
	if (status.permission === 'denied') {
		console.log('권한이 거부되었습니다. 브라우저 설정에서 수동으로 허용해주세요.');
	}
}

/**
 * 전체 데모 실행
 */
export async function runFullDemo(): Promise<void> {
	try {
		// 1. 시스템 초기화
		await initializeNotificationDemo();

		// 2. 권한 관리
		await demoPermissionManagement();

		// 3. 브라우저 알림 데모
		await demoBrowserNotifications();

		// 4. 사운드 알림 데모
		await demoSoundNotifications();

		// 5. 진동 알림 데모
		await demoVibrationNotifications();

		// 6. 통합 테스트 데모
		await demoIntegratedNotifications();

		console.log('\n=== 전체 데모 완료 ===');
	} catch (error) {
		console.error('데모 실행 중 오류 발생:', error);
	}
}

/**
 * 개별 기능 테스트
 */
export const notificationDemoFunctions = {
	// 권한 관리
	checkPermission: () => getNotificationPermissionStatus(),
	requestPermission: () => requestNotificationPermission(),
	requestPermissionWithExplanation: () => requestNotificationPermissionWithExplanation(),

	// 브라우저 알림
	showLowPriorityNotification: () =>
		showBrowserNotification({
			id: Date.now(),
			title: '테스트 알림',
			message: '낮음 우선순위 테스트',
			type: 'test',
			priority: 'low', read: false, received_at: '', target_type: 'all'
		}),

	showUrgentNotification: () =>
		showBrowserNotification({
			id: Date.now(),
			title: '긴급 알림',
			message: '긴급 상황 발생!',
			type: 'error',
			priority: 'urgent',
			read: false,
			received_at: '',
			target_type: 'all'
		}),

	// 사운드 알림
	playLowSound: () => playNotificationSound('low'),
	playNormalSound: () => playNotificationSound('normal'),
	playHighSound: () => playNotificationSound('high'),
	playUrgentSound: () => playNotificationSound('urgent'),

	// 진동 알림
	vibrateLow: () => vibrateDevice('low'),
	vibrateNormal: () => vibrateDevice('normal'),
	vibrateHigh: () => vibrateDevice('high'),
	vibrateUrgent: () => vibrateDevice('urgent'),
	stopVibration: () => stopVibration(),

	// 시스템 테스트
	testAll: () => testNotificationSystem('normal', 'all'),
	testBrowser: () => testNotificationSystem('normal', 'browser'),
	testSound: () => testNotificationSystem('normal', 'sound'),
	testVibration: () => testNotificationSystem('normal', 'vibration'),

	// 진단
	diagnose: () => diagnoseNotificationSystem(),

	// 사운드 사전 로드
	preloadSounds: () => preloadNotificationSounds()
};

// 브라우저 콘솔에서 사용할 수 있도록 전역 객체에 추가
if (typeof window !== 'undefined') {
	(window as any).notificationDemo = {
		runFullDemo,
		...notificationDemoFunctions
	};

	console.log('알림 데모 함수가 window.notificationDemo에 등록되었습니다.');
	console.log('사용 예: window.notificationDemo.testAll()');
}
