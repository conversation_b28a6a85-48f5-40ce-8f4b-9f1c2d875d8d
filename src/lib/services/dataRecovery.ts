/**
 * 데이터 복구 유틸리티
 */

/**
 * 브라우저 캐시에서 printSetting 데이터 복구 시도
 */
export async function attemptPrintSettingRecovery(): Promise<any[]> {
	const recoveredData: any[] = [];

	try {
		// 1. LocalStorage 확인
		const localStorageKeys = Object.keys(localStorage);
		for (const key of localStorageKeys) {
			if (key.includes('print') || key.includes('setting')) {
				try {
					const data = JSON.parse(localStorage.getItem(key) || '');
					console.log(`LocalStorage에서 발견: ${key}`, data);
					recoveredData.push({ source: 'localStorage', key, data });
				} catch (e) {
					// JSON 파싱 실패는 무시
				}
			}
		}

		// 2. SessionStorage 확인
		const sessionStorageKeys = Object.keys(sessionStorage);
		for (const key of sessionStorageKeys) {
			if (key.includes('print') || key.includes('setting')) {
				try {
					const data = JSON.parse(sessionStorage.getItem(key) || '');
					console.log(`SessionStorage에서 발견: ${key}`, data);
					recoveredData.push({ source: 'sessionStorage', key, data });
				} catch (e) {
					// JSON 파싱 실패는 무시
				}
			}
		}

		// 3. 다른 IndexedDB 확인
		const databases = await indexedDB.databases();
		console.log('사용 가능한 데이터베이스들:', databases);

		for (const dbInfo of databases) {
			if (dbInfo.name && dbInfo.name !== 'EmployeeNotificationDB') {
				try {
					const db = await new Promise<IDBDatabase>((resolve, reject) => {
						const request = indexedDB.open(dbInfo.name!);
						request.onsuccess = () => resolve(request.result);
						request.onerror = () => reject(request.error);
					});

					console.log(`DB ${dbInfo.name} 스토어들:`, Array.from(db.objectStoreNames));

					// settings 관련 스토어 찾기
					for (const storeName of db.objectStoreNames) {
						if (storeName.includes('setting') || storeName.includes('print')) {
							try {
								const transaction = db.transaction([storeName], 'readonly');
								const store = transaction.objectStore(storeName);
								const request = store.getAll();

								const data = await new Promise((resolve, reject) => {
									request.onsuccess = () => resolve(request.result);
									request.onerror = () => reject(request.error);
								});

								console.log(`${dbInfo.name}.${storeName}에서 발견:`, data);
								recoveredData.push({
									source: 'indexedDB',
									database: dbInfo.name,
									store: storeName,
									data
								});
							} catch (e) {
								console.warn(`${dbInfo.name}.${storeName} 접근 실패:`, e);
							}
						}
					}

					db.close();
				} catch (e) {
					console.warn(`DB ${dbInfo.name} 열기 실패:`, e);
				}
			}
		}
	} catch (error) {
		console.error('데이터 복구 시도 중 오류:', error);
	}

	return recoveredData;
}

/**
 * 복구된 데이터를 새 DB에 저장
 */
export async function restoreRecoveredData(
	recoveredData: any[],
	targetDb: IDBDatabase
): Promise<void> {
	for (const item of recoveredData) {
		try {
			if (item.data && Array.isArray(item.data)) {
				for (const dataItem of item.data) {
					// printSetting 형태로 변환
					const printSetting = {
						settingName: dataItem.settingName || `recovered_${Date.now()}`,
						settings: dataItem.settings || dataItem,
						updatedAt: dataItem.updatedAt || new Date().toISOString(),
						migrated: true,
						migratedAt: new Date().toISOString(),
						recovered: true,
						recoveredFrom: `${item.source}_${item.key || item.store}`
					};

					const transaction = targetDb.transaction(['print_settings'], 'readwrite');
					const store = transaction.objectStore('print_settings');
					await new Promise((resolve, reject) => {
						const request = store.add(printSetting);
						request.onsuccess = () => resolve(request.result);
						request.onerror = () => reject(request.error);
					});

					console.log('복구된 데이터 저장 완료:', printSetting.settingName);
				}
			}
		} catch (error) {
			console.error('복구 데이터 저장 실패:', error);
		}
	}
}

/**
 * 전체 복구 프로세스 실행
 */
export async function runDataRecovery(): Promise<void> {
	console.log('🚨 데이터 복구 프로세스 시작...');

	try {
		// 복구 시도
		const recoveredData = await attemptPrintSettingRecovery();

		if (recoveredData.length > 0) {
			console.log(`📦 ${recoveredData.length}개의 복구 가능한 데이터 발견!`);

			// 현재 DB 열기
			const db = await new Promise<IDBDatabase>((resolve, reject) => {
				const request = indexedDB.open('EmployeeNotificationDB');
				request.onsuccess = () => resolve(request.result);
				request.onerror = () => reject(request.error);
			});

			// 복구 데이터 저장
			await restoreRecoveredData(recoveredData, db);

			console.log('✅ 데이터 복구 완료!');
			db.close();
		} else {
			console.log('😞 복구 가능한 데이터를 찾을 수 없습니다.');
		}
	} catch (error) {
		console.error('❌ 데이터 복구 실패:', error);
	}
}
