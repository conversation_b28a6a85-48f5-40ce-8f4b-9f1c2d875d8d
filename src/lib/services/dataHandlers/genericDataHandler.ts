/**
 * 범용 데이터 업데이트 핸들러
 *
 * 확장 가능한 범용 데이터 타입 처리를 담당합니다.
 */

import {
	putData,
	putManyData,
	deleteData,
	clearStore,
	getAllData,
	getData
} from '$lib/services/indexedDBManager';

/**
 * 데이터 타입별 설정 인터페이스
 */
export interface DataTypeConfig {
	storeName: string;
	displayName: string;
	storeUpdater: (db: IDBDatabase) => Promise<void>;
	validatePayload?: (payload: any) => boolean;
	transformPayload?: (payload: any) => any;
	getItemSummary?: (item: any) => string;
}

/**
 * 확장 가능한 데이터 타입 레지스트리
 */
const dataTypeRegistry: Map<string, DataTypeConfig> = new Map([
	[
		'settings',
		{
			storeName: 'settings',
			displayName: '설정',
			storeUpdater: async () => {}, // 설정은 별도 스토어 업데이트 없음
			validatePayload: (payload) => payload && payload.key,
			transformPayload: (payload) => ({
				...payload,
				updated_at: new Date().toISOString()
			}),
			getItemSummary: (item) => `${item.key}: ${String(item.value).substring(0, 30)}`
		}
	],
	[
		'print_settings',
		{
			storeName: 'print_settings',
			displayName: '인쇄 설정',
			storeUpdater: async () => {}, // 인쇄 설정은 별도 스토어 업데이트 없음
			validatePayload: (payload) => payload && payload.key,
			transformPayload: (payload) => ({
				...payload,
				updated_at: new Date().toISOString()
			}),
			getItemSummary: (item) => `${item.key}: ${item.printer_name || 'N/A'}`
		}
	]
]);

/**
 * 새로운 데이터 타입 등록
 */
export function registerDataType(modelName: string, config: DataTypeConfig): void {
	dataTypeRegistry.set(modelName, config);
	console.log(`데이터 타입 등록 완료: ${modelName} (${config.displayName})`);
}

/**
 * 등록된 데이터 타입 목록 조회
 */
export function getRegisteredDataTypes(): string[] {
	return Array.from(dataTypeRegistry.keys());
}

/**
 * 확장 가능한 범용 데이터 업데이트 처리
 */
export async function handleGenericDataUpdate(
	db: IDBDatabase,
	modelName: string,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	const config = dataTypeRegistry.get(modelName);
	if (!config) {
		console.warn(`등록되지 않은 데이터 타입: ${modelName}`);
		return;
	}

	try {
		console.log(`${config.displayName} 업데이트 처리 시작:`, { action, affectedIds });

		// 페이로드 검증
		if (config.validatePayload && !config.validatePayload(payload)) {
			throw new Error(`잘못된 ${config.displayName} 데이터 형식`);
		}

		// 페이로드 변환
		const transformedPayload = config.transformPayload ? config.transformPayload(payload) : payload;

		// 액션별 처리
		switch (action) {
			case 'created':
				await handleGenericCreated(db, config, transformedPayload, affectedIds);
				break;

			case 'updated':
				await handleGenericUpdated(db, config, transformedPayload, affectedIds);
				break;

			case 'deleted':
				await handleGenericDeleted(db, config, affectedIds);
				break;

			case 'batch_updated':
				await handleGenericBatchUpdated(db, config, transformedPayload, affectedIds);
				break;

			case 'synchronized':
				await handleGenericSynchronized(db, config, transformedPayload, affectedIds);
				break;

			default:
				console.warn(`알 수 없는 ${config.displayName} 액션:`, action);
				return;
		}

		// 스토어 업데이트
		await config.storeUpdater(db);

		console.log(`${config.displayName} 업데이트 처리 완료:`, { action, count: affectedIds.length });
	} catch (error) {
		console.error(`${config.displayName} 업데이트 처리 실패:`, error);
		throw error;
	}
}

/**
 * 범용 생성 처리
 */
async function handleGenericCreated(
	db: IDBDatabase,
	config: DataTypeConfig,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (Array.isArray(payload)) {
		await putManyData(db, config.storeName, payload);

		// 상세한 생성 메시지
		const summaries = payload
			.slice(0, 3)
			.map((item) => config.getItemSummary?.(item) || `ID: ${item.id}`)
			.join(', ');

		const message =
			payload.length <= 3
				? `새로운 ${config.displayName}: ${summaries}`
				: `새로운 ${config.displayName} ${payload.length}개가 추가되었습니다. (${summaries} 외 ${payload.length - 3}개)`;

		console.log(message);
	} else {
		await putData(db, config.storeName, payload);

		const summary = config.getItemSummary?.(payload) || `ID: ${payload.id}`;
		console.log(`새로운 ${config.displayName}이 추가되었습니다: ${summary}`);
	}
}

/**
 * 범용 업데이트 처리
 */
async function handleGenericUpdated(
	db: IDBDatabase,
	config: DataTypeConfig,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (Array.isArray(payload)) {
		await putManyData(db, config.storeName, payload);

		// 변경사항 분석
		const changeAnalysis = await analyzeDataChanges(db, config.storeName, payload);

		let message = `${config.displayName} ${payload.length}개가 업데이트되었습니다.`;
		if (changeAnalysis.significantChanges > 0) {
			message += ` (주요 변경: ${changeAnalysis.significantChanges}개)`;
		}

		console.log(message);
	} else {
		// 기존 데이터와 비교
		const existingData = await getData(db, config.storeName, payload.id);
		const changes = detectItemChanges(existingData, payload);

		await putData(db, config.storeName, payload);

		const summary = config.getItemSummary?.(payload) || `ID: ${payload.id}`;
		let message = `${config.displayName}이 업데이트되었습니다: ${summary}`;

		if (changes.length > 0) {
			message += ` (변경: ${changes.slice(0, 2).join(', ')}${changes.length > 2 ? ' 외' : ''})`;
		}

		console.log(message);
	}
}

/**
 * 범용 삭제 처리
 */
async function handleGenericDeleted(
	db: IDBDatabase,
	config: DataTypeConfig,
	affectedIds: number[]
): Promise<void> {
	// 삭제 전 데이터 정보 수집 (복구 가능성을 위해)
	const deletedItems = [];
	for (const id of affectedIds) {
		try {
			const item = await getData(db, config.storeName, id);
			if (item) {
				deletedItems.push(item);
			}
		} catch (error) {
			console.warn(`삭제할 ${config.displayName} 항목 조회 실패 (ID: ${id}):`, error);
		}
	}

	// 실제 삭제 수행
	for (const id of affectedIds) {
		await deleteData(db, config.storeName, id);
	}

	// 삭제 메시지 표시
	if (deletedItems.length === 1 && config.getItemSummary) {
		const summary = config.getItemSummary(deletedItems[0]);
		console.log(`${config.displayName}이 삭제되었습니다: ${summary}`);
	} else {
		console.log(`${config.displayName} ${affectedIds.length}개가 삭제되었습니다.`);
	}

	// 삭제 히스토리 저장 (복구 가능성을 위해)
	if (deletedItems.length > 0) {
		await saveDeletedItemsHistory(db, config.displayName, deletedItems);
	}
}

/**
 * 범용 배치 업데이트 처리
 */
async function handleGenericBatchUpdated(
	db: IDBDatabase,
	config: DataTypeConfig,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!Array.isArray(payload)) {
		console.warn(`배치 업데이트 페이로드가 배열이 아닙니다 (${config.displayName}):`, payload);
		return;
	}

	// 배치 업데이트 전 현재 상태 분석
	const beforeCount = (await getAllData(db, config.storeName)).length;

	// 배치 업데이트 실행
	await putManyData(db, config.storeName, payload);

	const afterCount = (await getAllData(db, config.storeName)).length;
	const netChange = afterCount - beforeCount;

	// 상세한 배치 업데이트 메시지
	let message = `${config.displayName} ${payload.length}개가 일괄 업데이트되었습니다.`;

	if (netChange > 0) {
		message += ` (신규 추가: ${netChange}개)`;
	} else if (netChange < 0) {
		message += ` (제거됨: ${Math.abs(netChange)}개)`;
	}

	console.log(message);
}

/**
 * 범용 동기화 처리 (새로운 액션 타입)
 */
async function handleGenericSynchronized(
	db: IDBDatabase,
	config: DataTypeConfig,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!Array.isArray(payload)) {
		console.warn(`동기화 페이로드가 배열이 아닙니다 (${config.displayName}):`, payload);
		return;
	}

	// 전체 동기화: 기존 데이터 삭제 후 새 데이터 저장
	await clearStore(db, config.storeName);
	await putManyData(db, config.storeName, payload);

	console.log(`${config.displayName} 전체 동기화가 완료되었습니다. (${payload.length}개 항목)`);
}

/**
 * 데이터 변경사항 분석
 */
async function analyzeDataChanges(
	db: IDBDatabase,
	storeName: string,
	newData: any[]
): Promise<{
	significantChanges: number;
	minorChanges: number;
	totalChanges: number;
}> {
	try {
		const existingData = await getAllData(db, storeName);
		const existingMap = new Map(existingData.map((item) => [item.id, item]));

		let significantChanges = 0;
		let minorChanges = 0;

		for (const newItem of newData) {
			const existingItem = existingMap.get(newItem.id);
			if (existingItem) {
				const changes = detectItemChanges(existingItem, newItem);
				const significantFields = ['name', 'grade_name', 'level', 'status', 'active'];

				const hasSignificantChange = changes.some((field) => significantFields.includes(field));

				if (hasSignificantChange) {
					significantChanges++;
				} else if (changes.length > 0) {
					minorChanges++;
				}
			}
		}

		return {
			significantChanges,
			minorChanges,
			totalChanges: significantChanges + minorChanges
		};
	} catch (error) {
		console.error('데이터 변경사항 분석 실패:', error);
		return { significantChanges: 0, minorChanges: 0, totalChanges: 0 };
	}
}

/**
 * 개별 항목 변경사항 감지
 */
function detectItemChanges(existingItem: any, newItem: any): string[] {
	if (!existingItem || !newItem) {
		return [];
	}

	const changes: string[] = [];
	const excludeFields = ['updated_at', 'created_at', 'timestamp'];

	for (const key in newItem) {
		if (excludeFields.includes(key)) {
			continue;
		}

		const existingValue = existingItem[key];
		const newValue = newItem[key];

		// 깊은 비교를 위한 JSON 문자열 변환
		const existingStr = JSON.stringify(existingValue);
		const newStr = JSON.stringify(newValue);

		if (existingStr !== newStr) {
			changes.push(key);
		}
	}

	return changes;
}

/**
 * 삭제된 항목 히스토리 저장
 */
async function saveDeletedItemsHistory(
	db: IDBDatabase,
	dataTypeName: string,
	deletedItems: any[]
): Promise<void> {
	try {
		const historyItem = {
			id: crypto.randomUUID(),
			type: 'deletion_history',
			data_type: dataTypeName,
			deleted_items: deletedItems,
			deleted_at: new Date().toISOString(),
			item_count: deletedItems.length
		};

		// 삭제 히스토리 스토어에 저장 (최대 100개 유지)
		const currentHistory = (await getAllData(db, 'deletion_history')) || [];
		const newHistory = [historyItem, ...currentHistory.slice(0, 99)];

		await clearStore(db, 'deletion_history');
		await putManyData(db, 'deletion_history', newHistory);

		console.log(`${dataTypeName} 삭제 히스토리 저장 완료:`, deletedItems.length, '개 항목');
	} catch (error) {
		console.error('삭제 히스토리 저장 실패:', error);
	}
}
