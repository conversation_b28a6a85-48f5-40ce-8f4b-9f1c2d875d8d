/**
 * 업데이트 히스토리 관리자
 *
 * 데이터 업데이트 히스토리 및 삭제 히스토리 관리를 담당합니다.
 */

import type { DataUpdateMessage } from '$lib/types/sseTypes';
import { putManyData, clearStore, getAllData, getData } from '$lib/services/indexedDBManager';

/**
 * 업데이트 히스토리 추가
 *
 * @param db IDBDatabase 객체
 * @param updateData 업데이트 데이터
 * @param includePayload 페이로드 전체 포함 여부 (기본값: false, 요약만 저장)
 */
export async function addToUpdateHistory(
	db: IDBDatabase,
	updateData: DataUpdateMessage,
	includePayload: boolean = false
): Promise<void> {
	try {
		const historyItem = {
			id: crypto.randomUUID(),
			model: updateData.model,
			action: updateData.action,
			affected_ids: updateData.affected_ids || [],
			affected_count: updateData.affected_ids?.length || 0,
			payload_summary: getPayloadSummary(updateData.payload),
			payload_size: getPayloadSize(updateData.payload),
			timestamp: new Date().toISOString(),
			user_agent: navigator.userAgent,
			session_id: getSessionId(),
			// 전체 페이로드는 선택적으로만 저장 (용량 고려)
			...(includePayload && { full_payload: updateData.payload })
		};

		// 현재 히스토리 조회 (최대 49개 유지하여 새 항목과 함께 50개)
		const currentHistory = (await getAllData(db, 'update_history')) || [];
		const newHistory = [historyItem, ...currentHistory.slice(0, 49)];

		// 히스토리 스토어 업데이트
		await clearStore(db, 'update_history');
		await putManyData(db, 'update_history', newHistory);

		// 삭제 액션인 경우 삭제 히스토리에도 추가
		if (updateData.action === 'deleted' && updateData.payload) {
			await addToDeletionHistory(db, updateData);
		}

		console.log('업데이트 히스토리 추가 완료:', {
			id: historyItem.id,
			model: historyItem.model,
			action: historyItem.action,
			affectedCount: historyItem.affected_count,
			payloadSize: historyItem.payload_size
		});
	} catch (error) {
		console.error('업데이트 히스토리 저장 실패:', error);
		throw error;
	}
}

/**
 * 삭제 히스토리 추가
 *
 * @param db IDBDatabase 객체
 * @param updateData 업데이트 데이터
 */
async function addToDeletionHistory(db: IDBDatabase, updateData: DataUpdateMessage): Promise<void> {
	try {
		const deletionItem = {
			id: crypto.randomUUID(),
			data_type: updateData.model,
			deleted_ids: updateData.affected_ids || [],
			deleted_items: Array.isArray(updateData.payload) ? updateData.payload : [updateData.payload],
			item_count: updateData.affected_ids?.length || 0,
			deleted_at: new Date().toISOString(),
			can_restore: true,
			restore_deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30일 후
		};

		// 삭제 히스토리 조회 및 업데이트 (최대 100개 유지)
		const currentDeletionHistory = (await getAllData(db, 'deletion_history')) || [];
		const newDeletionHistory = [deletionItem, ...currentDeletionHistory.slice(0, 99)];

		await clearStore(db, 'deletion_history');
		await putManyData(db, 'deletion_history', newDeletionHistory);

		console.log('삭제 히스토리 추가 완료:', {
			id: deletionItem.id,
			dataType: deletionItem.data_type,
			itemCount: deletionItem.item_count
		});
	} catch (error) {
		console.error('삭제 히스토리 저장 실패:', error);
	}
}

/**
 * 세션 ID 조회 (브라우저 세션별 고유 ID)
 */
function getSessionId(): string {
	let sessionId = sessionStorage.getItem('sse_session_id');
	if (!sessionId) {
		sessionId = crypto.randomUUID();
		sessionStorage.setItem('sse_session_id', sessionId);
	}
	return sessionId;
}

/**
 * 페이로드 크기 계산
 */
function getPayloadSize(payload: any): number {
	try {
		if (!payload) return 0;

		const jsonString = JSON.stringify(payload);
		return new Blob([jsonString]).size;
	} catch (error) {
		console.error('페이로드 크기 계산 실패:', error);
		return 0;
	}
}

/**
 * 업데이트 히스토리 조회
 */
export async function getUpdateHistory(db: IDBDatabase, limit: number = 20): Promise<any[]> {
	try {
		const history = await getAllData(db, 'update_history');
		return history.slice(0, limit);
	} catch (error) {
		console.error('업데이트 히스토리 조회 실패:', error);
		return [];
	}
}

/**
 * 특정 모델의 업데이트 히스토리 조회
 */
export async function getModelUpdateHistory(
	db: IDBDatabase,
	model: string,
	limit: number = 10
): Promise<any[]> {
	try {
		const allHistory = await getAllData(db, 'update_history');
		const modelHistory = allHistory.filter((item) => item.model === model);
		return modelHistory.slice(0, limit);
	} catch (error) {
		console.error(`${model} 모델 업데이트 히스토리 조회 실패:`, error);
		return [];
	}
}

/**
 * 업데이트 통계 조회
 */
export async function getUpdateStatistics(db: IDBDatabase): Promise<{
	totalUpdates: number;
	modelCounts: Record<string, number>;
	actionCounts: Record<string, number>;
	recentUpdates: number;
}> {
	try {
		const allHistory = await getAllData(db, 'update_history');
		const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000);

		const modelCounts: Record<string, number> = {};
		const actionCounts: Record<string, number> = {};
		let recentUpdates = 0;

		allHistory.forEach((item) => {
			modelCounts[item.model] = (modelCounts[item.model] || 0) + 1;
			actionCounts[item.action] = (actionCounts[item.action] || 0) + 1;

			if (new Date(item.timestamp) > recentDate) {
				recentUpdates++;
			}
		});

		return {
			totalUpdates: allHistory.length,
			modelCounts,
			actionCounts,
			recentUpdates
		};
	} catch (error) {
		console.error('업데이트 통계 조회 실패:', error);
		return {
			totalUpdates: 0,
			modelCounts: {},
			actionCounts: {},
			recentUpdates: 0
		};
	}
}

/**
 * 업데이트 히스토리 정리
 */
export async function cleanupUpdateHistory(db: IDBDatabase, keepDays: number = 7): Promise<void> {
	try {
		const cutoffDate = new Date(Date.now() - keepDays * 24 * 60 * 60 * 1000);
		const allHistory = await getAllData(db, 'update_history');

		const recentHistory = allHistory.filter((item) => {
			const itemDate = new Date(item.timestamp);
			return itemDate > cutoffDate;
		});

		if (recentHistory.length < allHistory.length) {
			await clearStore(db, 'update_history');
			await putManyData(db, 'update_history', recentHistory);

			console.log(
				`업데이트 히스토리 정리 완료: ${allHistory.length - recentHistory.length}개 항목 삭제`
			);
		}
	} catch (error) {
		console.error('업데이트 히스토리 정리 실패:', error);
	}
}

/**
 * 삭제 히스토리 조회
 */
export async function getDeletionHistory(
	db: IDBDatabase,
	dataType?: string,
	limit: number = 20
): Promise<any[]> {
	try {
		const allHistory = await getAllData(db, 'deletion_history');

		let filteredHistory = allHistory;
		if (dataType) {
			filteredHistory = allHistory.filter((item) => item.data_type === dataType);
		}

		return filteredHistory
			.sort((a, b) => new Date(b.deleted_at).getTime() - new Date(a.deleted_at).getTime())
			.slice(0, limit);
	} catch (error) {
		console.error('삭제 히스토리 조회 실패:', error);
		return [];
	}
}

/**
 * 데이터 복구 (삭제 히스토리에서)
 */
export async function restoreDeletedItems(
	db: IDBDatabase,
	historyId: string,
	dataTypeRegistry: Map<string, any>
): Promise<boolean> {
	try {
		const historyItem = await getData(db, 'deletion_history', historyId);
		if (!historyItem || !historyItem.deleted_items) {
			throw new Error('복구할 데이터를 찾을 수 없습니다.');
		}

		const config = dataTypeRegistry.get(historyItem.data_type);
		if (!config) {
			throw new Error(`알 수 없는 데이터 타입: ${historyItem.data_type}`);
		}

		// 삭제된 항목들을 다시 저장
		await putManyData(db, config.storeName, historyItem.deleted_items);

		// 스토어 업데이트
		await config.storeUpdater(db);

		console.log(`데이터 복구 완료: ${historyItem.data_type}`, historyItem.item_count, '개 항목');
		return true;
	} catch (error) {
		console.error('데이터 복구 실패:', error);
		return false;
	}
}

/**
 * 페이로드 요약 생성
 */
function getPayloadSummary(payload: any): string {
	try {
		if (!payload) {
			return '데이터 없음';
		}

		if (Array.isArray(payload)) {
			return `${payload.length}개 항목`;
		}

		if (typeof payload === 'object') {
			const keys = Object.keys(payload);
			if (keys.length === 0) {
				return '빈 객체';
			}

			const importantFields = ['id', 'name', 'title', 'key', 'cate4', 'cate5'];
			const summary = importantFields
				.filter((field) => payload[field] !== undefined)
				.map((field) => {
					const value = payload[field];
					if (Array.isArray(value)) {
						return `${field}: ${value.length}개`;
					}
					return `${field}: ${String(value).substring(0, 20)}`;
				})
				.join(', ');

			return summary || `${keys.length}개 필드`;
		}

		return String(payload).substring(0, 50);
	} catch (error) {
		console.error('페이로드 요약 생성 실패:', error);
		return '요약 생성 실패';
	}
}
