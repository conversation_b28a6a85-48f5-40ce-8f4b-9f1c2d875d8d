/**
 * 데이터 통계 관리자
 *
 * 각 데이터 타입별 통계 및 상태 분석을 담당합니다.
 */

import { getAllData } from '$lib/services/indexedDBManager';
import type { DataTypeConfig } from '$lib/services/dataHandlers/genericDataHandler';

/**
 * 특정 데이터 타입의 통계 조회
 */
export async function getDataTypeStatistics(
	db: IDBDatabase,
	modelName: string,
	config: DataTypeConfig
): Promise<{
	totalItems: number;
	lastUpdate: string | null;
	recentChanges: number;
	dataHealth: 'good' | 'warning' | 'error';
}> {
	try {
		const allData = await getAllData(db, config.storeName);
		const recentDate = new Date(Date.now() - 24 * 60 * 60 * 1000);

		// 마지막 업데이트 시간 찾기
		const lastUpdate = allData.reduce((latest, item) => {
			const itemUpdate = item.updated_at || item.created_at;
			if (!latest || (itemUpdate && new Date(itemUpdate) > new Date(latest))) {
				return itemUpdate;
			}
			return latest;
		}, null);

		// 최근 변경사항 수 계산
		const recentChanges = allData.filter((item) => {
			const itemDate = new Date(item.updated_at || item.created_at || 0);
			return itemDate > recentDate;
		}).length;

		// 데이터 건강도 평가
		let dataHealth: 'good' | 'warning' | 'error' = 'good';

		if (allData.length === 0) {
			dataHealth = 'warning';
		} else if (
			!lastUpdate ||
			new Date(lastUpdate) < new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
		) {
			dataHealth = 'warning'; // 7일 이상 업데이트 없음
		}

		// 데이터 무결성 검사
		const hasInvalidItems = allData.some((item) => !item.id);
		if (hasInvalidItems) {
			dataHealth = 'error';
		}

		return {
			totalItems: allData.length,
			lastUpdate,
			recentChanges,
			dataHealth
		};
	} catch (error) {
		console.error(`${modelName} 통계 조회 실패:`, error);
		return {
			totalItems: 0,
			lastUpdate: null,
			recentChanges: 0,
			dataHealth: 'error'
		};
	}
}

/**
 * 직원 동기화 상태 확인
 */
export async function getEmployeeSyncStatus(db: IDBDatabase): Promise<{
	totalEmployees: number;
	lastUpdate: string | null;
	groupMembershipConsistency: boolean;
}> {
	try {
		const employees = await getAllData(db, 'employees');
		const groups = await getAllData(db, 'groups');

		// 마지막 업데이트 시간 찾기
		const lastUpdate = employees.reduce((latest, emp) => {
			const empUpdate = emp.updated_at || emp.created_at;
			if (!latest || (empUpdate && new Date(empUpdate) > new Date(latest))) {
				return empUpdate;
			}
			return latest;
		}, null);

		// 그룹 멤버십 일관성 확인
		let groupMembershipConsistency = true;
		for (const employee of employees) {
			const empGroupIds = employee.group_ids || [];
			for (const groupId of empGroupIds) {
				const group = groups.find((g) => g.id === groupId);
				if (group) {
					const groupMemberIds = group.member_ids || [];
					if (!groupMemberIds.includes(employee.id)) {
						groupMembershipConsistency = false;
						break;
					}
				}
			}
			if (!groupMembershipConsistency) break;
		}

		return {
			totalEmployees: employees.length,
			lastUpdate,
			groupMembershipConsistency
		};
	} catch (error) {
		console.error('직원 동기화 상태 확인 실패:', error);
		throw error;
	}
}

/**
 * 그룹 동기화 상태 확인
 */
export async function getGroupSyncStatus(db: IDBDatabase): Promise<{
	totalGroups: number;
	lastUpdate: string | null;
	membershipConsistency: boolean;
	averageMembersPerGroup: number;
}> {
	try {
		const groups = await getAllData(db, 'groups');
		const employees = await getAllData(db, 'employees');

		// 마지막 업데이트 시간 찾기
		const lastUpdate = groups.reduce((latest, group) => {
			const groupUpdate = group.updated_at || group.created_at;
			if (!latest || (groupUpdate && new Date(groupUpdate) > new Date(latest))) {
				return groupUpdate;
			}
			return latest;
		}, null);

		// 멤버십 일관성 확인
		let membershipConsistency = true;
		for (const group of groups) {
			const groupMemberIds = group.member_ids || [];
			for (const memberId of groupMemberIds) {
				const employee = employees.find((e) => e.id === memberId);
				if (employee) {
					const empGroupIds = employee.group_ids || [];
					if (!empGroupIds.includes(group.id)) {
						membershipConsistency = false;
						break;
					}
				}
			}
			if (!membershipConsistency) break;
		}

		// 평균 멤버 수 계산
		const totalMembers = groups.reduce((sum, group) => sum + (group.member_ids?.length || 0), 0);
		const averageMembersPerGroup = groups.length > 0 ? totalMembers / groups.length : 0;

		return {
			totalGroups: groups.length,
			lastUpdate,
			membershipConsistency,
			averageMembersPerGroup: Math.round(averageMembersPerGroup * 100) / 100
		};
	} catch (error) {
		console.error('그룹 동기화 상태 확인 실패:', error);
		throw error;
	}
}
