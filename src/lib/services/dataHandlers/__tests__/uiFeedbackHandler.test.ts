/**
 * UI 피드백 핸들러 테스트
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { get } from 'svelte/store';
import {
	showUpdateMessage,
	removeUpdateMessage,
	clearAllUpdateMessages,
	showSuccessMessage,
	showInfoMessage,
	showWarningMessage,
	showErrorMessage,
	highlightNewItems,
	highlightUpdatedItems,
	highlightDeletedItems,
	highlightBatchUpdatedItems,
	clearAllHighlights,
	showComprehensiveFeedback
} from '../uiFeedbackHandler';
import {
	updateMessageStore,
	highlightedItemsStore,
	updatedItemsStore,
	deletedItemsStore,
	batchUpdatedItemsStore
} from '../../../stores';

describe('UI 피드백 핸들러', () => {
	beforeEach(() => {
		// 각 테스트 전에 스토어 초기화
		updateMessageStore.set([]);
		highlightedItemsStore.set(new Set());
		updatedItemsStore.set(new Set());
		deletedItemsStore.set(new Set());
		batchUpdatedItemsStore.set(new Set());

		// 타이머 모킹
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	describe('메시지 표시 함수들', () => {
		it('기본 업데이트 메시지를 표시해야 함', () => {
			showUpdateMessage('테스트 메시지', 'info');

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);
			expect(messages[0].message).toBe('테스트 메시지');
			expect(messages[0].type).toBe('info');
			expect(messages[0].id).toBeDefined();
			expect(messages[0].timestamp).toBeDefined();
		});

		it('지속시간이 설정된 메시지는 자동으로 제거되어야 함', () => {
			showUpdateMessage('자동 제거 메시지', 'success', 1000);

			let messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);

			// 1초 후 메시지가 제거되어야 함
			vi.advanceTimersByTime(1000);

			messages = get(updateMessageStore);
			expect(messages).toHaveLength(0);
		});

		it('최대 10개 메시지만 유지해야 함', () => {
			// 15개 메시지 추가
			for (let i = 0; i < 15; i++) {
				showUpdateMessage(`메시지 ${i}`, 'info');
			}

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(10);
			expect(messages[0].message).toBe('메시지 14'); // 가장 최근 메시지
		});

		it('편의 함수들이 올바른 타입으로 메시지를 표시해야 함', () => {
			showSuccessMessage('성공 메시지');
			showInfoMessage('정보 메시지');
			showWarningMessage('경고 메시지');
			showErrorMessage('오류 메시지');

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(4);
			expect(messages.find((m) => m.message === '성공 메시지')?.type).toBe('success');
			expect(messages.find((m) => m.message === '정보 메시지')?.type).toBe('info');
			expect(messages.find((m) => m.message === '경고 메시지')?.type).toBe('warning');
			expect(messages.find((m) => m.message === '오류 메시지')?.type).toBe('error');
		});

		it('특정 메시지를 제거할 수 있어야 함', () => {
			showUpdateMessage('메시지 1', 'info');
			showUpdateMessage('메시지 2', 'info');

			let messages = get(updateMessageStore);
			expect(messages).toHaveLength(2);

			// 첫 번째 메시지(가장 최근)는 '메시지 2'이므로 두 번째 메시지를 제거
			const messageId = messages[1].id;
			removeUpdateMessage(messageId);

			messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);
			expect(messages[0].message).toBe('메시지 2');
		});

		it('모든 메시지를 제거할 수 있어야 함', () => {
			showUpdateMessage('메시지 1', 'info');
			showUpdateMessage('메시지 2', 'info');

			let messages = get(updateMessageStore);
			expect(messages).toHaveLength(2);

			clearAllUpdateMessages();

			messages = get(updateMessageStore);
			expect(messages).toHaveLength(0);
		});
	});

	describe('하이라이트 함수들', () => {
		it('새 항목을 하이라이트해야 함', () => {
			const ids = [1, 2, 3];
			highlightNewItems(ids, 1000);

			let highlightedItems = get(highlightedItemsStore);
			expect(highlightedItems.size).toBe(3);
			expect(highlightedItems.has(1)).toBe(true);
			expect(highlightedItems.has(2)).toBe(true);
			expect(highlightedItems.has(3)).toBe(true);

			// 1초 후 하이라이트가 제거되어야 함
			vi.advanceTimersByTime(1000);

			highlightedItems = get(highlightedItemsStore);
			expect(highlightedItems.size).toBe(0);
		});

		it('업데이트된 항목을 하이라이트해야 함', () => {
			const ids = [4, 5, 6];
			highlightUpdatedItems(ids, 1500);

			let updatedItems = get(updatedItemsStore);
			expect(updatedItems.size).toBe(3);
			expect(updatedItems.has(4)).toBe(true);
			expect(updatedItems.has(5)).toBe(true);
			expect(updatedItems.has(6)).toBe(true);

			// 1.5초 후 하이라이트가 제거되어야 함
			vi.advanceTimersByTime(1500);

			updatedItems = get(updatedItemsStore);
			expect(updatedItems.size).toBe(0);
		});

		it('삭제된 항목을 하이라이트해야 함', () => {
			const ids = [7, 8, 9];
			highlightDeletedItems(ids, 2000, false); // 경고 메시지 비활성화

			let deletedItems = get(deletedItemsStore);
			expect(deletedItems.size).toBe(3);
			expect(deletedItems.has(7)).toBe(true);
			expect(deletedItems.has(8)).toBe(true);
			expect(deletedItems.has(9)).toBe(true);

			// 2초 후 하이라이트가 제거되어야 함
			vi.advanceTimersByTime(2000);

			deletedItems = get(deletedItemsStore);
			expect(deletedItems.size).toBe(0);
		});

		it('배치 업데이트 항목을 파도 효과로 하이라이트해야 함', () => {
			const ids = [10, 11, 12, 13, 14];
			highlightBatchUpdatedItems(ids, 2500, 5);

			// 배치 업데이트 스토어에 추가되어야 함
			let batchItems = get(batchUpdatedItemsStore);
			expect(batchItems.size).toBe(5);

			// 파도 효과 확인을 위해 시간 진행
			vi.advanceTimersByTime(500); // 첫 번째 파도

			let updatedItems = get(updatedItemsStore);
			expect(updatedItems.size).toBeGreaterThan(0);

			// 전체 애니메이션 완료 후
			vi.advanceTimersByTime(3000);

			batchItems = get(batchUpdatedItemsStore);
			expect(batchItems.size).toBe(0);
		});

		it('빈 ID 배열로 호출 시 경고를 출력해야 함', () => {
			const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

			highlightNewItems([]);
			highlightUpdatedItems([]);
			highlightDeletedItems([]);

			expect(consoleSpy).toHaveBeenCalledTimes(3);
			expect(consoleSpy).toHaveBeenCalledWith('하이라이트할 항목 ID가 없습니다.');

			consoleSpy.mockRestore();
		});

		it('모든 하이라이트를 제거할 수 있어야 함', () => {
			// 각 스토어에 항목 추가
			highlightNewItems([1, 2], 5000);
			highlightUpdatedItems([3, 4], 5000);
			highlightDeletedItems([5, 6], 5000, false);
			highlightBatchUpdatedItems([7, 8], 5000);

			// 모든 스토어에 항목이 있는지 확인
			expect(get(highlightedItemsStore).size).toBe(2);
			expect(get(updatedItemsStore).size).toBeGreaterThan(0);
			expect(get(deletedItemsStore).size).toBe(2);
			expect(get(batchUpdatedItemsStore).size).toBe(2);

			// 모든 하이라이트 제거
			clearAllHighlights();

			// 모든 스토어가 비어있어야 함
			expect(get(highlightedItemsStore).size).toBe(0);
			expect(get(updatedItemsStore).size).toBe(0);
			expect(get(deletedItemsStore).size).toBe(0);
			expect(get(batchUpdatedItemsStore).size).toBe(0);
		});
	});

	describe('통합 피드백 함수', () => {
		it('성공적인 생성 작업에 대한 피드백을 표시해야 함', () => {
			showComprehensiveFeedback('created', 5, '카테고리', true);

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);
			expect(messages[0].message).toBe('5개 카테고리 생성이 완료되었습니다.');
			expect(messages[0].type).toBe('success');
		});

		it('실패한 업데이트 작업에 대한 피드백을 표시해야 함', () => {
			showComprehensiveFeedback('updated', 3, '직원', false);

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);
			expect(messages[0].message).toBe('3개 직원 업데이트 중 오류가 발생했습니다.');
			expect(messages[0].type).toBe('error');
		});

		it('단일 항목에 대한 피드백을 표시해야 함', () => {
			showComprehensiveFeedback('deleted', 1, '그룹', true);

			const messages = get(updateMessageStore);
			expect(messages).toHaveLength(1);
			expect(messages[0].message).toBe('그룹 삭제이 완료되었습니다.');
			expect(messages[0].type).toBe('success');
		});
	});

	describe('오류 처리', () => {
		it('잘못된 입력에 대해 오류를 처리해야 함', () => {
			const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

			// null 값으로 함수 호출
			highlightNewItems(null as any);
			highlightUpdatedItems(null as any);

			expect(consoleWarnSpy).toHaveBeenCalled();
			consoleWarnSpy.mockRestore();
		});
	});
});
