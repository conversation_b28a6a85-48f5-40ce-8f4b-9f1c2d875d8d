/**
 * 카테고리 데이터 업데이트 핸들러
 *
 * 카테고리 관련 데이터 업데이트 처리를 담당합니다.
 */

import { putData, getData } from '$lib/services/indexedDBManager';
import { updateCategoryStore } from '$lib/services/storeUpdaters';
import {
	showSuccessMessage,
	showInfoMessage,
	showWarningMessage,
	highlightNewItems,
	highlightUpdatedItems,
	highlightDeletedItems,
	highlightBatchUpdatedItems
} from '$lib/services/dataHandlers/uiFeedbackHandler';

/**
 * 카테고리 업데이트 처리
 */
export async function handleCategoryUpdate(
	db: IDBDatabase,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('카테고리 업데이트 처리 시작:', { action, affectedIds });

		switch (action) {
			case 'created':
				await handleCategoryCreated(db, payload, affectedIds);
				break;
			case 'updated':
				await handleCategoryUpdated(db, payload, affectedIds);
				break;
			case 'deleted':
				await handleCategoryDeleted(db, affectedIds);
				break;
			case 'batch_updated':
				await handleCategoryBatchUpdated(db, payload, affectedIds);
				break;
			default:
				console.warn('알 수 없는 카테고리 액션:', action);
		}

		// 스토어 업데이트
		await updateCategoryStore(db);

		console.log('카테고리 업데이트 처리 완료:', affectedIds);
	} catch (error) {
		console.error('카테고리 업데이트 처리 실패:', error);
		throw error;
	}
}

/**
 * 카테고리 생성 처리
 */
async function handleCategoryCreated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		// 기존 카테고리 데이터 조회
		const existingData = await getData(db, 'categories', 'main');

		// 새로운 카테고리 데이터 병합
		const mergedData = {
			key: 'main',
			cate4: [...(existingData?.cate4 || []), ...(payload.cate4 || [])],
			cate5: [...(existingData?.cate5 || []), ...(payload.cate5 || [])],
			updated_at: new Date().toISOString()
		};

		// 중복 제거 (ID 기준)
		mergedData.cate4 = removeDuplicatesById(mergedData.cate4);
		mergedData.cate5 = removeDuplicatesById(mergedData.cate5);

		await putData(db, 'categories', mergedData);

		// UI 피드백 표시
		const addedCount = affectedIds.length;
		showSuccessMessage(`새로운 카테고리 ${addedCount}개가 추가되었습니다.`, 3000);
		highlightNewItems(affectedIds, 3000, 'glow');

		console.log(`새로운 카테고리 ${addedCount}개가 추가되었습니다.`);
	} catch (error) {
		console.error('카테고리 생성 처리 실패:', error);
		throw error;
	}
}

/**
 * 카테고리 업데이트 처리
 */
async function handleCategoryUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		// 기존 데이터와 비교하여 변경사항 확인
		const existingData = await getData(db, 'categories', 'main');
		const changes = detectCategoryChanges(existingData, payload);

		// 업데이트된 데이터 저장
		await putData(db, 'categories', {
			key: 'main',
			cate4: payload.cate4 || [],
			cate5: payload.cate5 || [],
			updated_at: new Date().toISOString()
		});

		// UI 피드백 표시
		const updatedCount = affectedIds.length;
		let message = `카테고리 ${updatedCount}개가 업데이트되었습니다.`;

		if (changes.added > 0) {
			message += ` (신규: ${changes.added}개)`;
		}
		if (changes.modified > 0) {
			message += ` (수정: ${changes.modified}개)`;
		}

		showInfoMessage(message, 4000);
		highlightUpdatedItems(affectedIds, 2500, 'normal');

		console.log(message);
	} catch (error) {
		console.error('카테고리 업데이트 처리 실패:', error);
		throw error;
	}
}

/**
 * 카테고리 삭제 처리
 */
async function handleCategoryDeleted(db: IDBDatabase, affectedIds: number[]): Promise<void> {
	try {
		const currentData = await getData(db, 'categories', 'main');

		if (!currentData) {
			console.warn('삭제할 카테고리 데이터가 없습니다.');
			return;
		}

		// 삭제 처리
		const updatedCate4 = currentData.cate4.filter((item: any) => !affectedIds.includes(item.id));
		const updatedCate5 = currentData.cate5.filter((item: any) => !affectedIds.includes(item.id));

		await putData(db, 'categories', {
			key: 'main',
			cate4: updatedCate4,
			cate5: updatedCate5,
			updated_at: new Date().toISOString()
		});

		// UI 피드백 표시
		const deletedCount = affectedIds.length;
		showWarningMessage(`카테고리 ${deletedCount}개가 삭제되었습니다.`, 4000);
		highlightDeletedItems(affectedIds, 2000, true);

		console.log(`카테고리 ${deletedCount}개가 삭제되었습니다.`);
	} catch (error) {
		console.error('카테고리 삭제 처리 실패:', error);
		throw error;
	}
}

/**
 * 카테고리 배치 업데이트 처리
 */
async function handleCategoryBatchUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		// 배치 업데이트 실행
		await putData(db, 'categories', {
			key: 'main',
			cate4: payload.cate4 || [],
			cate5: payload.cate5 || [],
			updated_at: new Date().toISOString()
		});

		// UI 피드백 표시
		const totalCount = affectedIds.length;
		const message = `${totalCount}개 카테고리가 일괄 업데이트되었습니다.`;

		showSuccessMessage(message, 4000);
		highlightBatchUpdatedItems(affectedIds, 3000, 6);

		console.log(message);
	} catch (error) {
		console.error('카테고리 배치 업데이트 처리 실패:', error);
		throw error;
	}
}

/**
 * ID 기준 중복 제거
 */
function removeDuplicatesById(items: any[]): any[] {
	const seen = new Set();
	return items.filter((item) => {
		if (seen.has(item.id)) {
			return false;
		}
		seen.add(item.id);
		return true;
	});
}

/**
 * 카테고리 변경사항 감지
 */
function detectCategoryChanges(
	existingData: any,
	newPayload: any
): {
	added: number;
	modified: number;
	removed: number;
} {
	if (!existingData) {
		return {
			added: (newPayload.cate4?.length || 0) + (newPayload.cate5?.length || 0),
			modified: 0,
			removed: 0
		};
	}

	const existingIds = new Set([
		...(existingData.cate4 || []).map((item: any) => item.id),
		...(existingData.cate5 || []).map((item: any) => item.id)
	]);

	const newIds = new Set([
		...(newPayload.cate4 || []).map((item: any) => item.id),
		...(newPayload.cate5 || []).map((item: any) => item.id)
	]);

	const added = [...newIds].filter((id) => !existingIds.has(id)).length;
	const removed = [...existingIds].filter((id) => !newIds.has(id)).length;
	const modified = [...newIds].filter((id) => existingIds.has(id)).length;

	return { added, modified, removed };
}
