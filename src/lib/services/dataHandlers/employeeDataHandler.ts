/**
 * 직원 데이터 업데이트 핸들러
 */

import { putData, putManyData, deleteData, getAllData, getData } from '../indexedDBManager';
import { showSimpleBrowserNotification } from '$lib/services/notificationHandler';
import { updateEmployeeStore, updateGroupStore } from '$lib/services/storeUpdaters';
import {
	showUpdateMessage,
	highlightNewItems,
	highlightUpdatedItems,
	highlightDeletedItems,
	highlightBatchUpdatedItems
} from '$lib/services/dataHandlers/uiFeedbackHandler';

/**
 * 직원 정보 업데이트 처리
 */
export async function handleEmployeeUpdate(
	db: IDBDatabase,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('직원 정보 업데이트 처리 시작:', {
			action,
			affectedIds,
			payloadType: Array.isArray(payload) ? 'array' : typeof payload
		});

		switch (action) {
			case 'created':
				await handleEmployeeCreated(db, payload, affectedIds);
				break;
			case 'updated':
				await handleEmployeeUpdated(db, payload, affectedIds);
				break;
			case 'deleted':
				await handleEmployeeDeleted(db, affectedIds);
				break;
			case 'batch_updated':
				await handleEmployeeBatchUpdated(db, payload, affectedIds);
				break;
			case 'group_membership_changed':
				await handleEmployeeGroupMembershipChanged(db, payload, affectedIds);
				break;
			default:
				console.warn('알 수 없는 직원 액션:', action);
				return;
		}

		// 스토어 업데이트
		await updateEmployeeStore(db);

		// 그룹 멤버십 변경이 있는 경우 그룹 스토어도 업데이트
		if (action === 'group_membership_changed' || hasGroupMembershipChanges(payload)) {
			await updateGroupStore(db);
		}

		console.log('직원 정보 업데이트 처리 완료:', { action, affectedCount: affectedIds.length });
	} catch (error) {
		console.error('직원 정보 업데이트 처리 실패:', error);
		showUpdateMessage('직원 정보 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 직원 생성 처리
 */
async function handleEmployeeCreated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (Array.isArray(payload)) {
			// 다중 직원 생성
			await putManyData(db, 'employees', payload);

			const employeeNames = payload
				.map((emp) => emp.name || emp.email)
				.slice(0, 3)
				.join(', ');

			let message = `새로운 직원 ${payload.length}명이 추가되었습니다.`;
			if (employeeNames) {
				message += ` (${employeeNames}${payload.length > 3 ? ' 외' : ''})`;
			}

			showUpdateMessage(message, 'success');
			highlightNewItems(affectedIds, 4000);

			// 브라우저 알림 (5명 이상인 경우)
			if (payload.length >= 5) {
				showSimpleBrowserNotification(
					'직원 추가',
					message,
					'/icons/employee-update.png',
					'employee-update'
				);
			}
		} else {
			// 단일 직원 생성
			await putData(db, 'employees', payload);

			const employeeName = payload.name || payload.email || '새 직원';
			showUpdateMessage(`${employeeName}님이 추가되었습니다.`, 'success');
			highlightNewItems(affectedIds, 3000);
		}

		console.log('직원 생성 처리 완료:', {
			count: Array.isArray(payload) ? payload.length : 1,
			affectedIds
		});
	} catch (error) {
		console.error('직원 생성 처리 실패:', error);
		showUpdateMessage('직원 추가 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 직원 정보 업데이트 처리
 */
async function handleEmployeeUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (Array.isArray(payload)) {
			// 다중 직원 업데이트
			const existingEmployees = await Promise.all(
				payload.map((emp) => getData(db, 'employees', emp.id).catch(() => null))
			);

			// 변경사항 분석
			const changes = analyzeEmployeeChanges(existingEmployees, payload);

			await putManyData(db, 'employees', payload);

			let message = `직원 ${payload.length}명의 정보가 업데이트되었습니다.`;
			if (changes.groupChanges > 0) {
				message += ` (그룹 변경: ${changes.groupChanges}명)`;
			}
			if (changes.statusChanges > 0) {
				message += ` (상태 변경: ${changes.statusChanges}명)`;
			}

			showUpdateMessage(message, 'info');
			highlightUpdatedItems(affectedIds, 3000);

			// 중요한 변경사항인 경우 브라우저 알림
			if (changes.groupChanges > 0 || changes.statusChanges > 3) {
				showSimpleBrowserNotification(
					'직원 정보 업데이트',
					message,
					'/icons/employee-update.png',
					'employee-update'
				);
			}
		} else {
			// 단일 직원 업데이트
			const existingEmployee = await getData(db, 'employees', payload.id).catch(() => null);
			const changes = analyzeEmployeeChanges([existingEmployee], [payload]);

			await putData(db, 'employees', payload);

			const employeeName = payload.name || payload.email || '직원';
			let message = `${employeeName}님의 정보가 업데이트되었습니다.`;

			if (changes.groupChanges > 0) {
				message += ' (그룹 변경됨)';
			}

			showUpdateMessage(message, 'info');
			highlightUpdatedItems(affectedIds, 2000);
		}

		console.log('직원 업데이트 처리 완료:', {
			count: Array.isArray(payload) ? payload.length : 1,
			affectedIds
		});
	} catch (error) {
		console.error('직원 업데이트 처리 실패:', error);
		showUpdateMessage('직원 정보 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 직원 삭제 처리
 */
async function handleEmployeeDeleted(db: IDBDatabase, affectedIds: number[]): Promise<void> {
	try {
		// 삭제될 직원 정보 수집 (삭제 전)
		const deletedEmployees = await Promise.all(
			affectedIds.map((id) => getData(db, 'employees', id).catch(() => null))
		);

		const validDeletedEmployees = deletedEmployees.filter((emp) => emp !== null);

		// 삭제 실행
		for (const id of affectedIds) {
			await deleteData(db, 'employees', id);
		}

		// 삭제 메시지 생성
		const deletedCount = affectedIds.length;
		const deletedNames = validDeletedEmployees
			.map((emp) => emp.name || emp.email)
			.slice(0, 3)
			.join(', ');

		let message = `직원 ${deletedCount}명이 삭제되었습니다.`;
		if (deletedNames) {
			message += ` (${deletedNames}${deletedCount > 3 ? ' 외' : ''})`;
		}

		showUpdateMessage(message, 'warning');
		highlightDeletedItems(affectedIds, 2500);

		// 다수 삭제인 경우 브라우저 알림
		if (deletedCount > 1) {
			showSimpleBrowserNotification(
				'직원 삭제',
				message,
				'/icons/employee-update.png',
				'employee-update'
			);
		}

		// 그룹 멤버십 정리 (삭제된 직원들의 그룹에서 제거)
		await cleanupDeletedEmployeeFromGroups(db, affectedIds);

		console.log('직원 삭제 처리 완료:', { deletedCount, affectedIds });
	} catch (error) {
		console.error('직원 삭제 처리 실패:', error);
		showUpdateMessage('직원 삭제 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 직원 배치 업데이트 처리
 */
async function handleEmployeeBatchUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (!Array.isArray(payload)) {
			console.warn('배치 업데이트 페이로드가 배열이 아닙니다:', payload);
			return;
		}

		// 기존 직원 데이터 조회
		const existingEmployees = await Promise.all(
			payload.map((emp) => getData(db, 'employees', emp.id).catch(() => null))
		);

		// 배치 변경사항 분석
		const batchChanges = analyzeBatchEmployeeChanges(existingEmployees, payload);

		// 배치 업데이트 실행
		await putManyData(db, 'employees', payload);

		// 상세한 배치 업데이트 메시지
		const totalCount = payload.length;
		let message = `${totalCount}명의 직원 정보가 일괄 업데이트되었습니다.`;

		if (batchChanges.newEmployees > 0) {
			message += ` (신규: ${batchChanges.newEmployees}명)`;
		}
		if (batchChanges.groupChanges > 0) {
			message += ` (그룹 변경: ${batchChanges.groupChanges}명)`;
		}
		if (batchChanges.statusChanges > 0) {
			message += ` (상태 변경: ${batchChanges.statusChanges}명)`;
		}

		showUpdateMessage(message, 'info');

		// 배치 업데이트 애니메이션 (파도 효과)
		highlightBatchUpdatedItems(affectedIds, 3000);

		// 대규모 배치 업데이트인 경우 브라우저 알림
		if (totalCount > 10 || batchChanges.groupChanges > 5) {
			showSimpleBrowserNotification(
				'대규모 직원 정보 업데이트',
				message,
				'/icons/employee-update.png',
				'employee-update'
			);
		}

		console.log('직원 배치 업데이트 처리 완료:', {
			totalCount,
			changes: batchChanges,
			affectedIds: affectedIds.slice(0, 10) // 처음 10개만 로깅
		});
	} catch (error) {
		console.error('직원 배치 업데이트 처리 실패:', error);
		showUpdateMessage('직원 일괄 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 직원 그룹 멤버십 변경 처리
 */
async function handleEmployeeGroupMembershipChanged(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('직원 그룹 멤버십 변경 처리:', { payload, affectedIds });

		if (Array.isArray(payload)) {
			// 다중 직원의 그룹 멤버십 변경
			await putManyData(db, 'employees', payload);

			const membershipChanges = analyzeMembershipChanges(payload);

			let message = `${payload.length}명의 그룹 멤버십이 변경되었습니다.`;
			if (membershipChanges.addedToGroups > 0) {
				message += ` (그룹 추가: ${membershipChanges.addedToGroups}건)`;
			}
			if (membershipChanges.removedFromGroups > 0) {
				message += ` (그룹 제거: ${membershipChanges.removedFromGroups}건)`;
			}

			showUpdateMessage(message, 'info');
		} else {
			// 단일 직원의 그룹 멤버십 변경
			await putData(db, 'employees', payload);

			const employeeName = payload.name || payload.email || '직원';
			const groupNames = payload.groups?.map((g: any) => g.name).join(', ') || '없음';

			showUpdateMessage(
				`${employeeName}님의 그룹이 변경되었습니다. (현재 그룹: ${groupNames})`,
				'info'
			);
		}

		// 그룹 멤버십 변경 하이라이트 (특별한 색상)
		highlightNewItems(affectedIds, 3500, 'glow');

		// 그룹 데이터도 동기화 (그룹의 멤버 목록 업데이트)
		await syncGroupMembershipData(db, payload);

		console.log('직원 그룹 멤버십 변경 처리 완료:', affectedIds);
	} catch (error) {
		console.error('직원 그룹 멤버십 변경 처리 실패:', error);
		showUpdateMessage('그룹 멤버십 변경 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

// ===== 헬퍼 함수들 =====

/**
 * 그룹 멤버십 변경 여부 확인
 */
function hasGroupMembershipChanges(payload: any): boolean {
	if (Array.isArray(payload)) {
		return payload.some((emp) => emp.groups !== undefined || emp.group_ids !== undefined);
	}
	return payload && (payload.groups !== undefined || payload.group_ids !== undefined);
}

/**
 * 직원 변경사항 분석
 */
function analyzeEmployeeChanges(
	existingEmployees: any[],
	newEmployees: any[]
): {
	groupChanges: number;
	statusChanges: number;
	profileChanges: number;
} {
	let groupChanges = 0;
	let statusChanges = 0;
	let profileChanges = 0;

	newEmployees.forEach((newEmp, index) => {
		const existing = existingEmployees[index];
		if (!existing) return;

		// 그룹 변경 확인
		const oldGroups = JSON.stringify(existing.groups || existing.group_ids || []);
		const newGroups = JSON.stringify(newEmp.groups || newEmp.group_ids || []);
		if (oldGroups !== newGroups) {
			groupChanges++;
		}

		// 상태 변경 확인
		if (existing.status !== newEmp.status || existing.is_active !== newEmp.is_active) {
			statusChanges++;
		}

		// 프로필 변경 확인
		if (
			existing.name !== newEmp.name ||
			existing.email !== newEmp.email ||
			existing.position !== newEmp.position
		) {
			profileChanges++;
		}
	});

	return { groupChanges, statusChanges, profileChanges };
}

/**
 * 배치 직원 변경사항 분석
 */
function analyzeBatchEmployeeChanges(
	existingEmployees: any[],
	newEmployees: any[]
): {
	newEmployees: number;
	groupChanges: number;
	statusChanges: number;
	profileChanges: number;
	totalProcessed: number;
} {
	const newEmployeesCount = existingEmployees.filter((emp) => emp === null).length;
	const changes = analyzeEmployeeChanges(existingEmployees, newEmployees);

	return {
		newEmployees: newEmployeesCount,
		...changes,
		totalProcessed: newEmployees.length
	};
}

/**
 * 멤버십 변경사항 분석
 */
function analyzeMembershipChanges(employees: any[]): {
	addedToGroups: number;
	removedFromGroups: number;
	totalChanges: number;
} {
	let addedToGroups = 0;
	let removedFromGroups = 0;

	employees.forEach((emp) => {
		if (emp.membership_changes) {
			addedToGroups += emp.membership_changes.added?.length || 0;
			removedFromGroups += emp.membership_changes.removed?.length || 0;
		}
	});

	return {
		addedToGroups,
		removedFromGroups,
		totalChanges: addedToGroups + removedFromGroups
	};
}

/**
 * 삭제된 직원을 그룹에서 정리
 */
async function cleanupDeletedEmployeeFromGroups(
	db: IDBDatabase,
	deletedEmployeeIds: number[]
): Promise<void> {
	try {
		const allGroups = await getAllData(db, 'groups');
		const updatedGroups: any[] = [];

		allGroups.forEach((group) => {
			let hasChanges = false;

			// 멤버 ID 배열에서 삭제된 직원 제거
			if (group.member_ids && Array.isArray(group.member_ids)) {
				const originalLength = group.member_ids.length;
				group.member_ids = group.member_ids.filter(
					(id: number) => !deletedEmployeeIds.includes(id)
				);
				if (group.member_ids.length !== originalLength) {
					hasChanges = true;
				}
			}

			// 멤버 객체 배열에서 삭제된 직원 제거
			if (group.members && Array.isArray(group.members)) {
				const originalLength = group.members.length;
				group.members = group.members.filter(
					(member: any) => !deletedEmployeeIds.includes(member.id)
				);
				if (group.members.length !== originalLength) {
					hasChanges = true;
				}
			}

			if (hasChanges) {
				group.updated_at = new Date().toISOString();
				updatedGroups.push(group);
			}
		});

		if (updatedGroups.length > 0) {
			await putManyData(db, 'groups', updatedGroups);
			console.log(`${updatedGroups.length}개 그룹에서 삭제된 직원 정리 완료`);
		}
	} catch (error) {
		console.error('삭제된 직원 그룹 정리 실패:', error);
	}
}

/**
 * 그룹 멤버십 데이터 동기화
 */
async function syncGroupMembershipData(db: IDBDatabase, employeeData: any): Promise<void> {
	try {
		const employees = Array.isArray(employeeData) ? employeeData : [employeeData];
		const groupsToUpdate = new Map<number, any>();

		// 영향받은 그룹들 수집
		for (const employee of employees) {
			const groupIds = employee.group_ids || employee.groups?.map((g: any) => g.id) || [];

			for (const groupId of groupIds) {
				if (!groupsToUpdate.has(groupId)) {
					const group = await getData(db, 'groups', groupId).catch(() => null);
					if (group) {
						groupsToUpdate.set(groupId, group);
					}
				}
			}
		}

		// 각 그룹의 멤버 목록 업데이트
		const updatedGroups: any[] = [];
		for (const [groupId, group] of groupsToUpdate) {
			const allEmployees = await getAllData(db, 'employees');
			const groupMembers = allEmployees.filter((emp) => {
				const empGroupIds = emp.group_ids || emp.groups?.map((g: any) => g.id) || [];
				return empGroupIds.includes(groupId);
			});

			group.members = groupMembers.map((emp) => ({
				id: emp.id,
				name: emp.name,
				email: emp.email
			}));
			group.member_ids = groupMembers.map((emp) => emp.id);
			group.updated_at = new Date().toISOString();

			updatedGroups.push(group);
		}

		if (updatedGroups.length > 0) {
			await putManyData(db, 'groups', updatedGroups);
			console.log(`${updatedGroups.length}개 그룹의 멤버십 데이터 동기화 완료`);
		}
	} catch (error) {
		console.error('그룹 멤버십 데이터 동기화 실패:', error);
	}
}
