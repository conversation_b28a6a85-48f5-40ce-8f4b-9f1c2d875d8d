/**
 * 수리 등급 데이터 업데이트 핸들러
 *
 * 수리 등급 관련 데이터 업데이트 처리를 담당합니다.
 */

import {
	putData,
	putManyData,
	deleteData,
	clearStore,
	getAllData,
	getData
} from '$lib/services/indexedDBManager';
import { updateRepairGradeStore } from '$lib/services/storeUpdaters';

/**
 * 수리 등급 업데이트 처리
 */
export async function handleRepairGradeUpdate(
	db: IDBDatabase,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('수리 등급 업데이트 처리 시작:', { action, affectedIds });

		switch (action) {
			case 'created':
				await handleRepairGradeCreated(db, payload, affectedIds);
				break;
			case 'updated':
				await handleRepairGradeUpdated(db, payload, affectedIds);
				break;
			case 'deleted':
				await handleRepairGradeDeleted(db, affectedIds);
				break;
			case 'batch_updated':
				await handleRepairGradeBatchUpdated(db, payload, affectedIds);
				break;
			case 'synchronized':
				await handleRepairGradeSynchronized(db, payload, affectedIds);
				break;
			default:
				console.warn('알 수 없는 수리 등급 액션:', action);
		}

		// 스토어 업데이트
		await updateRepairGradeStore(db);

		console.log('수리 등급 업데이트 처리 완료:', { action, count: affectedIds.length });
	} catch (error) {
		console.error('수리 등급 업데이트 처리 실패:', error);
		throw error;
	}
}

/**
 * 수리 등급 생성 처리
 */
async function handleRepairGradeCreated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (Array.isArray(payload)) {
		await putManyData(db, 'repair_grades', payload);

		const summaries = payload
			.slice(0, 3)
			.map((item) => `${item.grade_name} (레벨: ${item.level || 'N/A'})`)
			.join(', ');

		const message =
			payload.length <= 3
				? `새로운 수리 등급: ${summaries}`
				: `새로운 수리 등급 ${payload.length}개가 추가되었습니다. (${summaries} 외 ${payload.length - 3}개)`;

		console.log('수리 등급 생성 완료:', message);
	} else {
		await putData(db, 'repair_grades', payload);

		const summary = `${payload.grade_name} (레벨: ${payload.level || 'N/A'})`;
		console.log(`새로운 수리 등급이 추가되었습니다: ${summary}`);
	}
}

/**
 * 수리 등급 업데이트 처리
 */
async function handleRepairGradeUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (Array.isArray(payload)) {
		await putManyData(db, 'repair_grades', payload);
		console.log(`수리 등급 ${payload.length}개가 업데이트되었습니다.`);
	} else {
		await putData(db, 'repair_grades', payload);

		const summary = `${payload.grade_name} (레벨: ${payload.level || 'N/A'})`;
		console.log(`수리 등급이 업데이트되었습니다: ${summary}`);
	}
}

/**
 * 수리 등급 삭제 처리
 */
async function handleRepairGradeDeleted(db: IDBDatabase, affectedIds: number[]): Promise<void> {
	// 삭제 전 데이터 정보 수집
	const deletedItems = [];
	for (const id of affectedIds) {
		try {
			const item = await getData(db, 'repair_grades', id);
			if (item) {
				deletedItems.push(item);
			}
		} catch (error) {
			console.warn(`삭제할 수리 등급 항목 조회 실패 (ID: ${id}):`, error);
		}
	}

	// 실제 삭제 수행
	for (const id of affectedIds) {
		await deleteData(db, 'repair_grades', id);
	}

	// 삭제 메시지 표시
	if (deletedItems.length === 1) {
		const summary = `${deletedItems[0].grade_name} (레벨: ${deletedItems[0].level || 'N/A'})`;
		console.log(`수리 등급이 삭제되었습니다: ${summary}`);
	} else {
		console.log(`수리 등급 ${affectedIds.length}개가 삭제되었습니다.`);
	}
}

/**
 * 수리 등급 배치 업데이트 처리
 */
async function handleRepairGradeBatchUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!Array.isArray(payload)) {
		console.warn('배치 업데이트 페이로드가 배열이 아닙니다:', payload);
		return;
	}

	// 배치 업데이트 전 현재 상태 분석
	const beforeCount = (await getAllData(db, 'repair_grades')).length;

	// 배치 업데이트 실행
	await putManyData(db, 'repair_grades', payload);

	const afterCount = (await getAllData(db, 'repair_grades')).length;
	const netChange = afterCount - beforeCount;

	// 상세한 배치 업데이트 메시지
	let message = `수리 등급 ${payload.length}개가 일괄 업데이트되었습니다.`;

	if (netChange > 0) {
		message += ` (신규 추가: ${netChange}개)`;
	} else if (netChange < 0) {
		message += ` (제거됨: ${Math.abs(netChange)}개)`;
	}

	console.log(message);
}

/**
 * 수리 등급 동기화 처리
 */
async function handleRepairGradeSynchronized(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!Array.isArray(payload)) {
		console.warn('동기화 페이로드가 배열이 아닙니다:', payload);
		return;
	}

	// 전체 동기화: 기존 데이터 삭제 후 새 데이터 저장
	await clearStore(db, 'repair_grades');
	await putManyData(db, 'repair_grades', payload);

	console.log(`수리 등급 전체 동기화가 완료되었습니다. (${payload.length}개 항목)`);
}
