/**
 * 그룹 데이터 업데이트 핸들러
 */

import {
	putData,
	putManyData,
	deleteData,
	getAllData,
	getData
} from '$lib/services/indexedDBManager';
import { showSimpleBrowserNotification } from '$lib/services/notificationHandler';
import { updateGroupStore, updateEmployeeStore } from '$lib/services/storeUpdaters';
import {
	showUpdateMessage,
	highlightNewItems,
	highlightUpdatedItems,
	highlightDeletedItems,
	highlightBatchUpdatedItems
} from '$lib/services/dataHandlers/uiFeedbackHandler';

/**
 * 그룹 정보 업데이트 처리
 */
export async function handleGroupUpdate(
	db: IDBDatabase,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('그룹 정보 업데이트 처리 시작:', {
			action,
			affectedIds,
			payloadType: Array.isArray(payload) ? 'array' : typeof payload
		});

		switch (action) {
			case 'created':
				await handleGroupCreated(db, payload, affectedIds);
				break;
			case 'updated':
				await handleGroupUpdated(db, payload, affectedIds);
				break;
			case 'deleted':
				await handleGroupDeleted(db, affectedIds);
				break;
			case 'batch_updated':
				await handleGroupBatchUpdated(db, payload, affectedIds);
				break;
			case 'membership_changed':
				await handleGroupMembershipChanged(db, payload, affectedIds);
				break;
			default:
				console.warn('알 수 없는 그룹 액션:', action);
				return;
		}

		// 스토어 업데이트
		await updateGroupStore(db);

		// 멤버십 변경이 있는 경우 직원 스토어도 업데이트
		if (action === 'membership_changed' || hasEmployeeMembershipChanges(payload)) {
			await updateEmployeeStore(db);
		}

		console.log('그룹 정보 업데이트 처리 완료:', { action, affectedCount: affectedIds.length });
	} catch (error) {
		console.error('그룹 정보 업데이트 처리 실패:', error);
		showUpdateMessage('그룹 정보 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 그룹 생성 처리
 */
async function handleGroupCreated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (Array.isArray(payload)) {
			// 다중 그룹 생성
			await putManyData(db, 'groups', payload);

			const groupNames = payload
				.map((group) => group.name)
				.slice(0, 3)
				.join(', ');

			let message = `새로운 그룹 ${payload.length}개가 생성되었습니다.`;
			if (groupNames) {
				message += ` (${groupNames}${payload.length > 3 ? ' 외' : ''})`;
			}

			showUpdateMessage(message, 'success');
			highlightNewItems(affectedIds, 4000);
		} else {
			// 단일 그룹 생성
			await putData(db, 'groups', payload);

			const groupName = payload.name || '새 그룹';
			showUpdateMessage(`그룹 '${groupName}'이 생성되었습니다.`, 'success');
			highlightNewItems(affectedIds, 3000);
		}

		console.log('그룹 생성 처리 완료:', {
			count: Array.isArray(payload) ? payload.length : 1,
			affectedIds
		});
	} catch (error) {
		console.error('그룹 생성 처리 실패:', error);
		showUpdateMessage('그룹 생성 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 그룹 정보 업데이트 처리
 */
async function handleGroupUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (Array.isArray(payload)) {
			// 다중 그룹 업데이트
			const existingGroups = await Promise.all(
				payload.map((group) => getData(db, 'groups', group.id).catch(() => null))
			);

			const changes = analyzeGroupChanges(existingGroups, payload);

			await putManyData(db, 'groups', payload);

			let message = `그룹 ${payload.length}개의 정보가 업데이트되었습니다.`;
			if (changes.membershipChanges > 0) {
				message += ` (멤버십 변경: ${changes.membershipChanges}개 그룹)`;
			}
			if (changes.permissionChanges > 0) {
				message += ` (권한 변경: ${changes.permissionChanges}개 그룹)`;
			}

			showUpdateMessage(message, 'info');
			highlightUpdatedItems(affectedIds, 3000);
		} else {
			// 단일 그룹 업데이트
			const existingGroup = await getData(db, 'groups', payload.id).catch(() => null);
			const changes = analyzeGroupChanges([existingGroup], [payload]);

			await putData(db, 'groups', payload);

			const groupName = payload.name || '그룹';
			let message = `그룹 '${groupName}'의 정보가 업데이트되었습니다.`;

			if (changes.membershipChanges > 0) {
				message += ' (멤버 변경됨)';
			}

			showUpdateMessage(message, 'info');
			highlightUpdatedItems(affectedIds, 2000);
		}

		console.log('그룹 업데이트 처리 완료:', {
			count: Array.isArray(payload) ? payload.length : 1,
			affectedIds
		});
	} catch (error) {
		console.error('그룹 업데이트 처리 실패:', error);
		showUpdateMessage('그룹 정보 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 그룹 삭제 처리
 */
async function handleGroupDeleted(db: IDBDatabase, affectedIds: number[]): Promise<void> {
	try {
		// 삭제될 그룹 정보 수집 (삭제 전)
		const deletedGroups = await Promise.all(
			affectedIds.map((id) => getData(db, 'groups', id).catch(() => null))
		);

		const validDeletedGroups = deletedGroups.filter((group) => group !== null);

		// 그룹에 속한 직원들의 멤버십 정리
		await cleanupDeletedGroupFromEmployees(db, validDeletedGroups);

		// 그룹 삭제 실행
		for (const id of affectedIds) {
			await deleteData(db, 'groups', id);
		}

		// 삭제 메시지 생성
		const deletedCount = affectedIds.length;
		const deletedNames = validDeletedGroups
			.map((group) => group.name)
			.slice(0, 3)
			.join(', ');

		let message = `그룹 ${deletedCount}개가 삭제되었습니다.`;
		if (deletedNames) {
			message += ` (${deletedNames}${deletedCount > 3 ? ' 외' : ''})`;
		}

		showUpdateMessage(message, 'warning');
		highlightDeletedItems(affectedIds, 2500);

		// 다수 삭제인 경우 브라우저 알림
		if (deletedCount > 1) {
			showSimpleBrowserNotification(
				'그룹 삭제',
				message,
				'/icons/group-update.png',
				'group-update'
			);
		}

		console.log('그룹 삭제 처리 완료:', { deletedCount, affectedIds });
	} catch (error) {
		console.error('그룹 삭제 처리 실패:', error);
		showUpdateMessage('그룹 삭제 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 그룹 배치 업데이트 처리
 */
async function handleGroupBatchUpdated(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		if (!Array.isArray(payload)) {
			console.warn('그룹 배치 업데이트 페이로드가 배열이 아닙니다:', payload);
			return;
		}

		// 기존 그룹 데이터 조회
		const existingGroups = await Promise.all(
			payload.map((group) => getData(db, 'groups', group.id).catch(() => null))
		);

		// 배치 변경사항 분석
		const batchChanges = analyzeBatchGroupChanges(existingGroups, payload);

		// 배치 업데이트 실행
		await putManyData(db, 'groups', payload);

		// 상세한 배치 업데이트 메시지
		const totalCount = payload.length;
		let message = `${totalCount}개 그룹이 일괄 업데이트되었습니다.`;

		if (batchChanges.newGroups > 0) {
			message += ` (신규: ${batchChanges.newGroups}개)`;
		}
		if (batchChanges.membershipChanges > 0) {
			message += ` (멤버십 변경: ${batchChanges.membershipChanges}개)`;
		}
		if (batchChanges.permissionChanges > 0) {
			message += ` (권한 변경: ${batchChanges.permissionChanges}개)`;
		}

		showUpdateMessage(message, 'info');

		// 배치 업데이트 애니메이션 (파도 효과)
		highlightBatchUpdatedItems(affectedIds, 3000);

		// 대규모 배치 업데이트인 경우 브라우저 알림
		if (totalCount > 5 || batchChanges.membershipChanges > 3) {
			showSimpleBrowserNotification(
				'대규모 그룹 업데이트',
				message,
				'/icons/group-update.png',
				'group-update'
			);
		}

		console.log('그룹 배치 업데이트 처리 완료:', {
			totalCount,
			changes: batchChanges,
			affectedIds: affectedIds.slice(0, 10) // 처음 10개만 로깅
		});
	} catch (error) {
		console.error('그룹 배치 업데이트 처리 실패:', error);
		showUpdateMessage('그룹 일괄 업데이트 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

/**
 * 그룹 멤버십 변경 처리
 */
async function handleGroupMembershipChanged(
	db: IDBDatabase,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	try {
		console.log('그룹 멤버십 변경 처리:', { payload, affectedIds });

		if (Array.isArray(payload)) {
			// 다중 그룹의 멤버십 변경
			await putManyData(db, 'groups', payload);

			const membershipChanges = analyzeGroupMembershipChanges(payload);

			let message = `${payload.length}개 그룹의 멤버십이 변경되었습니다.`;
			if (membershipChanges.membersAdded > 0) {
				message += ` (멤버 추가: ${membershipChanges.membersAdded}명)`;
			}
			if (membershipChanges.membersRemoved > 0) {
				message += ` (멤버 제거: ${membershipChanges.membersRemoved}명)`;
			}

			showUpdateMessage(message, 'info');
		} else {
			// 단일 그룹의 멤버십 변경
			await putData(db, 'groups', payload);

			const groupName = payload.name || '그룹';
			const memberCount = payload.members?.length || 0;

			showUpdateMessage(
				`그룹 '${groupName}'의 멤버십이 변경되었습니다. (현재 멤버: ${memberCount}명)`,
				'info'
			);
		}

		// 멤버십 변경 하이라이트 (특별한 색상)
		highlightNewItems(affectedIds, 3500, 'glow');

		// 직원 데이터도 동기화 (직원의 그룹 목록 업데이트)
		await syncEmployeeGroupData(db, payload);

		console.log('그룹 멤버십 변경 처리 완료:', affectedIds);
	} catch (error) {
		console.error('그룹 멤버십 변경 처리 실패:', error);
		showUpdateMessage('그룹 멤버십 변경 중 오류가 발생했습니다.', 'error');
		throw error;
	}
}

// ===== 헬퍼 함수들 =====

/**
 * 직원 멤버십 변경 여부 확인
 */
function hasEmployeeMembershipChanges(payload: any): boolean {
	if (Array.isArray(payload)) {
		return payload.some((group) => group.members !== undefined || group.member_ids !== undefined);
	}
	return payload && (payload.members !== undefined || payload.member_ids !== undefined);
}

/**
 * 그룹 변경사항 분석
 */
function analyzeGroupChanges(
	existingGroups: any[],
	newGroups: any[]
): {
	membershipChanges: number;
	permissionChanges: number;
	nameChanges: number;
} {
	let membershipChanges = 0;
	let permissionChanges = 0;
	let nameChanges = 0;

	newGroups.forEach((newGroup, index) => {
		const existing = existingGroups[index];
		if (!existing) return;

		// 멤버십 변경 확인
		const oldMembers = JSON.stringify(existing.members || existing.member_ids || []);
		const newMembers = JSON.stringify(newGroup.members || newGroup.member_ids || []);
		if (oldMembers !== newMembers) {
			membershipChanges++;
		}

		// 권한 변경 확인
		const oldPermissions = JSON.stringify(existing.permissions || []);
		const newPermissions = JSON.stringify(newGroup.permissions || []);
		if (oldPermissions !== newPermissions) {
			permissionChanges++;
		}

		// 이름 변경 확인
		if (existing.name !== newGroup.name) {
			nameChanges++;
		}
	});

	return { membershipChanges, permissionChanges, nameChanges };
}

/**
 * 배치 그룹 변경사항 분석
 */
function analyzeBatchGroupChanges(
	existingGroups: any[],
	newGroups: any[]
): {
	newGroups: number;
	membershipChanges: number;
	permissionChanges: number;
	nameChanges: number;
	totalProcessed: number;
} {
	const newGroupsCount = existingGroups.filter((group) => group === null).length;
	const changes = analyzeGroupChanges(existingGroups, newGroups);

	return {
		newGroups: newGroupsCount,
		...changes,
			totalProcessed: newGroups.length
	};
}

/**
 * 그룹 멤버십 변경사항 분석
 */
function analyzeGroupMembershipChanges(groups: any[]): {
	membersAdded: number;
	membersRemoved: number;
	totalChanges: number;
} {
	let membersAdded = 0;
	let membersRemoved = 0;

	groups.forEach((group) => {
		if (group.membership_changes) {
			membersAdded += group.membership_changes.added?.length || 0;
			membersRemoved += group.membership_changes.removed?.length || 0;
		}
	});

	return {
		membersAdded,
		membersRemoved,
		totalChanges: membersAdded + membersRemoved
	};
}

/**
 * 삭제된 그룹을 직원에서 정리
 */
async function cleanupDeletedGroupFromEmployees(
	db: IDBDatabase,
	deletedGroups: any[]
): Promise<void> {
	try {
		const deletedGroupIds = deletedGroups.map((group) => group.id);
		const allEmployees = await getAllData(db, 'employees');
		const updatedEmployees: any[] = [];

		allEmployees.forEach((employee) => {
			let hasChanges = false;

			// 그룹 ID 배열에서 삭제된 그룹 제거
			if (employee.group_ids && Array.isArray(employee.group_ids)) {
				const originalLength = employee.group_ids.length;
				employee.group_ids = employee.group_ids.filter(
					(id: number) => !deletedGroupIds.includes(id)
				);
				if (employee.group_ids.length !== originalLength) {
					hasChanges = true;
				}
			}

			// 그룹 객체 배열에서 삭제된 그룹 제거
			if (employee.groups && Array.isArray(employee.groups)) {
				const originalLength = employee.groups.length;
				employee.groups = employee.groups.filter(
					(group: any) => !deletedGroupIds.includes(group.id)
				);
				if (employee.groups.length !== originalLength) {
					hasChanges = true;
				}
			}

			if (hasChanges) {
				employee.updated_at = new Date().toISOString();
				updatedEmployees.push(employee);
			}
		});

		if (updatedEmployees.length > 0) {
			await putManyData(db, 'employees', updatedEmployees);
			console.log(`${updatedEmployees.length}명 직원에서 삭제된 그룹 정리 완료`);
		}
	} catch (error) {
		console.error('삭제된 그룹 직원 정리 실패:', error);
	}
}

/**
 * 직원 그룹 데이터 동기화
 */
async function syncEmployeeGroupData(db: IDBDatabase, groupData: any): Promise<void> {
	try {
		const groups = Array.isArray(groupData) ? groupData : [groupData];
		const employeesToUpdate = new Map<number, any>();

		// 영향받은 직원들 수집
		for (const group of groups) {
			const memberIds = group.member_ids || group.members?.map((m: any) => m.id) || [];

			for (const memberId of memberIds) {
				if (!employeesToUpdate.has(memberId)) {
					const employee = await getData(db, 'employees', memberId).catch(() => null);
					if (employee) {
						employeesToUpdate.set(memberId, employee);
					}
				}
			}
		}

		// 각 직원의 그룹 목록 업데이트
		const updatedEmployees: any[] = [];
		for (const [employeeId, employee] of employeesToUpdate) {
			const allGroups = await getAllData(db, 'groups');
			const employeeGroups = allGroups.filter((group) => {
				const groupMemberIds = group.member_ids || group.members?.map((m: any) => m.id) || [];
				return groupMemberIds.includes(employeeId);
			});

			employee.groups = employeeGroups.map((group) => ({
				id: group.id,
				name: group.name,
				description: group.description
			}));
			employee.group_ids = employeeGroups.map((group) => group.id);
			employee.updated_at = new Date().toISOString();

			updatedEmployees.push(employee);
		}

		if (updatedEmployees.length > 0) {
			await putManyData(db, 'employees', updatedEmployees);
			console.log(`${updatedEmployees.length}명 직원의 그룹 데이터 동기화 완료`);
		}
	} catch (error) {
		console.error('직원 그룹 데이터 동기화 실패:', error);
	}
}
