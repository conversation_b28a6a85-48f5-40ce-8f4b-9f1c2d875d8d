/**
 * UI 피드백 핸들러
 *
 * 데이터 업데이트에 따른 UI 피드백 및 애니메이션 처리를 담당합니다.
 */

import {
	updateMessageStore,
	highlightedItemsStore,
	updatedItemsStore,
	deletedItemsStore,
	batchUpdatedItemsStore
} from '$lib/stores';

import type { MessageType, UpdateMessage } from '$lib/types/messageTypes';

/**
 * 업데이트 메시지 표시
 *
 * @param message 표시할 메시지
 * @param type 메시지 타입
 * @param duration 표시 지속시간 (밀리초, 0이면 수동 닫기)
 * @param action 액션 버튼 (선택사항)
 */
export function showUpdateMessage(
	message: string,
	type: MessageType,
	duration?: number,
	action?: { label: string; callback: () => void }
): void {
	try {
		const messageId = crypto.randomUUID();
		const newMessage: UpdateMessage = {
			id: messageId,
			message,
			type,
			timestamp: new Date().toISOString(),
			duration,
			action
		};

		updateMessageStore.update((messages) => [
			newMessage,
			...messages.slice(0, 9) // 최대 10개 메시지 유지
		]);

		// 자동 제거 설정 (duration이 지정된 경우)
		if (duration && duration > 0) {
			setTimeout(() => {
				removeUpdateMessage(messageId);
			}, duration);
		}

		console.log(
			`업데이트 메시지 표시: [${type}] ${message}`,
			duration ? `(${duration}ms)` : '(수동 닫기)'
		);
	} catch (error) {
		console.error('업데이트 메시지 표시 실패:', error);
	}
}

/**
 * 업데이트 메시지 제거
 *
 * @param messageId 제거할 메시지 ID
 */
export function removeUpdateMessage(messageId: string): void {
	try {
		updateMessageStore.update((messages) => messages.filter((msg) => msg.id !== messageId));
		console.log('업데이트 메시지 제거:', messageId);
	} catch (error) {
		console.error('업데이트 메시지 제거 실패:', error);
	}
}

/**
 * 모든 업데이트 메시지 제거
 */
export function clearAllUpdateMessages(): void {
	try {
		updateMessageStore.set([]);
		console.log('모든 업데이트 메시지 제거 완료');
	} catch (error) {
		console.error('모든 업데이트 메시지 제거 실패:', error);
	}
}

/**
 * 성공 메시지 표시 (편의 함수)
 */
export function showSuccessMessage(message: string, duration: number = 3000): void {
	showUpdateMessage(message, 'success', duration);
}

/**
 * 정보 메시지 표시 (편의 함수)
 */
export function showInfoMessage(message: string, duration: number = 5000): void {
	showUpdateMessage(message, 'info', duration);
}

/**
 * 경고 메시지 표시 (편의 함수)
 */
export function showWarningMessage(message: string, duration: number = 7000): void {
	showUpdateMessage(message, 'warning', duration);
}

/**
 * 오류 메시지 표시 (편의 함수)
 */
export function showErrorMessage(message: string, duration: number = 0): void {
	showUpdateMessage(message, 'error', duration); // 오류는 수동 닫기
}

/**
 * 새 항목 하이라이트
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param duration 하이라이트 지속시간 (밀리초)
 * @param animationType 애니메이션 타입
 */
export function highlightNewItems(
	ids: number[],
	duration: number = 3000,
	animationType: 'pulse' | 'glow' | 'bounce' = 'glow'
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('하이라이트할 항목 ID가 없습니다.');
			return;
		}

		// 새 항목 하이라이트 스토어에 추가
		highlightedItemsStore.update((current) => {
			const updated = new Set([...current, ...ids]);
			return updated;
		});

		// 지정된 시간 후 하이라이트 제거
		setTimeout(() => {
			highlightedItemsStore.update((current) => {
				const updated = new Set(current);
				ids.forEach((id) => updated.delete(id));
				return updated;
			});
		}, duration);

		console.log(`새 항목 하이라이트 적용 (${animationType}):`, ids, `지속시간: ${duration}ms`);
	} catch (error) {
		console.error('새 항목 하이라이트 실패:', error);
	}
}

/**
 * 새 항목 순차적 하이라이트 (계단식 효과)
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param staggerDelay 각 항목 간 지연시간 (밀리초)
 * @param duration 각 항목의 하이라이트 지속시간 (밀리초)
 */
export function highlightNewItemsStaggered(
	ids: number[],
	staggerDelay: number = 200,
	duration: number = 3000
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('하이라이트할 항목 ID가 없습니다.');
			return;
		}

		ids.forEach((id, index) => {
			setTimeout(() => {
				highlightNewItems([id], duration);
			}, index * staggerDelay);
		});

		console.log(
			'새 항목 순차적 하이라이트 적용:',
			ids.length,
			`지연: ${staggerDelay}ms, 지속: ${duration}ms`
		);
	} catch (error) {
		console.error('새 항목 순차적 하이라이트 실패:', error);
	}
}

/**
 * 업데이트된 항목 하이라이트
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param duration 하이라이트 지속시간 (밀리초)
 * @param intensity 하이라이트 강도 ('subtle' | 'normal' | 'strong')
 */
export function highlightUpdatedItems(
	ids: number[],
	duration: number = 2000,
	intensity: 'subtle' | 'normal' | 'strong' = 'normal'
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('하이라이트할 항목 ID가 없습니다.');
			return;
		}

		// 업데이트된 항목 하이라이트 스토어에 추가
		updatedItemsStore.update((current) => {
			const updated = new Set([...current, ...ids]);
			return updated;
		});

		// 강도에 따른 지속시간 조정
		const adjustedDuration =
			intensity === 'subtle' ? duration * 0.7 : intensity === 'strong' ? duration * 1.5 : duration;

		setTimeout(() => {
			updatedItemsStore.update((current) => {
				const updated = new Set(current);
				ids.forEach((id) => updated.delete(id));
				return updated;
			});
		}, adjustedDuration);

		console.log(
			`업데이트된 항목 하이라이트 적용 (${intensity}):`,
			ids,
			`지속시간: ${adjustedDuration}ms`
		);
	} catch (error) {
		console.error('업데이트된 항목 하이라이트 실패:', error);
	}
}

/**
 * 업데이트된 항목 펄스 효과
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param pulseCount 펄스 횟수
 * @param pulseDuration 각 펄스 지속시간 (밀리초)
 */
export function pulseUpdatedItems(
	ids: number[],
	pulseCount: number = 3,
	pulseDuration: number = 600
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('펄스할 항목 ID가 없습니다.');
			return;
		}

		for (let i = 0; i < pulseCount; i++) {
			setTimeout(() => {
				updatedItemsStore.update((current) => new Set([...current, ...ids]));

				setTimeout(() => {
					updatedItemsStore.update((current) => {
						const updated = new Set(current);
						ids.forEach((id) => updated.delete(id));
						return updated;
					});
				}, pulseDuration / 2);
			}, i * pulseDuration);
		}

		console.log(`업데이트된 항목 펄스 효과 적용:`, ids, `${pulseCount}회, 각 ${pulseDuration}ms`);
	} catch (error) {
		console.error('업데이트된 항목 펄스 효과 실패:', error);
	}
}

/**
 * 삭제된 항목 하이라이트 (페이드아웃 효과)
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param duration 페이드아웃 지속시간 (밀리초)
 * @param showWarning 삭제 경고 표시 여부
 */
export function highlightDeletedItems(
	ids: number[],
	duration: number = 2000,
	showWarning: boolean = true
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('하이라이트할 삭제 항목 ID가 없습니다.');
			return;
		}

		// 삭제된 항목 전용 스토어에 추가
		deletedItemsStore.update((current) => new Set([...current, ...ids]));

		// 경고 메시지 표시 (선택사항)
		if (showWarning) {
			const itemCount = ids.length;
			const message =
				itemCount === 1 ? '1개 항목이 삭제되었습니다.' : `${itemCount}개 항목이 삭제되었습니다.`;
			showWarningMessage(message, 3000);
		}

		// 페이드아웃 애니메이션을 위한 단계별 처리
		setTimeout(() => {
			deletedItemsStore.update((current) => {
				const updated = new Set(current);
				ids.forEach((id) => updated.delete(id));
				return updated;
			});
		}, duration);

		console.log('삭제된 항목 하이라이트 적용:', ids, `지속시간: ${duration}ms`);
	} catch (error) {
		console.error('삭제된 항목 하이라이트 실패:', error);
	}
}

/**
 * 삭제된 항목 순차적 페이드아웃
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param staggerDelay 각 항목 간 지연시간 (밀리초)
 * @param fadeDuration 각 항목의 페이드아웃 지속시간 (밀리초)
 */
export function fadeOutDeletedItemsStaggered(
	ids: number[],
	staggerDelay: number = 150,
	fadeDuration: number = 1500
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('페이드아웃할 항목 ID가 없습니다.');
			return;
		}

		ids.forEach((id, index) => {
			setTimeout(() => {
				highlightDeletedItems([id], fadeDuration, false);
			}, index * staggerDelay);
		});

		// 전체 삭제 완료 메시지
		const totalDuration = (ids.length - 1) * staggerDelay + fadeDuration;
		setTimeout(() => {
			showInfoMessage(`${ids.length}개 항목 삭제가 완료되었습니다.`, 2000);
		}, totalDuration);

		console.log(
			'삭제된 항목 순차적 페이드아웃 적용:',
			ids.length,
			`지연: ${staggerDelay}ms, 지속: ${fadeDuration}ms`
		);
	} catch (error) {
		console.error('삭제된 항목 순차적 페이드아웃 실패:', error);
	}
}

/**
 * 배치 업데이트 항목 하이라이트 (파도 효과)
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param duration 전체 애니메이션 지속시간 (밀리초)
 * @param waveCount 파도 그룹 수
 */
export function highlightBatchUpdatedItems(
	ids: number[],
	duration: number = 2500,
	waveCount: number = 5
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('하이라이트할 배치 항목 ID가 없습니다.');
			return;
		}

		// 배치 업데이트 전용 스토어에 추가
		batchUpdatedItemsStore.update((current) => new Set([...current, ...ids]));

		// 파도 효과를 위한 순차적 하이라이트
		const batchSize = Math.ceil(ids.length / waveCount);
		const waveDelay = duration / waveCount;

		for (let i = 0; i < waveCount; i++) {
			const startIndex = i * batchSize;
			const endIndex = Math.min(startIndex + batchSize, ids.length);
			const batchIds = ids.slice(startIndex, endIndex);

			if (batchIds.length === 0) break;

			setTimeout(() => {
				// 파도 그룹 하이라이트
				updatedItemsStore.update((current) => new Set([...current, ...batchIds]));

				// 각 파도 그룹의 하이라이트 제거
				setTimeout(() => {
					updatedItemsStore.update((current) => {
						const updated = new Set(current);
						batchIds.forEach((id) => updated.delete(id));
						return updated;
					});
				}, waveDelay * 1.5);
			}, i * waveDelay);
		}

		// 전체 배치 업데이트 완료 후 정리
		setTimeout(() => {
			batchUpdatedItemsStore.update((current) => {
				const updated = new Set(current);
				ids.forEach((id) => updated.delete(id));
				return updated;
			});
		}, duration + 500);

		console.log(
			`배치 업데이트 파도 효과 적용: ${ids.length}개 항목, ${waveCount}개 파도, 총 ${duration}ms`
		);
	} catch (error) {
		console.error('배치 업데이트 하이라이트 실패:', error);
	}
}

/**
 * 배치 업데이트 진행률 표시
 *
 * @param totalItems 전체 항목 수
 * @param processedItems 처리된 항목 수
 * @param operation 작업 유형
 */
export function showBatchUpdateProgress(
	totalItems: number,
	processedItems: number,
	operation: string = '업데이트'
): void {
	try {
		const percentage = Math.round((processedItems / totalItems) * 100);
		const message = `${operation} 진행 중: ${processedItems}/${totalItems} (${percentage}%)`;

		if (processedItems === totalItems) {
			showSuccessMessage(`${operation} 완료: ${totalItems}개 항목`, 3000);
		} else {
			showInfoMessage(message, 1000);
		}

		console.log(`배치 ${operation} 진행률:`, percentage, '%');
	} catch (error) {
		console.error('배치 업데이트 진행률 표시 실패:', error);
	}
}

/**
 * 배치 업데이트 리플 효과 (중심에서 퍼져나가는 효과)
 *
 * @param ids 하이라이트할 항목 ID 배열
 * @param centerIndex 중심 항목 인덱스
 * @param rippleSpeed 리플 속도 (밀리초)
 */
export function rippleBatchUpdatedItems(
	ids: number[],
	centerIndex: number = Math.floor(ids.length / 2),
	rippleSpeed: number = 100
): void {
	try {
		if (!ids || ids.length === 0) {
			console.warn('리플 효과를 적용할 항목 ID가 없습니다.');
			return;
		}

		// 중심에서부터 거리 계산하여 순서 결정
		const rippleOrder = ids
			.map((id, index) => ({
				id,
				distance: Math.abs(index - centerIndex)
			}))
			.sort((a, b) => a.distance - b.distance);

		// 거리별로 순차적 하이라이트
		rippleOrder.forEach(({ id, distance }) => {
			setTimeout(() => {
				highlightUpdatedItems([id], 1500, 'strong');
			}, distance * rippleSpeed);
		});

		const maxDistance = Math.max(...rippleOrder.map((item) => item.distance));
		const totalDuration = maxDistance * rippleSpeed + 1500;

		console.log(
			`배치 업데이트 리플 효과 적용: ${ids.length}개 항목, 중심: ${centerIndex}, 총 ${totalDuration}ms`
		);
	} catch (error) {
		console.error('배치 업데이트 리플 효과 실패:', error);
	}
}

/**
 * 모든 하이라이트 효과 즉시 제거
 */
export function clearAllHighlights(): void {
	try {
		highlightedItemsStore.set(new Set());
		updatedItemsStore.set(new Set());
		deletedItemsStore.set(new Set());
		batchUpdatedItemsStore.set(new Set());
		console.log('모든 하이라이트 효과 제거 완료');
	} catch (error) {
		console.error('하이라이트 효과 제거 실패:', error);
	}
}

/**
 * 사용자 경험 향상을 위한 통합 피드백 함수
 *
 * @param action 수행된 액션
 * @param itemCount 영향받은 항목 수
 * @param itemType 항목 유형
 * @param success 성공 여부
 */
export function showComprehensiveFeedback(
	action: 'created' | 'updated' | 'deleted' | 'batch_updated',
	itemCount: number,
	itemType: string = '항목',
	success: boolean = true
): void {
	try {
		const actionText = {
			created: '생성',
			updated: '업데이트',
			deleted: '삭제',
			batch_updated: '일괄 업데이트'
		}[action];

		if (success) {
			const message =
				itemCount === 1
					? `${itemType} ${actionText}이 완료되었습니다.`
					: `${itemCount}개 ${itemType} ${actionText}이 완료되었습니다.`;

			showSuccessMessage(message, 3000);
		} else {
			const message =
				itemCount === 1
					? `${itemType} ${actionText} 중 오류가 발생했습니다.`
					: `${itemCount}개 ${itemType} ${actionText} 중 오류가 발생했습니다.`;

			showErrorMessage(message);
		}

		console.log(
			`통합 피드백 표시: ${action} ${itemCount}개 ${itemType} - ${success ? '성공' : '실패'}`
		);
	} catch (error) {
		console.error('통합 피드백 표시 실패:', error);
	}
}
