/**
 * SSE 직원 알림 시스템 - 데이터 스토어 업데이트 함수들
 *
 * IndexedDB 데이터를 Svelte 스토어와 동기화하는 함수들만 포함합니다.
 * 요구사항 1.1, 1.2, 1.3, 2.1, 2.2, 2.3, 2.4, 2.5를 충족합니다.
 */

import { getAllData, getData } from '$lib/services/indexedDBManager';
import {
	allNotificationsStore,
	categoryStore,
	employeeStore,
	groupStore,
	repairGradeStore,
	settingsStore,
	printSettingsStore,
	historyStore,
	notificationSettingsStore,
	sseStatsStore
} from '$lib/stores';

// ===== 데이터 스토어 업데이트 함수들 =====

/**
 * 알림 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateNotificationStore(db: IDBDatabase): Promise<void> {
	try {
		const notifications = await getAllData(db, 'notifications');

		// 데이터 유효성 검증 및 정렬
		const validNotifications = notifications.filter((notification) => {
			return notification && notification.id && notification.title;
		});

		const sortedNotifications = validNotifications.sort(
			(a, b) =>
				new Date(b.received_at || b.created_at || 0).getTime() -
				new Date(a.received_at || a.created_at || 0).getTime()
		);

		allNotificationsStore.set(sortedNotifications);

		// 통계 업데이트
		updateSseStats('notification');

		console.log(`알림 스토어 업데이트 완료: ${sortedNotifications.length}개 알림`);

		// 읽지 않은 알림이 있으면 UI에 반영
		const unreadCount = sortedNotifications.filter((n) => !n.read).length;
		if (unreadCount > 0) {
			console.log(`읽지 않은 알림: ${unreadCount}개`);
		}
	} catch (error) {
		console.error('알림 스토어 업데이트 실패:', error);
		updateSseStats('error');
		// 오류 발생 시에도 빈 배열로 설정하여 UI 오류 방지
		allNotificationsStore.set([]);
	}
}

/**
 * 카테고리 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateCategoryStore(db: IDBDatabase): Promise<void> {
	try {
		const categoryData = await getData(db, 'categories', 'main');

		if (categoryData) {
			const updatedData = {
				cate4: categoryData.cate4 || [],
				cate5: categoryData.cate5 || [],
				lastUpdated: categoryData.updated_at || new Date().toISOString(),
				totalCount: (categoryData.cate4?.length || 0) + (categoryData.cate5?.length || 0)
			};

			categoryStore.set(updatedData);
			console.log('카테고리 스토어 업데이트 완료:', {
				cate4Count: updatedData.cate4.length,
				cate5Count: updatedData.cate5.length,
				totalCount: updatedData.totalCount
			});
		} else {
			// 데이터가 없는 경우 빈 상태로 초기화
			categoryStore.set({
				cate4: [],
				cate5: [],
				lastUpdated: null,
				totalCount: 0
			});
			console.log('카테고리 데이터가 없어 빈 상태로 초기화');
		}
	} catch (error) {
		console.error('카테고리 스토어 업데이트 실패:', error);
		// 오류 발생 시에도 빈 상태로 설정하여 UI 오류 방지
		categoryStore.set({
			cate4: [],
			cate5: [],
			lastUpdated: null,
			totalCount: 0
		});
	}
}

/**
 * 직원 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateEmployeeStore(db: IDBDatabase): Promise<void> {
	try {
		const employees = await getAllData(db, 'employees');

		// 데이터 유효성 검증
		const validEmployees = employees.filter((employee) => {
			return employee && employee.id && employee.name;
		});

		// 이름순으로 정렬
		const sortedEmployees = validEmployees.sort((a, b) => {
			return (a.name || '').localeCompare(b.name || '', 'ko-KR');
		});

		employeeStore.set(sortedEmployees);
		console.log(`직원 스토어 업데이트 완료: ${sortedEmployees.length}명`);

		// 활성 직원 수 로깅
		const activeEmployees = sortedEmployees.filter((emp) => emp.status === 'active');
		console.log(`활성 직원: ${activeEmployees.length}명`);
	} catch (error) {
		console.error('직원 스토어 업데이트 실패:', error);
		// 오류 발생 시 빈 배열로 설정
		employeeStore.set([]);
	}
}

/**
 * 그룹 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateGroupStore(db: IDBDatabase): Promise<void> {
	try {
		const groups = await getAllData(db, 'groups');

		// 데이터 유효성 검증
		const validGroups = groups.filter((group) => {
			return group && group.id && group.name;
		});

		// 그룹명순으로 정렬
		const sortedGroups = validGroups.sort((a, b) => {
			return (a.name || '').localeCompare(b.name || '', 'ko-KR');
		});

		groupStore.set(sortedGroups);
		console.log(`그룹 스토어 업데이트 완료: ${sortedGroups.length}개 그룹`);

		// 각 그룹의 멤버 수 로깅
		sortedGroups.forEach((group) => {
			const memberCount = group.members ? group.members.length : 0;
			console.log(`그룹 "${group.name}": ${memberCount}명`);
		});
	} catch (error) {
		console.error('그룹 스토어 업데이트 실패:', error);
		// 오류 발생 시 빈 배열로 설정
		groupStore.set([]);
	}
}

/**
 * 수리 등급 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateRepairGradeStore(db: IDBDatabase): Promise<void> {
	try {
		const repairGrades = await getAllData(db, 'repair_grades');

		// 데이터 유효성 검증
		const validRepairGrades = repairGrades.filter((grade) => {
			return grade && grade.id && grade.name;
		});

		// 등급 순서대로 정렬 (grade 필드가 있다면 그것으로, 없다면 이름으로)
		const sortedRepairGrades = validRepairGrades.sort((a, b) => {
			// 등급 필드가 있으면 숫자로 정렬
			if (a.grade !== undefined && b.grade !== undefined) {
				return a.grade - b.grade;
			}
			// 없으면 이름으로 정렬
			return (a.name || '').localeCompare(b.name || '', 'ko-KR');
		});

		repairGradeStore.set(sortedRepairGrades);
		console.log(`수리 등급 스토어 업데이트 완료: ${sortedRepairGrades.length}개 등급`);

		// 등급별 정보 로깅
		sortedRepairGrades.forEach((grade) => {
			console.log(`수리 등급 "${grade.name}": 레벨 ${grade.grade || 'N/A'}`);
		});
	} catch (error) {
		console.error('수리 등급 스토어 업데이트 실패:', error);
		// 오류 발생 시 빈 배열로 설정
		repairGradeStore.set([]);
	}
}

/**
 * 설정 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateSettingsStore(db: IDBDatabase): Promise<void> {
	try {
		const settings = await getAllData(db, 'settings');
		settingsStore.set(settings);

		// 알림 설정 별도 업데이트
		const notificationSettings = settings.find((s) => s.key === 'notification_settings');
		if (notificationSettings) {
			notificationSettingsStore.set(notificationSettings.value);
		}

		console.log(`설정 스토어 업데이트 완료: ${settings.length}개 설정`);
	} catch (error) {
		console.error('설정 스토어 업데이트 실패:', error);
	}
}

/**
 * 인쇄 설정 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updatePrintSettingsStore(db: IDBDatabase): Promise<void> {
	try {
		const printSettings = await getAllData(db, 'print_settings');
		printSettingsStore.set(printSettings);
		console.log(`인쇄 설정 스토어 업데이트 완료: ${printSettings.length}개 설정`);
	} catch (error) {
		console.error('인쇄 설정 스토어 업데이트 실패:', error);
	}
}

/**
 * 업데이트 히스토리 스토어 업데이트
 *
 * @param db IDBDatabase 객체
 * @returns Promise<void>
 */
export async function updateHistoryStore(db: IDBDatabase): Promise<void> {
	try {
		const history = await getAllData(db, 'update_history');
		const sortedHistory = history.sort(
			(a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
		);
		historyStore.set(sortedHistory);
		console.log(`업데이트 히스토리 스토어 업데이트 완료: ${sortedHistory.length}개 항목`);
	} catch (error) {
		console.error('업데이트 히스토리 스토어 업데이트 실패:', error);
	}
}

// ===== 통계 업데이트 함수 =====

/**
 * SSE 통계 업데이트
 *
 * @param messageType 메시지 타입
 * @param latency 지연시간 (선택사항)
 */
function updateSseStats(
	messageType: 'notification' | 'data_update' | 'error',
	latency?: number
): void {
	sseStatsStore.update((current) => {
		const updated = {
			...current,
			totalMessagesReceived: current.totalMessagesReceived + 1,
			lastMessageAt: new Date().toISOString()
		};

		switch (messageType) {
			case 'notification':
				updated.notificationsReceived = current.notificationsReceived + 1;
				break;
			case 'data_update':
				updated.dataUpdatesReceived = current.dataUpdatesReceived + 1;
				break;
			case 'error':
				updated.errorsCount = current.errorsCount + 1;
				break;
		}

		if (latency !== undefined) {
			// 평균 지연시간 계산 (간단한 이동평균)
			updated.averageLatency = current.averageLatency * 0.9 + latency * 0.1;
		}

		return updated;
	});
}

// ===== 전체 스토어 관리 함수들 =====

/**
 * 모든 스토어 초기화
 *
 * @param db IDBDatabase 객체
 * @param showProgress 진행상황 표시 여부 (기본값: true)
 * @returns Promise<void>
 */
export async function initializeAllStores(
	db: IDBDatabase,
	showProgress: boolean = true
): Promise<void> {
	const startTime = Date.now();

	try {
		console.log('모든 스토어 초기화 시작...');

		// 스토어 초기화 작업 정의
		const initializationTasks = [
			{ name: '알림', fn: () => updateNotificationStore(db) },
			{ name: '카테고리', fn: () => updateCategoryStore(db) },
			{ name: '직원', fn: () => updateEmployeeStore(db) },
			{ name: '그룹', fn: () => updateGroupStore(db) },
			{ name: '수리 등급', fn: () => updateRepairGradeStore(db) },
			{ name: '설정', fn: () => updateSettingsStore(db) },
			{ name: '인쇄 설정', fn: () => updatePrintSettingsStore(db) },
			{ name: '업데이트 히스토리', fn: () => updateHistoryStore(db) }
		];

		// 각 작업의 성공/실패 추적
		const results = await Promise.allSettled(initializationTasks.map((task) => task.fn()));

		// 결과 분석
		const successful: string[] = [];
		const failed: string[] = [];

		results.forEach((result, index) => {
			const taskName = initializationTasks[index].name;
			if (result.status === 'fulfilled') {
				successful.push(taskName);
			} else {
				failed.push(taskName);
				console.error(`${taskName} 스토어 초기화 실패:`, result.reason);
			}
		});

		const endTime = Date.now();
		const duration = endTime - startTime;

		console.log(`스토어 초기화 완료 - 소요시간: ${duration}ms`);
		console.log(`성공: ${successful.length}개, 실패: ${failed.length}개`);

		if (successful.length > 0) {
			console.log('성공한 스토어:', successful.join(', '));
		}

		if (failed.length > 0) {
			console.warn('실패한 스토어:', failed.join(', '));
		}

		// 일부라도 실패한 경우 오류 발생
		if (failed.length > 0) {
			const error = new Error(`스토어 초기화 부분 실패: ${failed.join(', ')}`);
			error.name = 'PartialInitializationError';
			throw error;
		}
	} catch (error) {
		const endTime = Date.now();
		const duration = endTime - startTime;

		console.error(`스토어 초기화 실패 (${duration}ms):`, error);
		updateSseStats('error');
		throw error;
	}
}

/**
 * 특정 스토어만 초기화
 *
 * @param db IDBDatabase 객체
 * @param storeNames 초기화할 스토어 이름 배열
 * @returns Promise<void>
 */
export async function initializeSpecificStores(
	db: IDBDatabase,
	storeNames: string[]
): Promise<void> {
	try {
		const updateFunctions: Record<string, (db: IDBDatabase) => Promise<void>> = {
			notifications: updateNotificationStore,
			categories: updateCategoryStore,
			employees: updateEmployeeStore,
			groups: updateGroupStore,
			repair_grades: updateRepairGradeStore,
			settings: updateSettingsStore,
			print_settings: updatePrintSettingsStore,
			update_history: updateHistoryStore
		};

		const promises = storeNames
			.filter((name) => updateFunctions[name])
			.map((name) => updateFunctions[name](db));

		await Promise.all(promises);
		console.log(`특정 스토어 초기화 완료: ${storeNames.join(', ')}`);
	} catch (error) {
		console.error('특정 스토어 초기화 실패:', error);
		throw error;
	}
}

/**
 * 스토어 간 의존성 관리
 * 특정 스토어가 업데이트될 때 관련된 다른 스토어도 함께 업데이트
 *
 * @param db IDBDatabase 객체
 * @param updatedStore 업데이트된 스토어 이름
 * @returns Promise<void>
 */
export async function handleStoreDependencies(
	db: IDBDatabase,
	updatedStore: string
): Promise<void> {
	try {
		// 의존성 맵 정의
		const dependencies: Record<string, string[]> = {
			employees: ['groups'], // 직원이 업데이트되면 그룹도 확인
			groups: ['employees'], // 그룹이 업데이트되면 직원도 확인
			settings: ['notifications'] // 설정이 업데이트되면 알림 설정도 확인
		};

		const dependentStores = dependencies[updatedStore];
		if (dependentStores) {
			await initializeSpecificStores(db, dependentStores);
			console.log(`의존성 스토어 업데이트 완료: ${dependentStores.join(', ')}`);
		}
	} catch (error) {
		console.error('스토어 의존성 처리 실패:', error);
	}
}
