/**
 * 알림 시스템 통합 유틸리티
 *
 * 기존 시스템과 새로운 권한 관리 시스템을 통합합니다.
 */

import {
	initializeNotificationSystem,
	type NotificationInitOptions
} from '$lib/services/notificationInitializer';
import { processNotificationWithPermission } from '$lib/services/notifications/processor';
import {
	notificationPermissionStore,
	alternativeNotificationModeStore
} from '$lib/services/notifications/permissions';

/**
 * 애플리케이션 시작 시 알림 시스템 초기화
 *
 * @param options 초기화 옵션
 */
export async function initializeAppNotifications(options: Partial<NotificationInitOptions> = {}) {
	const defaultOptions: NotificationInitOptions = {
		autoRequestPermission: false, // 사용자가 직접 요청하도록
		enableMonitoring: true,
		requestDelay: 3000,
		onInitialized: (status) => {
			console.log(`알림 시스템 초기화 완료: ${status}`);
		},
		onPermissionChanged: (status) => {
			console.log(`알림 권한 변경: ${status}`);
		}
	};

	const finalOptions = { ...defaultOptions, ...options };

	try {
		const status = await initializeNotificationSystem(finalOptions);
		return status;
	} catch (error) {
		console.error('알림 시스템 초기화 실패:', error);
		return 'denied' as NotificationPermission;
	}
}

/**
 * SSE 메시지를 알림으로 처리 (권한 관리 포함)
 *
 * @param sseMessage SSE로 받은 알림 메시지
 * @param userSettings 사용자 알림 설정
 */
export function handleSseNotification(sseMessage: any, userSettings: any = {}) {
	// SSE 메시지를 알림 형식으로 변환
	const notification = {
		id: sseMessage.id || Date.now(),
		title: sseMessage.title || '새 알림',
		message: sseMessage.message || sseMessage.body || '',
		priority: sseMessage.priority || 'normal',
		action_url: sseMessage.action_url || null,
		received_at: new Date().toISOString()
	};

	// 권한 관리가 포함된 알림 처리
	processNotificationWithPermission(notification, userSettings);
}

/**
 * 수동 알림 전송 (테스트용)
 *
 * @param title 알림 제목
 * @param message 알림 내용
 * @param priority 우선순위
 * @param actionUrl 액션 URL
 */
export function sendManualNotification(
	title: string,
	message: string,
	priority: string = 'normal',
	actionUrl?: string
) {
	const notification = {
		id: Date.now(),
		title,
		message,
		priority,
		action_url: actionUrl || null,
		received_at: new Date().toISOString()
	};

	processNotificationWithPermission(notification);
}

/**
 * 알림 권한 상태 구독
 *
 * @param callback 권한 상태 변경 시 호출될 콜백
 * @returns 구독 해제 함수
 */
export function subscribeToPermissionStatus(
	callback: (permission: NotificationPermission) => void
) {
	return notificationPermissionStore.subscribe(callback);
}

/**
 * 대체 알림 모드 상태 구독
 *
 * @param callback 대체 모드 상태 변경 시 호출될 콜백
 * @returns 구독 해제 함수
 */
export function subscribeToAlternativeMode(callback: (isAlternative: boolean) => void) {
	return alternativeNotificationModeStore.subscribe(callback);
}

/**
 * 알림 시스템 상태 정보 조회
 *
 * @returns 현재 알림 시스템 상태
 */
export function getNotificationSystemInfo() {
	let currentPermission: NotificationPermission = 'default';
	let isAlternativeMode = false;

	// 현재 상태 조회
	const unsubscribePermission = notificationPermissionStore.subscribe(
		(permission) => (currentPermission = permission)
	);
	const unsubscribeAlternative = alternativeNotificationModeStore.subscribe(
		(alternative) => (isAlternativeMode = alternative)
	);

	// 즉시 구독 해제
	unsubscribePermission();
	unsubscribeAlternative();

	const permission = currentPermission as NotificationPermission;

	return {
		permission: currentPermission,
		isAlternativeMode,
		isAvailable: permission === 'granted',
		needsPermission: permission === 'default',
		isBlocked: permission === 'denied'
	};
}
