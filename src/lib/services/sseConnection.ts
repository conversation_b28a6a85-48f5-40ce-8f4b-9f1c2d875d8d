/**
 * 간단하고 깔끔한 SSE 연결 관리
 * 429 에러 해결에 집중
 */

import {
	updateConnectionStatus,
	getCurrentConnectionState
} from '$lib/services/sseConnectionState';
import { routeMessage } from '$lib/services/sseMessageRouter';
import { recordError, recordConnection } from '$lib/services/sseStats';

// HMR 대응: 전역 상태로 SSE 연결 관리
declare global {
	interface Window {
		__SSE_CONNECTION__?: EventSource;
		__SSE_RECONNECT_TIMER__?: NodeJS.Timeout;
		__SSE_RECONNECT_ATTEMPTS__?: number;
	}
}

let currentEventSource: EventSource | null = null;
let reconnectTimer: NodeJS.Timeout | null = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 3;

// HMR 환경에서 기존 연결 복원
if (typeof window !== 'undefined' && import.meta.env.DEV) {
	currentEventSource = window.__SSE_CONNECTION__ || null;
	reconnectTimer = window.__SSE_RECONNECT_TIMER__ || null;
	reconnectAttempts = window.__SSE_RECONNECT_ATTEMPTS__ || 0;
}

/**
 * CSRF 쿠키 초기화 (Sanctum SPA 필수)
 */
async function initializeCsrf(): Promise<void> {
	const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;
	const baseUrl = apiEndpoint.replace('/api', '');

	const response = await fetch(`${baseUrl}/sanctum/csrf-cookie`, {
		method: 'GET',
		credentials: 'include'
	});

	if (!response.ok) {
		throw new Error(`CSRF 초기화 실패: ${response.status}`);
	}
}

/**
 * 인증 상태 확인
 */
async function checkAuth(): Promise<boolean> {
	const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;

	try {
		const response = await fetch(`${apiEndpoint}/user`, {
			method: 'GET',
			credentials: 'include'
		});

		return response.ok;
	} catch {
		return false;
	}
}

/**
 * SSE 연결 생성
 */
function createEventSource(): EventSource {
	const sseEndpoint = import.meta.env.VITE_SSE_ENDPOINT || '';

	const eventSource = new EventSource(sseEndpoint, {
		withCredentials: true
	});

	eventSource.onopen = () => {
		console.log('SSE 연결 성공');
		updateConnectionStatus('connected');
		recordConnection();
		reconnectAttempts = 0;
	};

	eventSource.onmessage = (event) => {
		try {
			const data = JSON.parse(event.data);
			routeMessage({
				type: data.type || 'message',
				timestamp: new Date().toISOString(),
				data: data
			});
		} catch (error) {
			console.error('메시지 파싱 오류:', error);
			recordError('message_parsing_error', String(error));
		}
	};

	eventSource.onerror = (error) => {
		console.error('SSE 연결 오류:', error);

		// 연결 상태에 따른 스마트한 처리
		if (eventSource.readyState === EventSource.CLOSED) {
			// 서버에서 연결을 명시적으로 종료한 경우 (예: 429 에러)
			console.log('서버에서 연결 종료됨 - 재연결 시도하지 않음');
			updateConnectionStatus('error', '서버에서 연결 거부됨 (429 에러 가능성)');
			recordError('connection_rejected', '서버에서 연결 거부');
			return;
		}

		// 네트워크 문제인 경우에만 재연결 시도
		if (navigator.onLine && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
			updateConnectionStatus('error', 'SSE 연결 오류 - 재연결 시도 예정');
			recordError('connection_error', 'SSE 연결 오류');
			scheduleReconnect();
		} else if (!navigator.onLine) {
			console.log('오프라인 상태 - 재연결 시도하지 않음');
			updateConnectionStatus('error', '오프라인 상태');
		} else {
			console.log('최대 재연결 시도 횟수 초과');
			updateConnectionStatus('error', '재연결 실패 - 수동 재연결 필요');
		}
	};

	return eventSource;
}

/**
 * 재연결 스케줄링
 */
function scheduleReconnect(): void {
	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
	}

	reconnectAttempts++;
	const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 최대 30초

	console.log(`${delay}ms 후 재연결 시도 (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
	updateConnectionStatus(
		'reconnecting',
		`재연결 시도 ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}`
	);

	reconnectTimer = setTimeout(async () => {
		try {
			await connect();
		} catch (error) {
			console.error('재연결 실패:', error);
		}
	}, delay);

	// HMR 대응: 전역 상태에 저장
	if (typeof window !== 'undefined' && import.meta.env.DEV) {
		window.__SSE_RECONNECT_TIMER__ = reconnectTimer;
		window.__SSE_RECONNECT_ATTEMPTS__ = reconnectAttempts;
	}
}

/**
 * 온라인/오프라인 상태 감지
 */
function setupNetworkListeners(): void {
	window.addEventListener('online', () => {
		console.log('네트워크 온라인 복귀 - 자동 재연결 시도');
		if (!currentEventSource || currentEventSource.readyState === EventSource.CLOSED) {
			reconnectAttempts = 0; // 온라인 복귀 시 재연결 횟수 리셋
			connect();
		}
	});

	window.addEventListener('offline', () => {
		console.log('네트워크 오프라인 - SSE 연결 대기');
		updateConnectionStatus('error', '네트워크 오프라인');
	});
}

/**
 * SSE 연결 시작
 */
export async function connect(): Promise<EventSource | null> {
	try {
		// HMR 환경에서 기존 연결이 활성 상태인지 확인
		if (
			import.meta.env.DEV &&
			currentEventSource &&
			currentEventSource.readyState === EventSource.OPEN
		) {
			console.log('HMR: 기존 SSE 연결 재사용');
			updateConnectionStatus('connected');
			return currentEventSource;
		}

		// 오프라인 상태 확인
		if (!navigator.onLine) {
			console.log('오프라인 상태 - 연결 시도하지 않음');
			updateConnectionStatus('error', '오프라인 상태');
			return null;
		}

		// 기존 연결 정리
		if (currentEventSource) {
			currentEventSource.close();
			currentEventSource = null;
		}

		updateConnectionStatus('connecting');

		// 1. CSRF 쿠키 초기화
		await initializeCsrf();

		// 2. 인증 상태 확인
		const isAuthenticated = await checkAuth();
		if (!isAuthenticated) {
			// 호출 하는 곳으로 throw 로 넘겨 주므로 상관 없음
			throw new Error('인증되지 않은 상태');
		}

		// 3. 잠시 대기 (CSRF 쿠키 설정 시간 확보)
		await new Promise((resolve) => setTimeout(resolve, 500));

		// 4. SSE 연결 생성
		currentEventSource = createEventSource();

		// HMR 대응: 전역 상태에 저장
		if (typeof window !== 'undefined' && import.meta.env.DEV) {
			window.__SSE_CONNECTION__ = currentEventSource;
			window.__SSE_RECONNECT_ATTEMPTS__ = reconnectAttempts;
		}

		// 5. 네트워크 상태 감지 설정 (한 번만)
		if (!(window as any).onlineListenerAdded) {
			setupNetworkListeners();
			(window as any).onlineListenerAdded = true;
		}

		return currentEventSource;
	} catch (error) {
		console.error('SSE 연결 실패:', error);
		updateConnectionStatus('error', String(error));
		recordError('connection_failed', String(error));
		return null;
	}
}

/**
 * SSE 연결 종료
 */
export function disconnect(): void {
	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
		reconnectTimer = null;
	}

	if (currentEventSource) {
		currentEventSource.close();
		currentEventSource = null;
	}

	// HMR 대응: 전역 상태 정리 (개발 환경이 아닌 경우에만)
	if (typeof window !== 'undefined' && !import.meta.env.DEV) {
		delete window.__SSE_CONNECTION__;
		delete window.__SSE_RECONNECT_TIMER__;
		delete window.__SSE_RECONNECT_ATTEMPTS__;
	}

	updateConnectionStatus('closed');
	reconnectAttempts = 0;
}

/**
 * 수동 재연결
 */
export async function manualReconnect(): Promise<void> {
	reconnectAttempts = 0;
	await connect();
}

/**
 * 현재 연결 상태
 */
export function getCurrentEventSource(): EventSource | null {
	return currentEventSource;
}

/**
 * 하위 호환성을 위한 함수들
 */
export async function createDefaultSseConnection(): Promise<EventSource | null> {
	return await connect();
}

export function createDefaultSseConnectionSync(): EventSource | null {
	// 동기 버전은 간단한 연결만 시도
	const user = window.sessionStorage.getItem('user');
	if (!user) {
		console.error('사용자 정보가 없습니다');
		return null;
	}

	const sseEndpoint : string = import.meta.env.VITE_SSE_ENDPOINT || '';
	return new EventSource(sseEndpoint, { withCredentials: true });
}

export function closeSseConnection(eventSource: EventSource): void {
	if (eventSource) {
		eventSource.close();
	}
}

export function getCurrentSseUrl(): string {
	return import.meta.env.VITE_SSE_ENDPOINT || '';
}

// 빈 함수들 (다른 파일에서 import하는 경우를 위해)
export function cancelReconnection(): void {
	if (reconnectTimer) {
		clearTimeout(reconnectTimer);
		reconnectTimer = null;
	}
}

export function scheduleReconnection(): void {
	scheduleReconnect();
}

/**
 * 오프라인 모드 확인
 *
 * @returns boolean 오프라인 상태 여부
 */
export function isOfflineMode(): boolean {
	// 네트워크 오프라인 상태 확인
	if (!navigator.onLine) {
		return true;
	}

	// SSE 연결 상태 확인
	const connectionState = getCurrentConnectionState();
	if (connectionState.status === 'error' || connectionState.status === 'closed') {
		return true;
	}

	// EventSource 연결 상태 확인
	return !currentEventSource || currentEventSource.readyState !== EventSource.OPEN;
}
