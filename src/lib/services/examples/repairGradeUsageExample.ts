/**
 * 수리 등급 및 확장 가능한 데이터 처리 사용 예제
 *
 * 이 파일은 6.4 작업에서 구현한 확장 가능한 데이터 타입 처리 구조의
 * 실제 사용 방법을 보여줍니다.
 */

import {
	registerDataType,
	getRegisteredDataTypes,
	getDataTypeStatistics,
	getDeletionHistory,
	restoreDeletedItems,
	initializeDataHandlers
} from '../sseDataHandlers';
import { initializeWithMigration } from '../indexedDBManager';

/**
 * 수리 등급 데이터 처리 예제
 */
export class RepairGradeUsageExample {
	private db: IDBDatabase | null = null;

	/**
	 * 시스템 초기화
	 */
	async initialize(): Promise<void> {
		console.log('=== 수리 등급 데이터 처리 시스템 초기화 ===');

		try {
			// IndexedDB 초기화
			this.db = await initializeWithMigration();
			console.log('✅ IndexedDB 초기화 완료');

			// 데이터 핸들러 초기화
			await initializeDataHandlers(this.db);
			console.log('✅ 데이터 핸들러 초기화 완료');

			// 등록된 데이터 타입 확인
			const registeredTypes = getRegisteredDataTypes();
			console.log('📋 등록된 데이터 타입:', registeredTypes);
		} catch (error) {
			console.error('❌ 초기화 실패:', error);
			throw error;
		}
	}

	/**
	 * 커스텀 데이터 타입 등록 예제
	 */
	registerCustomDataTypes(): void {
		console.log('\n=== 커스텀 데이터 타입 등록 ===');

		// 예제 1: 장비 상태 데이터 타입
		registerDataType('equipment_status', {
			storeName: 'equipment_status',
			displayName: '장비 상태',
			storeUpdater: async (db) => {
				console.log('장비 상태 스토어 업데이트됨');
				// 실제 스토어 업데이트 로직
			},
			validatePayload: (payload) => {
				if (Array.isArray(payload)) {
					return payload.every((item) => item.equipment_id && item.status);
				}
				return payload && payload.equipment_id && payload.status;
			},
			transformPayload: (payload) => {
				const transform = (item: any) => ({
					...item,
					status_updated_at: new Date().toISOString(),
					is_active: item.status === 'active'
				});

				return Array.isArray(payload) ? payload.map(transform) : transform(payload);
			},
			getItemSummary: (item) => `장비 ${item.equipment_id}: ${item.status}`
		});

		// 예제 2: 작업 로그 데이터 타입
		registerDataType('work_logs', {
			storeName: 'work_logs',
			displayName: '작업 로그',
			storeUpdater: async (db) => {
				console.log('작업 로그 스토어 업데이트됨');
			},
			validatePayload: (payload) => {
				if (Array.isArray(payload)) {
					return payload.every((item) => item.log_id && item.worker_id && item.action);
				}
				return payload && payload.log_id && payload.worker_id && payload.action;
			},
			transformPayload: (payload) => {
				const transform = (item: any) => ({
					...item,
					logged_at: item.logged_at || new Date().toISOString(),
					duration_minutes:
						item.end_time && item.start_time
							? Math.round(
									(new Date(item.end_time).getTime() - new Date(item.start_time).getTime()) / 60000
								)
							: null
				});

				return Array.isArray(payload) ? payload.map(transform) : transform(payload);
			},
			getItemSummary: (item) => `${item.worker_id}님의 ${item.action} 작업`
		});

		console.log('✅ 커스텀 데이터 타입 등록 완료');
		console.log('📋 현재 등록된 타입:', getRegisteredDataTypes());
	}

	/**
	 * 수리 등급 데이터 업데이트 시뮬레이션
	 */
	async simulateRepairGradeUpdates(): Promise<void> {
		console.log('\n=== 수리 등급 데이터 업데이트 시뮬레이션 ===');

		// 시뮬레이션 데이터
		const sampleUpdates = [
			{
				model: 'repair_grades',
				action: 'created',
				payload: [
					{
						id: 1,
						grade_name: 'S급',
						level: 1,
						description: '최고 등급',
						min_score: 95,
						max_score: 100,
						color: '#gold'
					},
					{
						id: 2,
						grade_name: 'A급',
						level: 2,
						description: '우수 등급',
						min_score: 85,
						max_score: 94,
						color: '#silver'
					},
					{
						id: 3,
						grade_name: 'B급',
						level: 3,
						description: '보통 등급',
						min_score: 70,
						max_score: 84,
						color: '#bronze'
					}
				],
				affected_ids: [1, 2, 3],
				timestamp: new Date().toISOString()
			},
			{
				model: 'repair_grades',
				action: 'updated',
				payload: {
					id: 1,
					grade_name: 'S+급',
					level: 1,
					description: '최고 등급 (업그레이드)',
					min_score: 98,
					max_score: 100,
					color: '#platinum',
					updated_at: new Date().toISOString()
				},
				affected_ids: [1],
				timestamp: new Date().toISOString()
			},
			{
				model: 'repair_grades',
				action: 'batch_updated',
				payload: [
					{
						id: 1,
						grade_name: 'S+급',
						level: 1,
						description: '최고 등급 (배치 업데이트)',
						min_score: 98,
						max_score: 100,
						color: '#platinum'
					},
					{
						id: 2,
						grade_name: 'A+급',
						level: 2,
						description: '우수 등급 (배치 업데이트)',
						min_score: 88,
						max_score: 97,
						color: '#gold'
					},
					{
						id: 3,
						grade_name: 'B+급',
						level: 3,
						description: '보통 등급 (배치 업데이트)',
						min_score: 75,
						max_score: 87,
						color: '#silver'
					}
				],
				affected_ids: [1, 2, 3],
				timestamp: new Date().toISOString()
			}
		];

		// 각 업데이트 시뮬레이션
		for (const [index, update] of sampleUpdates.entries()) {
			console.log(`\n📝 업데이트 ${index + 1}: ${update.action}`);
			console.log('   모델:', update.model);
			console.log('   영향받은 ID:', update.affected_ids);

			if (Array.isArray(update.payload)) {
				console.log('   페이로드:', `${update.payload.length}개 항목`);
			} else if (update.payload) {
				console.log('   페이로드:', update.payload.grade_name || '단일 항목');
			}

			// 실제 처리는 SSE 메시지 핸들러에서 이루어짐
			console.log('   ✅ 처리 완료 (시뮬레이션)');

			// 잠시 대기 (실제 시나리오 시뮬레이션)
			await new Promise((resolve) => setTimeout(resolve, 100));
		}
	}

	/**
	 * 커스텀 데이터 타입 업데이트 시뮬레이션
	 */
	async simulateCustomDataUpdates(): Promise<void> {
		console.log('\n=== 커스텀 데이터 타입 업데이트 시뮬레이션 ===');

		// 장비 상태 업데이트 시뮬레이션
		const equipmentUpdate = {
			model: 'equipment_status',
			action: 'created',
			payload: [
				{
					equipment_id: 'EQ001',
					status: 'active',
					location: '1층 작업장',
					last_maintenance: '2025-08-20T10:00:00Z'
				},
				{
					equipment_id: 'EQ002',
					status: 'maintenance',
					location: '2층 작업장',
					last_maintenance: '2025-08-22T14:30:00Z'
				}
			],
			affected_ids: ['EQ001', 'EQ002'],
			timestamp: new Date().toISOString()
		};

		console.log('🔧 장비 상태 업데이트:');
		console.log('   장비 수:', equipmentUpdate.payload.length);
		console.log(
			'   활성 장비:',
			equipmentUpdate.payload.filter((eq) => eq.status === 'active').length
		);
		console.log(
			'   정비 중 장비:',
			equipmentUpdate.payload.filter((eq) => eq.status === 'maintenance').length
		);

		// 작업 로그 업데이트 시뮬레이션
		const workLogUpdate = {
			model: 'work_logs',
			action: 'created',
			payload: {
				log_id: 'LOG001',
				worker_id: 'W001',
				action: '수리 작업',
				start_time: '2025-08-24T09:00:00Z',
				end_time: '2025-08-24T11:30:00Z',
				equipment_id: 'EQ001',
				notes: '정기 점검 및 부품 교체'
			},
			affected_ids: ['LOG001'],
			timestamp: new Date().toISOString()
		};

		console.log('\n📋 작업 로그 업데이트:');
		console.log('   작업자:', workLogUpdate.payload.worker_id);
		console.log('   작업 내용:', workLogUpdate.payload.action);
		console.log('   소요 시간:', '2시간 30분');
		console.log('   대상 장비:', workLogUpdate.payload.equipment_id);
	}

	/**
	 * 데이터 통계 조회 예제
	 */
	async showDataStatistics(): Promise<void> {
		console.log('\n=== 데이터 통계 조회 ===');

		const dataTypes = ['repair_grades', 'equipment_status', 'work_logs'];

		for (const dataType of dataTypes) {
			try {
				console.log(`\n📊 ${dataType} 통계:`);

				// 실제 환경에서는 이 함수가 작동하지만,
				// 예제에서는 시뮬레이션된 결과를 보여줍니다
				const mockStats = {
					totalItems: Math.floor(Math.random() * 100) + 10,
					lastUpdate: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
					recentChanges: Math.floor(Math.random() * 10),
					dataHealth: ['good', 'warning', 'error'][Math.floor(Math.random() * 3)] as
						| 'good'
						| 'warning'
						| 'error'
				};

				console.log('   총 항목 수:', mockStats.totalItems);
				console.log('   마지막 업데이트:', new Date(mockStats.lastUpdate).toLocaleString('ko-KR'));
				console.log('   최근 변경사항:', mockStats.recentChanges, '개');
				console.log(
					'   데이터 상태:',
					mockStats.dataHealth === 'good'
						? '✅ 양호'
						: mockStats.dataHealth === 'warning'
							? '⚠️ 주의'
							: '❌ 오류'
				);
			} catch (error) {
				console.log(`   ❌ ${dataType} 통계 조회 실패:`, error.message);
			}
		}
	}

	/**
	 * 삭제 히스토리 및 복구 예제
	 */
	async demonstrateDeletionRecovery(): Promise<void> {
		console.log('\n=== 삭제 히스토리 및 복구 시연 ===');

		// 삭제 시뮬레이션
		console.log('🗑️ 데이터 삭제 시뮬레이션:');
		const deletedItems = [
			{ id: 4, grade_name: 'C급', level: 4, description: '기본 등급' },
			{ id: 5, grade_name: 'D급', level: 5, description: '초급 등급' }
		];

		console.log('   삭제된 항목:', deletedItems.map((item) => item.grade_name).join(', '));
		console.log('   삭제 시간:', new Date().toLocaleString('ko-KR'));

		// 삭제 히스토리 조회 시뮬레이션
		console.log('\n📜 삭제 히스토리 조회:');
		const mockHistory = [
			{
				id: 'history-1',
				type: 'deletion_history',
				data_type: 'repair_grades',
				deleted_items: deletedItems,
				deleted_at: new Date().toISOString(),
				item_count: deletedItems.length
			}
		];

		for (const historyItem of mockHistory) {
			console.log(`   히스토리 ID: ${historyItem.id}`);
			console.log(`   데이터 타입: ${historyItem.data_type}`);
			console.log(`   삭제된 항목 수: ${historyItem.item_count}`);
			console.log(`   삭제 시간: ${new Date(historyItem.deleted_at).toLocaleString('ko-KR')}`);
		}

		// 복구 시뮬레이션
		console.log('\n🔄 데이터 복구 시뮬레이션:');
		console.log('   복구 대상:', mockHistory[0].id);
		console.log('   복구할 항목 수:', mockHistory[0].item_count);
		console.log('   ✅ 복구 완료 (시뮬레이션)');
	}

	/**
	 * 에러 처리 예제
	 */
	demonstrateErrorHandling(): void {
		console.log('\n=== 에러 처리 예제 ===');

		// 1. 잘못된 데이터 타입
		console.log('❌ 알 수 없는 데이터 타입 처리:');
		console.log('   모델: unknown_model');
		console.log('   결과: 경고 메시지 출력 후 무시');

		// 2. 잘못된 페이로드
		console.log('\n❌ 잘못된 페이로드 처리:');
		console.log('   문제: 필수 필드 누락 (id, grade_name)');
		console.log('   결과: 검증 실패 메시지 출력');

		// 3. 데이터베이스 오류
		console.log('\n❌ 데이터베이스 오류 처리:');
		console.log('   문제: 연결 실패 또는 트랜잭션 오류');
		console.log('   결과: 에러 로깅 및 사용자 알림');

		// 4. 네트워크 오류
		console.log('\n❌ 네트워크 오류 처리:');
		console.log('   문제: SSE 연결 끊김 또는 데이터 전송 실패');
		console.log('   결과: 재연결 시도 및 오프라인 모드 전환');
	}

	/**
	 * 성능 최적화 예제
	 */
	demonstratePerformanceOptimizations(): void {
		console.log('\n=== 성능 최적화 기능 ===');

		// 1. 배치 처리
		console.log('⚡ 배치 처리 최적화:');
		console.log('   - 대용량 데이터를 청크 단위로 분할 처리');
		console.log('   - 트랜잭션 최적화로 성능 향상');
		console.log('   - 메모리 사용량 제한');

		// 2. 캐시 관리
		console.log('\n💾 캐시 관리 최적화:');
		console.log('   - 자동 캐시 정리 (30일 이상 된 데이터)');
		console.log('   - 용량 기반 정리 (50MB 초과 시)');
		console.log('   - 우선순위 기반 데이터 보존');

		// 3. UI 피드백
		console.log('\n🎨 UI 피드백 최적화:');
		console.log('   - 실시간 하이라이트 효과');
		console.log('   - 배치 업데이트 파도 효과');
		console.log('   - 상세한 진행 상황 메시지');

		// 4. 메모리 관리
		console.log('\n🧠 메모리 관리 최적화:');
		console.log('   - 이벤트 리스너 자동 정리');
		console.log('   - 타이머 및 인터벌 정리');
		console.log('   - 가비지 컬렉션 최적화');
	}

	/**
	 * 전체 데모 실행
	 */
	async runFullDemo(): Promise<void> {
		try {
			await this.initialize();
			this.registerCustomDataTypes();
			await this.simulateRepairGradeUpdates();
			await this.simulateCustomDataUpdates();
			await this.showDataStatistics();
			await this.demonstrateDeletionRecovery();
			this.demonstrateErrorHandling();
			this.demonstratePerformanceOptimizations();

			console.log('\n🎉 전체 데모 완료!');
			console.log('📋 구현된 주요 기능:');
			console.log('   ✅ 확장 가능한 데이터 타입 처리 구조');
			console.log('   ✅ 수리 등급 데이터 CRUD 처리');
			console.log('   ✅ 사용자 피드백 메시지 시스템');
			console.log('   ✅ 삭제 히스토리 및 복구 기능');
			console.log('   ✅ 데이터 검증 및 변환');
			console.log('   ✅ 에러 처리 및 복구');
			console.log('   ✅ 성능 최적화');
		} catch (error) {
			console.error('❌ 데모 실행 중 오류 발생:', error);
		} finally {
			// 정리 작업
			if (this.db) {
				this.db.close();
				console.log('🔒 데이터베이스 연결 종료');
			}
		}
	}
}

/**
 * 사용 예제 실행 함수
 */
export async function runRepairGradeExample(): Promise<void> {
	console.log('🚀 수리 등급 및 확장 가능한 데이터 처리 예제 시작\n');

	const example = new RepairGradeUsageExample();
	await example.runFullDemo();
}

/**
 * 개별 기능 테스트 함수들
 */
export const RepairGradeExamples = {
	/**
	 * 기본 수리 등급 처리만 테스트
	 */
	async basicRepairGradeProcessing(): Promise<void> {
		const example = new RepairGradeUsageExample();
		await example.initialize();
		await example.simulateRepairGradeUpdates();
	},

	/**
	 * 커스텀 데이터 타입 등록만 테스트
	 */
	async customDataTypeRegistration(): Promise<void> {
		const example = new RepairGradeUsageExample();
		await example.initialize();
		example.registerCustomDataTypes();
	},

	/**
	 * 삭제 및 복구 기능만 테스트
	 */
	async deletionAndRecovery(): Promise<void> {
		const example = new RepairGradeUsageExample();
		await example.initialize();
		await example.demonstrateDeletionRecovery();
	},

	/**
	 * 통계 조회 기능만 테스트
	 */
	async statisticsQuery(): Promise<void> {
		const example = new RepairGradeUsageExample();
		await example.initialize();
		await example.showDataStatistics();
	}
};

// 브라우저 환경에서 직접 실행 가능하도록 전역 함수로 노출
if (typeof window !== 'undefined') {
	(window as any).runRepairGradeExample = runRepairGradeExample;
	(window as any).RepairGradeExamples = RepairGradeExamples;
}
