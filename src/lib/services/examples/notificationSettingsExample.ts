/**
 * 알림 설정 관리 함수 사용 예제
 *
 * 이 파일은 알림 설정 관리 함수들의 사용법을 보여주는 예제입니다.
 */

import { initializeIndexedDB } from '../indexedDBManager';
import {
	getNotificationSettings,
	getDefaultNotificationSettings,
	saveNotificationSettings,
	isWorkingHours,
	isNotificationTypeEnabled,
	setNotificationTypeEnabled,
	updateWorkingHours,
	updateDisplayDuration,
	updateSoundSettings,
	resetNotificationSettings,
	validateNotificationSettings,
	type NotificationSettings,
	type WorkingHours
} from '../notificationSettings';

/**
 * 기본 사용법 예제
 */
export async function basicUsageExample(): Promise<void> {
	console.log('=== 알림 설정 관리 기본 사용법 예제 ===');

	// 1. IndexedDB 초기화
	const db = await initializeIndexedDB();

	// 2. 현재 설정 조회
	const currentSettings = await getNotificationSettings(db);
	console.log('현재 알림 설정:', currentSettings);

	// 3. 기본 설정 조회
	const defaultSettings = getDefaultNotificationSettings();
	console.log('기본 알림 설정:', defaultSettings);

	// 4. 근무 시간 확인
	const isWorking = isWorkingHours(currentSettings.workingHours);
	console.log('현재 근무 시간인가?', isWorking);

	// 5. 특정 알림 타입 활성화 상태 확인
	const isSystemEnabled = isNotificationTypeEnabled('system', currentSettings);
	console.log('시스템 알림이 활성화되어 있는가?', isSystemEnabled);
}

/**
 * 설정 변경 예제
 */
export async function settingsUpdateExample(): Promise<void> {
	console.log('=== 알림 설정 변경 예제 ===');

	const db = await initializeIndexedDB();

	// 1. 알림 타입 비활성화
	await setNotificationTypeEnabled(db, 'maintenance', false);
	console.log('유지보수 알림을 비활성화했습니다.');

	// 2. 근무 시간 설정 변경
	await updateWorkingHours(db, {
		start: '08:30',
		end: '17:30',
		enabled: true,
		weekdays: [1, 2, 3, 4, 5] // 월-금
	});
	console.log('근무 시간을 08:30-17:30으로 설정했습니다.');

	// 3. 알림 지속시간 변경
	await updateDisplayDuration(db, 'high', 12000); // 12초
	console.log('높음 우선순위 알림 지속시간을 12초로 설정했습니다.');

	// 4. 사운드 설정 변경
	await updateSoundSettings(db, {
		enableSounds: true,
		volume: 0.7,
		customSounds: {
			urgent: '/custom-sounds/urgent-alert.mp3'
		}
	});
	console.log('사운드 설정을 업데이트했습니다.');
}

/**
 * 고급 설정 예제
 */
export async function advancedSettingsExample(): Promise<void> {
	console.log('=== 고급 알림 설정 예제 ===');

	const db = await initializeIndexedDB();

	// 1. 커스텀 설정 생성
	const customSettings: NotificationSettings = {
		enableBrowserNotifications: true,
		enableSounds: true,
		enableVibration: false, // 진동 비활성화
		workingHours: {
			start: '09:00',
			end: '18:00',
			enabled: true,
			timezone: 'Asia/Seoul',
			weekdays: [1, 2, 3, 4, 5] // 월-금만
		},
		soundFile: '/sounds/custom-notification.mp3',
		displayDuration: {
			low: 2000, // 2초
			normal: 4000, // 4초
			high: 10000, // 10초
			urgent: 0 // 수동 닫기
		},
		disabledTypes: ['system', 'debug'], // 시스템, 디버그 알림 비활성화
		volume: 0.6,
		customSounds: {
			low: '/sounds/soft-beep.mp3',
			normal: '/sounds/notification.mp3',
			high: '/sounds/important-alert.mp3',
			urgent: '/sounds/emergency-alarm.mp3'
		}
	};

	// 2. 설정 유효성 검증
	const validation = validateNotificationSettings(customSettings);
	if (!validation.isValid) {
		console.error('설정 유효성 검증 실패:', validation.errors);
		return;
	}

	// 3. 설정 저장
	await saveNotificationSettings(db, customSettings);
	console.log('커스텀 알림 설정이 저장되었습니다.');

	// 4. 저장된 설정 확인
	const savedSettings = await getNotificationSettings(db);
	console.log('저장된 설정:', savedSettings);
}

/**
 * 근무 시간 테스트 예제
 */
export async function workingHoursTestExample(): Promise<void> {
	console.log('=== 근무 시간 테스트 예제 ===');

	// 다양한 근무 시간 설정 테스트
	const workingHoursConfigs: WorkingHours[] = [
		{
			start: '09:00',
			end: '18:00',
			enabled: true,
			weekdays: [1, 2, 3, 4, 5] // 월-금
		},
		{
			start: '22:00',
			end: '06:00', // 야간 근무
			enabled: true,
			weekdays: [0, 1, 2, 3, 4, 5, 6] // 매일
		},
		{
			start: '10:00',
			end: '14:00', // 단축 근무
			enabled: true,
			weekdays: [1, 3, 5] // 월, 수, 금
		}
	];

	// 테스트 시간들
	const testTimes = [
		new Date('2024-01-02T10:00:00'), // 화요일 오전 10시
		new Date('2024-01-02T15:00:00'), // 화요일 오후 3시
		new Date('2024-01-02T20:00:00'), // 화요일 오후 8시
		new Date('2024-01-06T14:00:00'), // 토요일 오후 2시
		new Date('2024-01-03T02:00:00') // 수요일 새벽 2시
	];

	workingHoursConfigs.forEach((config, configIndex) => {
		console.log(`\n근무 시간 설정 ${configIndex + 1}:`, config);

		testTimes.forEach((testTime, timeIndex) => {
			const isWorking = isWorkingHours(config, testTime);
			console.log(
				`  테스트 시간 ${timeIndex + 1} (${testTime.toLocaleString('ko-KR')}): ${
					isWorking ? '근무 시간' : '근무 시간 외'
				}`
			);
		});
	});
}

/**
 * 설정 초기화 및 복원 예제
 */
export async function resetAndRestoreExample(): Promise<void> {
	console.log('=== 설정 초기화 및 복원 예제 ===');

	const db = await initializeIndexedDB();

	// 1. 현재 설정 백업
	const backupSettings = await getNotificationSettings(db);
	console.log('현재 설정을 백업했습니다.');

	// 2. 설정 초기화
	await resetNotificationSettings(db);
	console.log('설정을 기본값으로 초기화했습니다.');

	// 3. 초기화된 설정 확인
	const resetSettings = await getNotificationSettings(db);
	console.log('초기화된 설정:', resetSettings);

	// 4. 백업된 설정 복원 (예시)
	setTimeout(async () => {
		await saveNotificationSettings(db, backupSettings);
		console.log('백업된 설정을 복원했습니다.');
	}, 2000);
}

/**
 * 모든 예제 실행
 */
export async function runAllExamples(): Promise<void> {
	try {
		await basicUsageExample();
		await settingsUpdateExample();
		await advancedSettingsExample();
		await workingHoursTestExample();
		await resetAndRestoreExample();

		console.log('\n=== 모든 예제 실행 완료 ===');
	} catch (error) {
		console.error('예제 실행 중 오류 발생:', error);
	}
}

// 브라우저 환경에서 직접 실행할 수 있도록 전역 함수로 노출
if (typeof window !== 'undefined') {
	(window as any).notificationSettingsExamples = {
		basicUsage: basicUsageExample,
		settingsUpdate: settingsUpdateExample,
		advancedSettings: advancedSettingsExample,
		workingHoursTest: workingHoursTestExample,
		resetAndRestore: resetAndRestoreExample,
		runAll: runAllExamples
	};

	console.log('알림 설정 예제 함수들이 window.notificationSettingsExamples에 등록되었습니다.');
	console.log('사용법: window.notificationSettingsExamples.runAll()');
}
