/**
 * SSE 메시지 배치 처리 시스템 (함수 기반)
 *
 * 대량의 메시지를 효율적으로 처리하기 위한 배치 처리 및 디바운싱 기능을 제공합니다.
 */

import type { SseMessage, DataUpdateMessage, BatchConfig, NotificationMessage } from '$lib/types/sseTypes';
import { handleDataUpdateMessage, routeMessage } from '$lib/services/sseMessageRouter';

/**
 * 배치 상태 관리
 */
let messageBatch: SseMessage[] = [];
let batchTimeout: NodeJS.Timeout | null = null;
let batchConfig: BatchConfig = {
	batchSize: 10,
	batchDelay: 100 // 100ms
};

/**
 * 배치 설정 업데이트
 */
export function updateBatchConfig(config: Partial<BatchConfig>): void {
	batchConfig = { ...batchConfig, ...config };
	console.log('배치 설정 업데이트:', batchConfig);
}

/**
 * 메시지를 배치에 추가
 */
export function addMessageToBatch(message: SseMessage): void {
	messageBatch.push(message);

	if (messageBatch.length >= batchConfig.batchSize) {
		processBatch();
	} else if (!batchTimeout) {
		batchTimeout = setTimeout(() => processBatch(), batchConfig.batchDelay);
	}
}

/**
 * 배치 처리 실행
 */
function processBatch(): void {
	if (messageBatch.length === 0) return;

	const currentBatch = [...messageBatch];
	messageBatch = [];

	if (batchTimeout) {
		clearTimeout(batchTimeout);
		batchTimeout = null;
	}

	console.log(`배치 처리 시작: ${currentBatch.length}개 메시지`);
	handleMessageBatch(currentBatch);

	// 배치 통계 업데이트
	updateBatchStatsInternal(currentBatch.length);
}

/**
 * 메시지 배치 처리
 */
function handleMessageBatch(messages: SseMessage[]): void {
	// 메시지 타입별 그룹화
	const groupedMessages = groupMessagesByType(messages);

	// 타입별 배치 처리
	Object.entries(groupedMessages).forEach(([type, typeMessages]) => {
		processBatchByType(type, typeMessages);
	});
}

/**
 * 메시지 타입별 그룹화
 */
function groupMessagesByType(messages: SseMessage[]): Record<string, SseMessage[]> {
	return messages.reduce(
		(groups, message) => {
			const type = message.type;
			if (!groups[type]) groups[type] = [];
			groups[type].push(message);
			return groups;
		},
		{} as Record<string, SseMessage[]>
	);
}

/**
 * 타입별 배치 처리
 */
function processBatchByType(type: string, messages: SseMessage[]): void {
	switch (type) {
		case 'data_update':
			processDataUpdateBatch(messages);
			break;
		case 'notification':
			processNotificationBatch(messages);
			break;
		default:
			// 기본 메시지 처리
			messages.forEach((message) => routeMessage(message));
	}
}

/**
 * 데이터 업데이트 배치 처리
 */
function processDataUpdateBatch(messages: SseMessage[]): void {
	// 같은 모델의 업데이트를 병합
	const updatesByModel = groupUpdatesByModel(messages);

	// 모델별 배치 업데이트
	Object.entries(updatesByModel).forEach(([model, updates]) => {
		batchUpdateModel(model, updates);
	});
}

/**
 * 모델별 업데이트 그룹화
 */
function groupUpdatesByModel(messages: SseMessage[]): Record<string, DataUpdateMessage[]> {
	return messages.reduce(
		(acc, message) => {
			const update = message.data as DataUpdateMessage;
			if (!acc[update.model]) acc[update.model] = [];
			acc[update.model].push(update);
			return acc;
		},
		{} as Record<string, DataUpdateMessage[]>
	);
}

/**
 * 모델별 배치 업데이트
 */
function batchUpdateModel(model: string, updates: DataUpdateMessage[]): void {
	// 중복 업데이트 제거 (최신 것만 유지)
	const latestUpdates = getLatestUpdates(updates);

	// 배치 업데이트 실행
	const finalUpdates = Array.from(latestUpdates.values());
	console.log(`${model} 모델 배치 업데이트: ${finalUpdates.length}개 항목`);

	finalUpdates.forEach((update) => handleDataUpdateMessage(update));
}

/**
 * 최신 업데이트만 추출
 */
function getLatestUpdates(updates: DataUpdateMessage[]): Map<number, DataUpdateMessage> {
	const latestUpdates = new Map<number, DataUpdateMessage>();

	updates.forEach((update) => {
		update.affected_ids.forEach((id) => {
			const existing = latestUpdates.get(id);
			if (!existing || new Date(update.timestamp) > new Date(existing.timestamp)) {
				latestUpdates.set(id, update);
			}
		});
	});

	return latestUpdates;
}

/**
 * 알림 배치 처리
 */
function processNotificationBatch(messages: SseMessage[]): void {
	// 알림은 우선순위별로 정렬하여 처리
	const sortedMessages = sortNotificationsByPriority(messages);

	console.log(`알림 배치 처리: ${sortedMessages.length}개 알림`);
	sortedMessages.forEach((message) => routeMessage(message));
}

// ... (existing code) ...

/**
 * 알림 우선순위별 정렬
 */
function sortNotificationsByPriority(messages: SseMessage[]): SseMessage[] {
	const priorityOrder: Record<string, number> = { urgent: 0, high: 1, normal: 2, low: 3 };

	return messages.sort((a, b) => {
		// a.data가 NotificationMessage 타입인지 확인
		const aData = a.type === 'notification' ? (a.data as NotificationMessage) : undefined;
		const bData = b.type === 'notification' ? (b.data as NotificationMessage) : undefined;

		const aPriority = aData?.priority || 'normal';
		const bPriority = bData?.priority || 'normal';

		// priorityOrder 객체에 해당 키가 존재하는지 확인하여 안전하게 접근
		const aOrder = priorityOrder[aPriority] !== undefined ? priorityOrder[aPriority] : 2;
		const bOrder = priorityOrder[bPriority] !== undefined ? priorityOrder[bPriority] : 2;

		return aOrder - bOrder;
	});
}

// ... (existing code) ...

/**
 * 강제 배치 처리
 */
export function flushBatch(): void {
	if (messageBatch.length > 0) {
		console.log('강제 배치 처리 실행');
		processBatch();
	}
}

/**
 * 배치 처리 중단
 */
export function cancelBatch(): void {
	if (batchTimeout) {
		clearTimeout(batchTimeout);
		batchTimeout = null;
	}
	messageBatch = [];
	console.log('배치 처리 취소됨');
}

/**
 * 현재 배치 상태 조회
 */
export function getBatchStatus(): {
	queuedMessages: number;
	isProcessing: boolean;
	config: BatchConfig;
} {
	return {
		queuedMessages: messageBatch.length,
		isProcessing: batchTimeout !== null,
		config: { ...batchConfig }
	};
}

/**
 * 배치 통계
 */
let batchStats = {
	totalBatches: 0,
	totalMessages: 0,
	averageBatchSize: 0,
	lastBatchTime: null as Date | null
};

/**
 * 배치 통계 업데이트
 */
function updateBatchStatsInternal(batchSize: number): void {
	batchStats.totalBatches++;
	batchStats.totalMessages += batchSize;
	batchStats.averageBatchSize = batchStats.totalMessages / batchStats.totalBatches;
	batchStats.lastBatchTime = new Date();
}

/**
 * 배치 통계 조회
 */
export function getBatchStats(): typeof batchStats {
	return { ...batchStats };
}

/**
 * 배치 통계 초기화
 */
export function resetBatchStats(): void {
	batchStats = {
		totalBatches: 0,
		totalMessages: 0,
		averageBatchSize: 0,
		lastBatchTime: null
	};
}
