/**
 * SSE 네트워크 모니터링
 *
 * 네트워크 상태 변화를 감지하고 연결 건강성을 모니터링합니다.
 */

import {
	updateNetworkStatus,
	updateConnectionStatus,
	getCurrentConnectionState
} from '$lib/services/sseConnectionState';
import { createDefaultSseConnection } from '$lib/services/sseConnection';

/**
 * 네트워크 모니터링 상태
 */
interface NetworkMonitorState {
	isMonitoring: boolean;
	lastOnlineTime: Date | null;
	lastOfflineTime: Date | null;
	reconnectTimer: NodeJS.Timeout | null;
	healthCheckInterval: NodeJS.Timeout | null;
}

const monitorState: NetworkMonitorState = {
	isMonitoring: false,
	lastOnlineTime: null,
	lastOfflineTime: null,
	reconnectTimer: null,
	healthCheckInterval: null
};

/**
 * 네트워크 모니터링 시작
 */
export function startNetworkMonitoring(): void {
	if (monitorState.isMonitoring) {
		return;
	}

	console.log('네트워크 모니터링 시작');

	// 온라인/오프라인 이벤트 리스너
	window.addEventListener('online', handleNetworkOnline);
	window.addEventListener('offline', handleNetworkOffline);

	// 페이지 가시성 변경 감지
	document.addEventListener('visibilitychange', handleVisibilityChange);

	// 주기적 건강성 검사 시작
	startHealthCheck();

	monitorState.isMonitoring = true;
	updateNetworkStatus(navigator.onLine);
}

/**
 * 네트워크 모니터링 중지
 */
export function stopNetworkMonitoring(): void {
	if (!monitorState.isMonitoring) {
		return;
	}

	console.log('네트워크 모니터링 중지');

	window.removeEventListener('online', handleNetworkOnline);
	window.removeEventListener('offline', handleNetworkOffline);
	document.removeEventListener('visibilitychange', handleVisibilityChange);

	// 타이머 정리
	if (monitorState.reconnectTimer) {
		clearTimeout(monitorState.reconnectTimer);
		monitorState.reconnectTimer = null;
	}

	if (monitorState.healthCheckInterval) {
		clearInterval(monitorState.healthCheckInterval);
		monitorState.healthCheckInterval = null;
	}

	monitorState.isMonitoring = false;
}

/**
 * 네트워크 온라인 처리
 */
function handleNetworkOnline(): void {
	console.log('네트워크 연결 복구됨');

	monitorState.lastOnlineTime = new Date();
	updateNetworkStatus(true);

	// 연결 상태 확인 후 재연결 시도
	const connectionState = getCurrentConnectionState();
	if (connectionState.status === 'error' || connectionState.status === 'closed') {
		scheduleReconnection();
	}
}

/**
 * 네트워크 오프라인 처리
 */
function handleNetworkOffline(): void {
	console.log('네트워크 연결 끊어짐');

	monitorState.lastOfflineTime = new Date();
	updateNetworkStatus(false);
	updateConnectionStatus('disconnected', '네트워크 연결 없음');
}

/**
 * 페이지 가시성 변경 처리
 */
function handleVisibilityChange(): void {
	if (document.visibilityState === 'visible') {
		console.log('페이지가 다시 보임 - 연결 상태 확인');

		// 잠시 대기 후 건강성 검사
		setTimeout(() => {
			performHealthCheck();
		}, 1000);
	}
}

/**
 * 재연결 스케줄링
 */
function scheduleReconnection(delay: number = 2000): void {
	if (monitorState.reconnectTimer) {
		clearTimeout(monitorState.reconnectTimer);
	}

	console.log(`${delay}ms 후 재연결 시도`);

	monitorState.reconnectTimer = setTimeout(() => {
		attemptReconnection();
	}, delay);
}

/**
 * 재연결 시도
 */
function attemptReconnection(): void {
	const connectionState = getCurrentConnectionState();

	// 이미 연결되어 있으면 재연결하지 않음
	if (connectionState.status === 'connected') {
		return;
	}

	// 네트워크가 오프라인이면 재연결하지 않음
	if (!connectionState.isOnline) {
		console.log('네트워크 오프라인 상태 - 재연결 대기');
		return;
	}

	console.log('SSE 재연결 시도');
	updateConnectionStatus('reconnecting');

	try {
		// 새 연결 생성
		const eventSource = createDefaultSseConnection();
		if (!eventSource) {
			throw new Error('SSE 연결 생성 실패');
		}
	} catch (error) {
		console.error('재연결 실패:', error);
		updateConnectionStatus('error', `재연결 실패: ${error}`);

		// 지수 백오프로 재시도
		const nextDelay = Math.min(connectionState.reconnectAttempts * 2000, 30000);
		scheduleReconnection(nextDelay);
	}
}

/**
 * 주기적 건강성 검사 시작
 */
function startHealthCheck(): void {
	if (monitorState.healthCheckInterval) {
		clearInterval(monitorState.healthCheckInterval);
	}

	// 30초마다 건강성 검사
	monitorState.healthCheckInterval = setInterval(() => {
		performHealthCheck();
	}, 30000);
}

/**
 * 연결 건강성 검사 수행
 */
async function performHealthCheck(): Promise<void> {
	const connectionState = getCurrentConnectionState();

	// 연결되어 있지 않으면 검사하지 않음
	if (connectionState.status !== 'connected') {
		return;
	}

	try {
		const isHealthy = await checkConnectionHealth();

		if (!isHealthy) {
			console.warn('연결 건강성 검사 실패 - 재연결 시도');
			updateConnectionStatus('error', '연결 건강성 검사 실패');
			scheduleReconnection();
		}
	} catch (error) {
		console.error('건강성 검사 오류:', error);
	}
}

/**
 * 연결 건강성 검사
 */
async function checkConnectionHealth(): Promise<boolean> {
	try {
		// 하트비트 전송 및 응답 대기
		const healthCheck = await sendHeartbeat();
		return healthCheck.success;
	} catch (error) {
		console.warn('연결 건강성 검사 실패:', error);
		return false;
	}
}

/**
 * 하트비트 전송 및 응답 측정
 */
async function sendHeartbeat(): Promise<{ success: boolean; latency?: number }> {
	const startTime = Date.now();

	return new Promise((resolve) => {
		const heartbeatId = crypto.randomUUID();
		const timeout = setTimeout(() => {
			resolve({ success: false });
		}, 5000); // 5초 타임아웃

		// 실제 구현에서는 서버에 하트비트 요청을 보내고 응답을 기다림
		// 여기서는 시뮬레이션
		setTimeout(
			() => {
				clearTimeout(timeout);
				const latency = Date.now() - startTime;
				resolve({ success: true, latency });
			},
			Math.random() * 1000 + 100
		); // 100-1100ms 랜덤 지연
	});
}

/**
 * 네트워크 모니터링 상태 가져오기
 */
export function getNetworkMonitorState(): NetworkMonitorState {
	return { ...monitorState };
}

/**
 * 강제 재연결
 */
export function forceReconnection(): void {
	console.log('강제 재연결 시도');
	attemptReconnection();
}

/**
 * 강제 건강성 검사
 */
export function forceHealthCheck(): Promise<void> {
	console.log('강제 건강성 검사 시작');
	return performHealthCheck();
}
