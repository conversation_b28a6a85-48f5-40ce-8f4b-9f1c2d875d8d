# 카테고리 데이터 업데이트 처리 시스템

## 개요

SSE(Server-Sent Events)를 통해 수신되는 카테고리 데이터 업데이트를 처리하고, 실시간 UI 업데이트 및 애니메이션 효과를 제공하는 시스템입니다.

## 주요 기능

### 1. 카테고리 업데이트 처리

#### 1.1 카테고리 생성 처리 (`handleCategoryCreated`)

- 새로운 카테고리 데이터를 기존 데이터와 병합
- ID 기준 중복 제거 자동 처리
- 성공 메시지 및 하이라이트 애니메이션 적용
- 브라우저 알림 표시 (선택적)

```typescript
// 사용 예시
await handleCategoryCreated(
	db,
	{
		cate4: [{ id: 1, name: '새 카테고리' }],
		cate5: []
	},
	[1]
);
```

#### 1.2 카테고리 업데이트 처리 (`handleCategoryUpdated`)

- 기존 데이터와 비교하여 변경사항 감지
- 변경 유형별 상세 메시지 표시 (신규, 수정)
- 업데이트된 항목 하이라이트 효과
- 중요한 변경사항 시 브라우저 알림

```typescript
// 사용 예시
await handleCategoryUpdated(
	db,
	{
		cate4: [{ id: 1, name: '수정된 카테고리' }],
		cate5: []
	},
	[1]
);
```

#### 1.3 카테고리 삭제 처리 (`handleCategoryDeleted`)

- 삭제될 항목 정보 수집 및 로깅
- 삭제된 항목명을 포함한 상세 메시지
- 삭제 애니메이션 효과 (페이드아웃)
- 중요 카테고리 삭제 시 브라우저 알림

```typescript
// 사용 예시
await handleCategoryDeleted(db, [1, 2]);
```

#### 1.4 배치 업데이트 처리 (`handleCategoryBatchUpdated`)

- 대량 데이터 변경사항 분석
- 배치 처리 통계 표시 (신규, 수정, 삭제 개수)
- 파도 효과 애니메이션 (순차적 하이라이트)
- 대규모 업데이트 시 브라우저 알림

```typescript
// 사용 예시
await handleCategoryBatchUpdated(
	db,
	{
		cate4: [...largeDataSet],
		cate5: []
	},
	affectedIds
);
```

### 2. 실시간 UI 업데이트 및 애니메이션

#### 2.1 하이라이트 효과

- **새 항목 하이라이트**: 4초간 지속되는 강조 효과
- **업데이트된 항목 하이라이트**: 3초간 지속되는 수정 표시
- **삭제된 항목 하이라이트**: 2초간 페이드아웃 효과

#### 2.2 배치 업데이트 파도 효과

- 대량 데이터 업데이트 시 순차적 하이라이트
- 5개 그룹으로 분할하여 시각적 파도 효과 연출
- 사용자 경험 향상을 위한 점진적 표시

#### 2.3 브라우저 알림

- 중요한 변경사항 시 자동 브라우저 알림
- 5초 후 자동 닫기 기능
- 테스트 환경에서는 시뮬레이션 모드로 동작

### 3. 데이터 동기화 및 상태 관리

#### 3.1 동기화 상태 확인 (`getCategorySyncStatus`)

```typescript
const status = await getCategorySyncStatus();
// 반환값:
// {
//   lastUpdate: "2024-01-01T10:00:00Z",
//   itemCount: { cate4: 10, cate5: 5 },
//   isStale: false
// }
```

#### 3.2 강제 업데이트 실행 (`forceCategoryUpdate`)

개발 및 테스트 목적으로 카테고리 업데이트를 강제 실행할 수 있습니다.

```typescript
await forceCategoryUpdate('created', payload, [1, 2, 3]);
```

### 4. 헬퍼 함수들

#### 4.1 데이터 처리 헬퍼

- `removeDuplicatesById`: ID 기준 중복 제거
- `detectCategoryChanges`: 변경사항 감지 및 분석
- `collectDeletedCategoryItems`: 삭제 항목 정보 수집
- `analyzeBatchChanges`: 배치 변경사항 분석

#### 4.2 UI 피드백 헬퍼

- `showUpdateMessage`: 업데이트 메시지 표시
- `highlightNewItems`: 새 항목 하이라이트
- `highlightUpdatedItems`: 업데이트된 항목 하이라이트
- `highlightDeletedItems`: 삭제된 항목 하이라이트
- `highlightBatchUpdatedItems`: 배치 업데이트 파도 효과

## 스토어 통합

### Svelte 스토어 업데이트

카테고리 데이터 변경 시 자동으로 Svelte 스토어가 업데이트됩니다:

```typescript
// 스토어 구조
{
  cate4: Array<CategoryItem>,
  cate5: Array<CategoryItem>,
  lastUpdated: string | null,
  totalCount: number
}
```

### 하이라이트 스토어

UI 애니메이션을 위한 전용 스토어들:

- `highlightedItemsStore`: 새 항목 하이라이트
- `updatedItemsStore`: 업데이트된 항목 하이라이트
- `updateMessageStore`: 업데이트 메시지 관리

## 오류 처리

### 1. 견고한 오류 처리

- IndexedDB 오류 시 적절한 에러 메시지 표시
- 네트워크 오류 시 캐시된 데이터 유지
- 부분적 실패 시에도 시스템 안정성 보장

### 2. 로깅 및 디버깅

- 상세한 콘솔 로깅으로 디버깅 지원
- 업데이트 히스토리 자동 관리
- 성능 통계 및 모니터링

## 테스트 커버리지

### 단위 테스트

- 모든 주요 함수에 대한 단위 테스트 완료
- 오류 상황 및 엣지 케이스 테스트
- 모킹을 통한 독립적 테스트 환경

### 테스트 시나리오

1. **정상 동작 테스트**
   - 카테고리 생성, 수정, 삭제
   - 배치 업데이트 처리
   - 동기화 상태 확인

2. **오류 처리 테스트**
   - IndexedDB 오류 상황
   - DB 미초기화 상태
   - 네트워크 오류 시나리오

3. **데이터 무결성 테스트**
   - 중복 데이터 처리
   - 존재하지 않는 데이터 삭제
   - 대용량 데이터 처리

## 성능 최적화

### 1. 효율적인 데이터 처리

- 중복 제거 알고리즘 최적화
- 배치 처리를 통한 성능 향상
- 메모리 효율적인 데이터 구조 사용

### 2. UI 성능 최적화

- 애니메이션 지속시간 최적화
- 배치 업데이트 시 점진적 렌더링
- 불필요한 리렌더링 방지

## 사용법

### 1. 시스템 초기화

```typescript
import { initializeDataHandlers } from '$lib/services/sseDataHandlers';

// IndexedDB 인스턴스와 함께 초기화
initializeDataHandlers(dbInstance);
```

### 2. SSE 메시지 처리

시스템이 초기화되면 SSE 메시지가 자동으로 처리됩니다:

```typescript
// SSE 메시지 예시
{
  type: 'data_update',
  data: {
    model: 'categories',
    action: 'created',
    payload: { cate4: [...], cate5: [...] },
    affected_ids: [1, 2, 3]
  }
}
```

### 3. 수동 업데이트 실행

```typescript
import { forceCategoryUpdate } from '$lib/services/sseDataHandlers';

// 개발/테스트 목적으로 수동 실행
await forceCategoryUpdate('updated', newData, [1, 2, 3]);
```

## 요구사항 충족

이 구현은 다음 요구사항들을 충족합니다:

- **요구사항 4.1**: 카테고리 데이터 IndexedDB 저장
- **요구사항 4.9**: 데이터 업데이트 시 기존 데이터 교체
- **요구사항 5.2**: 선택적 데이터 업데이트 처리

## 확장성

### 1. 새로운 애니메이션 효과 추가

기존 하이라이트 함수를 참고하여 새로운 애니메이션 효과를 쉽게 추가할 수 있습니다.

### 2. 다른 데이터 모델 적용

카테고리 처리 로직을 템플릿으로 사용하여 다른 데이터 모델(직원, 그룹 등)에도 동일한 패턴을 적용할 수 있습니다.

### 3. 커스텀 알림 규칙

브라우저 알림 표시 조건을 쉽게 커스터마이징할 수 있습니다.

## 결론

이 카테고리 데이터 업데이트 처리 시스템은 실시간 데이터 동기화, 사용자 친화적인 UI 피드백, 견고한 오류 처리를 제공하여 전체 SSE 알림 시스템의 핵심 구성요소 역할을 합니다.
