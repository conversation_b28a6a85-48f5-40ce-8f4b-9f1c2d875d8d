/**
 * SSE 연결 통계 관리
 *
 * SSE 연결의 성능 및 통계 정보를 수집하고 관리합니다.
 */

import { writable } from 'svelte/store';
import type { SseConnectionStats } from '$lib/types/sseTypes';

/**
 * 초기 통계 상태
 */
const initialStats: SseConnectionStats = {
	totalConnections: 0,
	totalReconnections: 0,
	totalMessages: 0,
	totalErrors: 0,
	averageLatency: 0,
	connectionUptime: 0,
	lastMessageTime: null,
	messagesPerMinute: 0
};

/**
 * SSE 통계 스토어
 */
export const sseStats = writable<SseConnectionStats>(initialStats);

/**
 * 연결 시작 시간 추적
 */
let connectionStartTime: Date | null = null;
let messageTimestamps: Date[] = [];

/**
 * 연결 통계 업데이트
 */
export function recordConnection(): void {
	connectionStartTime = new Date();
	sseStats.update((stats) => ({
		...stats,
		totalConnections: stats.totalConnections + 1
	}));
}

/**
 * 재연결 통계 업데이트
 */
export function recordReconnection(): void {
	sseStats.update((stats) => ({
		...stats,
		totalReconnections: stats.totalReconnections + 1
	}));
}

/**
 * 메시지 수신 통계 업데이트
 */
export function recordMessage(type: string = 'unknown', size: number = 0): void {
	const now = new Date();
	messageTimestamps.push(now);

	// 1분 이전 메시지 제거
	const oneMinuteAgo = new Date(now.getTime() - 60000);
	messageTimestamps = messageTimestamps.filter((timestamp) => timestamp > oneMinuteAgo);

	sseStats.update((stats) => ({
		...stats,
		totalMessages: stats.totalMessages + 1,
		lastMessageTime: now,
		messagesPerMinute: messageTimestamps.length,
		// 메시지 타입과 크기 정보를 로그에 기록
		averageLatency: size > 0 ?
			((stats.averageLatency * stats.totalMessages) + (size * 0.1)) / (stats.totalMessages + 1) :
			stats.averageLatency
	}));

	// 메시지 타입과 크기 정보를 로그에 기록
	console.log(`메시지 수신: 타입=${type}, 크기=${size}바이트`);
}

/**
 * 오류 통계 업데이트
 */
export function recordError(type: string = 'unknown', message: string = ''): void {
	const errorTime = new Date();

	sseStats.update((stats) => ({
		...stats,
		totalErrors: stats.totalErrors + 1
	}));

	// 오류 유형과 메시지를 로그에 기록
	console.error(`[${errorTime}]SSE 오류 발생: 유형=${type}${message ? ', 메시지=' + message : ''}`);

	// 중요 오류인 경우 추가 처리
	if (type === 'connection_lost' || type === 'authentication_failed') {
		// 연결 또는 인증 관련 심각한 오류 처리
		updateConnectionUptime(); // 연결 시간 업데이트
	}
}

/**
 * 연결 시간 업데이트
 */
export function updateConnectionUptime(): void {
	if (connectionStartTime) {
		const uptime = Date.now() - connectionStartTime.getTime();
		sseStats.update((stats) => ({
			...stats,
			connectionUptime: uptime
		}));
	}
}

/**
 * 지연 시간 기록
 */
export function recordLatency(latency: number): void {
	sseStats.update((stats) => {
		const totalLatency = stats.averageLatency * stats.totalMessages + latency;
		const newAverage = totalLatency / (stats.totalMessages + 1);

		return {
			...stats,
			averageLatency: newAverage
		};
	});
}

/**
 * 통계 초기화
 */
export function resetStats(): void {
	connectionStartTime = null;
	messageTimestamps = [];
	sseStats.set(initialStats);
}

/**
 * 통계 정보를 읽기 쉬운 형태로 포맷
 */
export function formatStats(stats: SseConnectionStats): Record<string, string> {
	return {
		'총 연결 수': stats.totalConnections.toString(),
		'재연결 수': stats.totalReconnections.toString(),
		'총 메시지 수': stats.totalMessages.toString(),
		'오류 수': stats.totalErrors.toString(),
		'평균 지연시간': `${stats.averageLatency.toFixed(2)}ms`,
		'연결 지속시간': formatUptime(stats.connectionUptime),
		'분당 메시지': stats.messagesPerMinute.toString(),
		'마지막 메시지': stats.lastMessageTime ? stats.lastMessageTime.toLocaleString('ko-KR') : '없음'
	};
}

/**
 * 연결 지속시간을 읽기 쉬운 형태로 포맷
 */
function formatUptime(uptime: number): string {
	const seconds = Math.floor(uptime / 1000);
	const minutes = Math.floor(seconds / 60);
	const hours = Math.floor(minutes / 60);

	if (hours > 0) {
		return `${hours}시간 ${minutes % 60}분`;
	} else if (minutes > 0) {
		return `${minutes}분 ${seconds % 60}초`;
	} else {
		return `${seconds}초`;
	}
}

/**
 * 성능 모니터링 함수들
 */

/**
 * 메시지 처리 시간 측정 시작
 */
export function startMessageProcessingTimer(): number {
	return performance.now();
}

/**
 * 메시지 처리 시간 측정 완료
 */
export function endMessageProcessingTimer(startTime: number): number {
	const processingTime = performance.now() - startTime;
	recordLatency(processingTime);
	return processingTime;
}

/**
 * 배치 처리 통계 업데이트
 */
export function updateBatchStats(batchSize: number, processingTime: number): void {
	// 배치 처리 관련 통계 업데이트
	recordMessage('batch_processed', batchSize);
	recordLatency(processingTime);
	updateConnectionUptime();
}

/**
 * 실시간 성능 모니터링
 */
let performanceMonitorInterval: NodeJS.Timeout | null = null;

/**
 * 성능 모니터링 시작
 */
export function startPerformanceMonitoring(intervalMs: number = 5000): void {
	if (performanceMonitorInterval) {
		clearInterval(performanceMonitorInterval);
	}

	performanceMonitorInterval = setInterval(() => {
		updateMemoryUsage();

		// 성능 경고 체크
		checkPerformanceWarnings();
	}, intervalMs);

	console.log('성능 모니터링 시작');
}

/**
 * 성능 모니터링 중지
 */
export function stopPerformanceMonitoring(): void {
	if (performanceMonitorInterval) {
		clearInterval(performanceMonitorInterval);
		performanceMonitorInterval = null;
	}
	console.log('성능 모니터링 중지');
}

/**
 * 메모리 사용량 업데이트
 */
export function updateMemoryUsage(): void {
	// 브라우저에서 메모리 정보 가져오기
	if (typeof performance !== 'undefined' && (performance as any).memory) {
		const memoryInfo = (performance as any).memory;
		console.log(`메모리 사용량: ${(memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
	}
}

/**
 * 성능 통계 조회
 */
export function getPerformanceStats(): {
	averageLatency: number;
	memoryUsage: number;
} {
	let memoryUsage = 0;
	if (typeof performance !== 'undefined' && (performance as any).memory) {
		memoryUsage = (performance as any).memory.usedJSHeapSize;
	}

	let averageLatency = 0;
	sseStats.subscribe((stats) => {
		averageLatency = stats.averageLatency;
	})();

	return {
		averageLatency,
		memoryUsage
	};
}

/**
 * 오류 통계 조회
 */
export function getErrorStats(): {
	errorRate: number;
} {
	let totalErrors = 0;
	let totalMessages = 0;

	sseStats.subscribe((stats) => {
		totalErrors = stats.totalErrors;
		totalMessages = stats.totalMessages;
	})();

	const errorRate = totalMessages > 0 ? totalErrors / totalMessages : 0;

	return {
		errorRate
	};
}

/**
 * 전체 통계 조회
 */
export function getStats(): {
	connection: any;
	messages: any;
	performance: any;
	errors: any;
} {
	let currentStats: SseConnectionStats;
	sseStats.subscribe((stats) => {
		currentStats = stats;
	})();

	return {
		connection: {
			reconnectAttempts: currentStats!.totalReconnections
		},
		messages: currentStats!,
		performance: getPerformanceStats(),
		errors: getErrorStats()
	};
}

/**
 * 성능 경고 체크
 */
function checkPerformanceWarnings(): void {
	const stats = getPerformanceStats();

	// 메모리 사용량 경고 (100MB 초과)
	if (stats.memoryUsage > 100 * 1024 * 1024) {
		console.warn(`높은 메모리 사용량: ${(stats.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
	}

	// 평균 지연시간 경고 (1초 초과)
	if (stats.averageLatency > 1000) {
		console.warn(`높은 평균 지연시간: ${stats.averageLatency.toFixed(2)}ms`);
	}

	// 오류율 경고 (5% 초과)
	const errorStats = getErrorStats();
	if (errorStats.errorRate > 0.05) {
		console.warn(`높은 오류율: ${(errorStats.errorRate * 100).toFixed(2)}%`);
	}
}

/**
 * 성능 최적화 제안 생성
 */
export function generateOptimizationSuggestions(): string[] {
	const suggestions: string[] = [];
	const stats = getStats();

	// 메모리 사용량 기반 제안
	if (stats.performance.memoryUsage > 50 * 1024 * 1024) {
		suggestions.push('메시지 히스토리 크기를 줄이거나 정리 주기를 단축하세요');
	}

	// 지연시간 기반 제안
	if (stats.performance.averageLatency > 500) {
		suggestions.push('메시지 배치 크기를 늘려 처리 효율성을 개선하세요');
	}

	// 오류율 기반 제안
	if (stats.errors.errorRate > 0.02) {
		suggestions.push('재연결 전략을 보수적으로 변경하여 안정성을 높이세요');
	}

	// 연결 안정성 기반 제안
	if (stats.connection.reconnectAttempts > 10) {
		suggestions.push('네트워크 연결 상태를 확인하고 서버 엔드포인트를 점검하세요');
	}

	return suggestions;
}
