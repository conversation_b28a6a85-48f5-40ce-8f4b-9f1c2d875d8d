import { putData, getAllData } from '$lib/services/indexedDBManager';
import type { ServerFetchFunction, RetryStrategy } from './cacheTypes';
import { getCachedData } from './cacheCore';
import { getCacheStatus } from './cacheHealth';
import { DEFAULT_RETRY_STRATEGY } from './cacheConstants';

export async function detectDataChanges(existingData: any[], newData: any[]): Promise<boolean> {
	try {
		if (existingData.length !== newData.length) {
			return true;
		}

		const existingHash = JSON.stringify(
			existingData
				.map((item) => ({
					id: item.id,
					updated_at: item.updated_at
				}))
				.sort((a, b) => a.id - b.id)
		);

		const newHash = JSON.stringify(
			newData
				.map((item) => ({
					id: item.id,
					updated_at: item.updated_at
				}))
				.sort((a, b) => a.id - b.id)
		);

		return existingHash !== newHash;
	} catch (error) {
		console.warn('데이터 변경사항 감지 실패, 변경사항 있음으로 처리:', error);
		return true;
	}
}

export function calculateRetryDelay(
	attempt: number,
	strategy: RetryStrategy = DEFAULT_RETRY_STRATEGY
): number {
	let delay = strategy.baseDelay;

	if (strategy.exponentialBackoff) {
		delay = Math.min(strategy.baseDelay * Math.pow(2, attempt), strategy.maxDelay);
	}

	if (strategy.jitter) {
		const jitterRange = delay * 0.1;
		const jitter = (Math.random() - 0.5) * 2 * jitterRange;
		delay = Math.max(100, delay + jitter);
	}

	return Math.floor(delay);
}

export function classifySyncError(error: Error): {
	type: 'network' | 'server' | 'client' | 'data' | 'unknown';
	isRetryable: boolean;
	suggestedAction: string;
} {
	const message = error.message.toLowerCase();

	if (
		message.includes('network') ||
		message.includes('fetch') ||
		message.includes('connection') ||
		message.includes('timeout') ||
		message.includes('오프라인')
	) {
		return {
			type: 'network',
			isRetryable: true,
			suggestedAction: '네트워크 연결을 확인하고 재시도하세요.'
		};
	}

	if (
		message.includes('500') ||
		message.includes('502') ||
		message.includes('503') ||
		message.includes('504')
	) {
		return {
			type: 'server',
			isRetryable: true,
			suggestedAction: '서버 문제로 인한 일시적 오류입니다. 잠시 후 재시도하세요.'
		};
	}

	if (
		message.includes('400') ||
		message.includes('401') ||
		message.includes('403') ||
		message.includes('404')
	) {
		return {
			type: 'client',
			isRetryable: false,
			suggestedAction: '요청에 문제가 있습니다. 로그인 상태를 확인하세요.'
		};
	}

	if (
		message.includes('유효하지 않은') ||
		message.includes('invalid') ||
		message.includes('parse') ||
		message.includes('json')
	) {
		return {
			type: 'data',
			isRetryable: false,
			suggestedAction: '데이터 형식에 문제가 있습니다. 관리자에게 문의하세요.'
		};
	}

	return {
		type: 'unknown',
			isRetryable: true,
			suggestedAction: '알 수 없는 오류가 발생했습니다. 재시도하거나 관리자에게 문의하세요.'
	};
}

export async function warmupCaches(
	db: IDBDatabase,
	cacheConfigs: Array<{
		storeName: string;
		serverFetchFn: ServerFetchFunction<any>;
		priority?: number;
	}>
): Promise<void> {
	console.log('캐시 워밍업 시작');

	const sortedConfigs = cacheConfigs.sort((a, b) => (b.priority || 0) - (a.priority || 0));

	const warmupResults: Array<{ storeName: string; success: boolean; error?: string }> = [];

	for (const config of sortedConfigs) {
		try {
			console.log(`캐시 워밍업: ${config.storeName} (우선순위: ${config.priority || 0})`);

			const startTime = Date.now();

			await getCachedData(db, config.storeName, config.serverFetchFn, {
				enableBackgroundUpdate: true,
				allowOffline: true
			});

			const endTime = Date.now();
			console.log(`캐시 워밍업 완료: ${config.storeName} (${endTime - startTime}ms)`);

			warmupResults.push({ storeName: config.storeName, success: true });

			await new Promise((resolve) => setTimeout(resolve, 100));
		} catch (error) {
			console.error(`캐시 워밍업 실패 (${config.storeName}):`, error);
			warmupResults.push({
				storeName: config.storeName,
				success: false,
				error: error instanceof Error ? error.message : String(error)
			});
		}
	}

	window.dispatchEvent(
		new CustomEvent('cacheWarmupCompleted', {
			detail: {
				results: warmupResults,
				totalConfigs: cacheConfigs.length,
				successCount: warmupResults.filter((r) => r.success).length,
				timestamp: new Date().toISOString()
			}
		})
	);

	console.log('캐시 워밍업 완료', {
		total: cacheConfigs.length,
		success: warmupResults.filter((r) => r.success).length,
		failed: warmupResults.filter((r) => !r.success).length
	});
}

export async function smartRefreshCache<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	conditions: {
		maxAge?: number;
		minDataCount?: number;
		forceIfEmpty?: boolean;
	} = {}
): Promise<T[]> {
	const {
		maxAge = 10 * 60 * 1000,
		minDataCount = 0,
		forceIfEmpty = true
	} = conditions;

	try {
		const cacheStatus = await getCacheStatus(db, storeName, maxAge);

		let shouldRefresh = false;
		const reasons: string[] = [];

		if (forceIfEmpty && cacheStatus.dataCount === 0) {
			shouldRefresh = true;
			reasons.push('캐시가 비어있음');
		}

		if (!cacheStatus.isValid) {
			shouldRefresh = true;
			reasons.push('캐시 만료');
		}

		if (cacheStatus.dataCount < minDataCount) {
			shouldRefresh = true;
			reasons.push(`최소 데이터 개수 미달 (${cacheStatus.dataCount} < ${minDataCount})`);
		}

		if (shouldRefresh && !cacheStatus.isOnline) {
			console.warn(`스마트 새로고침 불가: 오프라인 상태 (${storeName})`);
			shouldRefresh = false;
		}

		if (shouldRefresh) {
			console.log(`스마트 캐시 새로고침 실행 (${storeName}): ${reasons.join(', ')}`);
			return await forceRefreshCache(db, storeName, serverFetchFn);
		} else {
			console.log(`스마트 캐시 새로고침 불필요 (${storeName})`);
			return (await getAllData(db, storeName)) as T[];
		}
	} catch (error) {
		console.error(`스마트 캐시 새로고침 실패 (${storeName}):`, error);
		throw error;
	}
}

export async function invalidateAllCaches(db: IDBDatabase): Promise<void> {
	const storeNames = ['categories', 'employees', 'groups', 'repair_grades'];

	console.log('모든 캐시 무효화 시작');

	for (const storeName of storeNames) {
		try {
			await putData(db, 'settings', {
				key: `${storeName}_last_update`,
				value: new Date(0).toISOString(),
				updated_at: new Date().toISOString()
			});

			console.log(`${storeName} 캐시 무효화 완료`);
		} catch (error) {
			console.error(`${storeName} 캐시 무효화 실패:`, error);
		}
	}
}

export async function forceRefreshCache<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>
): Promise<T[]> {
	console.log(`캐시 강제 새로고침: ${storeName}`);

	return getCachedData(db, storeName, serverFetchFn, {
		forceRefresh: true,
		enableBackgroundUpdate: false
	});
}
