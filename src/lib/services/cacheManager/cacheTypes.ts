/**
 * 캐시 설정 인터페이스
 */
export interface CacheOptions {
	/** 캐시 유효 기간 (밀리초) - 기본값: 5분 */
	validityPeriod?: number;
	/** 강제 새로고침 여부 */
	forceRefresh?: boolean;
	/** 백그라운드 업데이트 여부 */
	enableBackgroundUpdate?: boolean;
	/** 오프라인 모드 허용 여부 */
	allowOffline?: boolean;
	/** 재시도 횟수 */
	maxRetries?: number;
}

/**
 * 캐시 상태 정보
 */
export interface CacheStatus {
	isValid: boolean;
	lastUpdate: string | null;
	dataCount: number;
	isOnline: boolean;
}

/**
 * 서버 데이터 조회 함수 타입
 */
export type ServerFetchFunction<T> = () => Promise<T[]>;

/**
 * 동기화 상태 추적 및 관리
 */
export interface SyncState {
	storeName: string;
	isActive: boolean;
	startTime: number;
	retryCount: number;
	lastError?: string;
	priority: 'low' | 'normal' | 'high';
}

export interface CacheManagementOptions {
	/** 최대 용량 (바이트) - 기본값: 50MB */
	maxSizeBytes?: number;
	/** 경고 임계치 (최대 용량의 비율) - 기본값: 0.8 (80%) */
	warningThreshold?: number;
	/** 정리 임계치 (최대 용량의 비율) - 기본값: 0.9 (90%) */
	cleanupThreshold?: number;
	/** 자동 정리 활성화 여부 - 기본값: true */
	autoCleanup?: boolean;
	/** 정리 후 목표 사용률 - 기본값: 0.7 (70%) */
	targetUsageRatio?: number;
	/** 상세 로깅 활성화 여부 - 기본값: false */
	verbose?: boolean;
}

export interface CacheManagementResult {
	/** 현재 사용량 (바이트) */
	currentUsage: number;
	/** 최대 용량 (바이트) */
	maxSize: number;
	/** 사용률 (0-1) */
	usageRatio: number;
	/** 정리 실행 여부 */
	cleanupPerformed: boolean;
	/** 정리된 데이터 크기 (바이트) */
	cleanedSize: number;
	/** 정리된 항목 수 */
	cleanedItems: number;
	/** 스토어별 정리 결과 */
	storeResults: Array<{
		storeName: string;
		beforeCount: number;
		afterCount: number;
		deletedCount: number;
		success: boolean;
		error?: string;
	}>;
	/** 경고 메시지 */
	warnings: string[];
	/** 실행 시간 (밀리초) */
	executionTime: number;
}

export interface CacheCleanupOptions {
	/** 최대 보관 기간 (밀리초) - 기본값: 30일 */
	maxAgeMs?: number;
	/** 목표 사용률 (0-1) - 기본값: 0.7 (70%) */
	targetUsageRatio?: number;
	/** 최대 용량 (바이트) */
	maxSizeBytes?: number;
	/** 스토어별 개별 설정 */
	storeConfigs?: Record<
		string,
		{
			maxAgeMs?: number;
			maxItems?: number;
			priority?: number; // 높을수록 나중에 정리
		}
	>;
	/** 상세 결과 반환 여부 - 기본값: false */
	detailed?: boolean;
	/** 상세 로깅 활성화 여부 - 기본값: false */
	verbose?: boolean;
	/** 정리 중 오류 발생 시 중단 여부 - 기본값: false */
	stopOnError?: boolean;
}

export interface CacheCleanupResult {
	/** 총 정리된 항목 수 */
	totalCleanedItems: number;
	/** 총 정리된 데이터 크기 (추정, 바이트) */
	totalCleanedSize: number;
	/** 스토어별 정리 결과 */
	storeResults: Array<{
		storeName: string;
		beforeCount: number;
		afterCount: number;
		deletedCount: number;
		estimatedSizeSaved: number;
		success: boolean;
		error?: string;
		executionTime: number;
	}>;
	/** 정리 전 총 항목 수 */
	totalItemsBefore: number;
	/** 정리 후 총 항목 수 */
	totalItemsAfter: number;
	/** 실행 시간 (밀리초) */
	executionTime: number;
	/** 경고 메시지 */
	warnings: string[];
}

export interface CacheSchedulerOptions {
	/** 정리 주기 (밀리초) - 기본값: 1시간 */
	cleanupInterval?: number;
	/** 용량 확인 주기 (밀리초) - 기본값: 10분 */
	monitoringInterval?: number;
	/** 자동 시작 여부 - 기본값: true */
	autoStart?: boolean;
	/** 캐시 관리 옵션 */
	managementOptions?: CacheManagementOptions;
	/** 정리 옵션 */
	cleanupOptions?: CacheCleanupOptions;
}

export interface CacheScheduler {
	/** 스케줄러 시작 */
	start(): void;
	/** 스케줄러 중지 */
	stop(): void;
	/** 즉시 정리 실행 */
	runCleanupNow(): Promise<CacheCleanupResult>;
	/** 즉시 용량 관리 실행 */
	runManagementNow(): Promise<CacheManagementResult>;
	/** 스케줄러 상태 */
	isRunning(): boolean;
	/** 마지막 실행 시간 */
	getLastRunTime(): Date | null;
	/** 다음 실행 예정 시간 */
	getNextRunTime(): Date | null;
}

export interface CacheHealthOptions {
	/** 상세 분석 활성화 여부 - 기본값: false */
	detailed?: boolean;
	/** 성능 측정 활성화 여부 - 기본값: false */
	performanceTest?: boolean;
	/** 권장사항 생성 여부 - 기본값: true */
	generateRecommendations?: boolean;
}

export interface CacheHealthReport {
	/** 전체 상태 점수 (0-100) */
	overallScore: number;
	/** 전체 상태 등급 */
	overallGrade: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
	/** 스토어별 상태 */
	storeHealth: Array<{
		storeName: string;
		itemCount: number;
		isValid: boolean;
		lastUpdate: string | null;
		ageInHours: number;
		score: number;
		issues: string[];
	}>;
	/** 용량 정보 */
	storageInfo: {
		currentUsage: number;
		maxSize: number;
		usageRatio: number;
		availableSpace: number;
	};
	/** 성능 정보 */
	performance?: {
		avgReadTime: number;
		avgWriteTime: number;
		totalTestTime: number;
	};
	/** 권장사항 */
	recommendations: string[];
	/** 경고 메시지 */
	warnings: string[];
	/** 생성 시간 */
	timestamp: string;
}

/**
 * 동기화 재시도 전략 설정
 */
export interface RetryStrategy {
	maxRetries: number;
	baseDelay: number;
	maxDelay: number;
	exponentialBackoff: boolean;
	jitter: boolean; // 재시도 간격에 랜덤 요소 추가
}

/**
 * 네트워크 상태 기반 동기화 전략
 */
export interface NetworkAwareSyncOptions {
	onlineStrategy: 'aggressive' | 'normal' | 'conservative';
	offlineStrategy: 'cache-only' | 'queue-requests' | 'fail-fast';
	connectionType?: 'slow-2g' | '2g' | '3g' | '4g' | 'wifi' | 'unknown';
}

/**
 * 동기화 성능 통계 수집
 */
export interface SyncPerformanceStats {
	storeName: string;
	totalSyncs: number;
	successfulSyncs: number;
	failedSyncs: number;
	averageDuration: number;
	lastSyncTime: string | null;
	errorRate: number;
}

/**
 * 캐시 관리 상태 정보
 */
export interface CacheManagementStatus {
	/** 스케줄러 실행 상태 */
	isRunning: boolean;
	/** 마지막 정리 시간 */
	lastCleanupTime: Date | null;
	/** 다음 정리 예정 시간 */
	nextCleanupTime: Date | null;
	/** 페이지 언로드 정리 활성화 여부 */
	unloadCleanupEnabled: boolean;
	/** 캐시 관리 설정 */
	settings: {
		cleanupInterval: number;
		monitoringInterval: number;
		maxSizeBytes: number;
		targetUsageRatio: number;
		autoCleanup: boolean;
	};
}
