import { getAllData, executeWithRetry } from '$lib/services/indexedDBManager';
import type { CacheOptions, ServerFetchFunction } from '$lib/services/cacheManager/cacheTypes';
import { updateCacheInBackground } from '$lib/services/cacheManager/cacheSync';
import { getLastUpdateTime, updateCache } from '$lib/services/cacheManager/cacheUpdate';

/**
 * 캐시 우선 데이터 조회 함수
 */
export async function getCachedData<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	options: CacheOptions = {}
): Promise<T[]> {
	const {
		validityPeriod = 5 * 60 * 1000, // 5분
		forceRefresh = false,
		enableBackgroundUpdate = true,
		allowOffline = true,
		maxRetries = 3
	} = options;

	console.log(`캐시 데이터 조회 시작: ${storeName}`);

	try {
		const isOnline = navigator.onLine;
		console.log(`네트워크 상태: ${isOnline ? '온라인' : '오프라인'}`);

		if (!forceRefresh) {
			const cachedData = await executeWithRetry(() => getAllData(db, storeName), maxRetries);

			if (cachedData.length > 0) {
				console.log(`캐시된 데이터 발견: ${cachedData.length}개 항목`);

				const isValid = await isCacheValid(db, storeName, validityPeriod);
				console.log(`캐시 유효성: ${isValid ? '유효' : '만료'}`);

				if (isValid) {
					console.log(`유효한 캐시 데이터 반환: ${storeName}`);
					return cachedData as T[];
				} else if (isOnline && enableBackgroundUpdate) {
					console.log(`백그라운드 캐시 업데이트 시작: ${storeName}`);
					updateCacheInBackground(db, storeName, serverFetchFn, { maxRetries });

					return cachedData as T[];
				} else if (!isOnline && allowOffline) {
					console.log(`오프라인 모드: 만료된 캐시 데이터 반환: ${storeName}`);
					return cachedData as T[];
				}
			}
		}

		if (!isOnline && allowOffline) {
			console.log(`오프라인 상태에서 캐시 없음: 빈 배열 반환`);
			return [];
		}

		if (!isOnline) {
			throw new Error('네트워크 연결이 없고 캐시된 데이터도 없습니다.');
		}

		console.log(`서버에서 데이터 조회 시작: ${storeName}`);
		const serverData = await executeWithRetry(serverFetchFn, maxRetries);

		if (!serverData || !Array.isArray(serverData)) {
			throw new Error('서버에서 유효하지 않은 데이터를 반환했습니다.');
		}

		console.log(`서버 데이터 조회 완료: ${serverData.length}개 항목`);

		await updateCache(db, storeName, serverData);

		return serverData;
	} catch (error) {
		console.error(`캐시 데이터 조회 실패 (${storeName}):`, error);

		if (allowOffline) {
			try {
				const fallbackData = await getAllData(db, storeName);
				if (fallbackData && Array.isArray(fallbackData)) {
					console.log(`폴백 캐시 데이터 반환: ${fallbackData.length}개 항목`);
					return fallbackData as T[];
				}
			} catch (cacheError) {
				console.error('폴백 캐시 데이터 조회도 실패:', cacheError);
			}
		}

		throw new Error(`데이터 조회 실패: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * 캐시 유효성 확인
 */
export async function isCacheValid(
	db: IDBDatabase,
	storeName: string,
	validityPeriod: number = 5 * 60 * 1000
): Promise<boolean> {
	try {
		const lastUpdate = await getLastUpdateTime(db, storeName);

		if (!lastUpdate) {
			console.log(`캐시 업데이트 시간 없음: ${storeName}`);
			return false;
		}

		const lastUpdateTime = new Date(lastUpdate).getTime();
		const currentTime = Date.now();
		const timeDiff = currentTime - lastUpdateTime;
		const isValid = timeDiff < validityPeriod;

		console.log(
			`캐시 유효성 확인 (${storeName}): ${Math.round(timeDiff / 1000)}초 경과, 유효: ${isValid}`,
			{
				lastUpdate,
				timeDiff,
				validityPeriod,
				isValid
			}
		);

		if (isValid && timeDiff > validityPeriod * 0.8) {
			console.warn(
				`캐시 만료 임박 (${storeName}): ${Math.round((validityPeriod - timeDiff) / 1000)}초 남음`
			);
		}

		return isValid;
	} catch (error) {
		console.error(`캐시 유효성 확인 실패 (${storeName}):`, error);
		return false;
	}
}
