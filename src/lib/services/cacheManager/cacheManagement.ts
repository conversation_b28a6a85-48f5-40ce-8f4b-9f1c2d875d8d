import { getAllData, clearStore, putManyData } from '$lib/services/indexedDBManager';
import type {
	CacheManagementOptions,
	CacheManagementResult,
	CacheCleanupOptions,
	CacheCleanupResult,
	CacheSchedulerOptions,
	CacheScheduler, CacheManagementStatus
} from '$lib/services/cacheManager/cacheTypes';

export async function manageCacheSize(
	db: IDBDatabase,
	options: CacheManagementOptions = {}
): Promise<CacheManagementResult> {
	const startTime = Date.now();
	const {
		maxSizeBytes = 50 * 1024 * 1024, // 50MB
		warningThreshold = 0.8,
		cleanupThreshold = 0.9,
		autoCleanup = true,
		targetUsageRatio = 0.7,
		verbose = false
	} = options;

	const result: CacheManagementResult = {
		currentUsage: 0,
		maxSize: maxSizeBytes,
		usageRatio: 0,
		cleanupPerformed: false,
		cleanedSize: 0,
		cleanedItems: 0,
		storeResults: [],
		warnings: [],
		executionTime: 0
	};

	try {
		if (verbose) {
			console.log('캐시 용량 관리 시작', {
				maxSizeBytes: Math.round(maxSizeBytes / 1024 / 1024) + 'MB',
				warningThreshold: Math.round(warningThreshold * 100) + '%',
				cleanupThreshold: Math.round(cleanupThreshold * 100) + '%'
			});
		}

		if ('storage' in navigator && 'estimate' in navigator.storage) {
			const estimate = await navigator.storage.estimate();
			result.currentUsage = estimate.usage || 0;
			result.usageRatio = result.currentUsage / maxSizeBytes;

			const usageMB = Math.round(result.currentUsage / 1024 / 1024);
			const maxMB = Math.round(maxSizeBytes / 1024 / 1024);

			console.log(
				`현재 저장소 사용량: ${usageMB}MB / ${maxMB}MB (${Math.round(result.usageRatio * 100)}%)`
			);

			if (result.usageRatio >= warningThreshold) {
				const warningMsg = `저장소 사용량이 경고 임계치를 초과했습니다: ${Math.round(result.usageRatio * 100)}%`;
				result.warnings.push(warningMsg);
				console.warn(warningMsg);
			}

			if (result.usageRatio >= cleanupThreshold) {
				if (autoCleanup) {
					console.log('캐시 용량 초과, 자동 정리 시작');

					const cleanupResult = await cleanupOldCache(db, {
						targetUsageRatio,
						maxSizeBytes,
						verbose,
						detailed: true
					});

					result.cleanupPerformed = true;
					result.cleanedSize = cleanupResult.totalCleanedSize;
					result.cleanedItems = cleanupResult.totalCleanedItems;
					result.storeResults = cleanupResult.storeResults;

					const newEstimate = await navigator.storage.estimate();
					const newUsage = newEstimate.usage || 0;
					const savedSize = result.currentUsage - newUsage;

					console.log(
						`캐시 정리 완료: ${Math.round(savedSize / 1024 / 1024)}MB 절약, ` +
							`현재 사용량: ${Math.round(newUsage / 1024 / 1024)}MB`
					);

					result.currentUsage = newUsage;
					result.usageRatio = newUsage / maxSizeBytes;
				} else {
					const warningMsg = '캐시 용량이 임계치를 초과했지만 자동 정리가 비활성화되어 있습니다.';
					result.warnings.push(warningMsg);
					console.warn(warningMsg);
				}
			}

			if (verbose && estimate.quota) {
				const quotaMB = Math.round(estimate.quota / 1024 / 1024);
				console.log(`브라우저 할당량: ${quotaMB}MB`);
			}
		} else {
			const warningMsg = 'Storage API를 사용할 수 없어 정확한 용량 측정이 불가능합니다.';
			result.warnings.push(warningMsg);
			console.warn(warningMsg);

			const storeNames = ['notifications', 'categories', 'employees', 'groups', 'repair_grades'];
			let totalItems = 0;

			for (const storeName of storeNames) {
				try {
					const data = await getAllData(db, storeName);
					totalItems += data.length;
				} catch (error) {
					console.warn(`${storeName} 스토어 데이터 개수 확인 실패:`, error);
				}
			}

			result.currentUsage = totalItems * 1024;
			result.usageRatio = result.currentUsage / maxSizeBytes;

			console.log(
				`추정 저장소 사용량: ${totalItems}개 항목 (약 ${Math.round(result.currentUsage / 1024)}KB)`
			);
		}

		window.dispatchEvent(
			new CustomEvent('cacheManagementCompleted', {
				detail: {
					...result,
					timestamp: new Date().toISOString()
				}
			})
		);
	} catch (error) {
		const errorMsg = `캐시 용량 관리 실패: ${error instanceof Error ? error.message : String(error)}`;
		result.warnings.push(errorMsg);
		console.error(errorMsg);

		window.dispatchEvent(
			new CustomEvent('cacheManagementError', {
				detail: {
					error: errorMsg,
					timestamp: new Date().toISOString()
				}
			})
		);
	} finally {
		result.executionTime = Date.now() - startTime;

		if (verbose) {
			console.log(`캐시 용량 관리 완료 (${result.executionTime}ms)`, result);
		}
	}

	return result;
}

export async function cleanupOldCache(
	db: IDBDatabase,
	options: CacheCleanupOptions = {}
): Promise<CacheCleanupResult> {
	const startTime = Date.now();
	const {
		maxAgeMs = 30 * 24 * 60 * 60 * 1000, // 30일
		targetUsageRatio = 0.7,
		maxSizeBytes,
		storeConfigs = {},
		detailed = false,
		verbose = false,
		stopOnError = false
	} = options;

	const result: CacheCleanupResult = {
		totalCleanedItems: 0,
		totalCleanedSize: 0,
		storeResults: [],
		totalItemsBefore: 0,
		totalItemsAfter: 0,
		executionTime: 0,
		warnings: []
	};

	const defaultStoreConfigs = {
		notifications: { maxAgeMs: 7 * 24 * 60 * 60 * 1000, maxItems: 1000, priority: 1 },
		categories: { maxAgeMs: 90 * 24 * 60 * 60 * 1000, maxItems: 10000, priority: 5 },
		employees: { maxAgeMs: 60 * 24 * 60 * 60 * 1000, maxItems: 5000, priority: 4 },
		groups: { maxAgeMs: 60 * 24 * 60 * 60 * 1000, maxItems: 1000, priority: 4 },
		repair_grades: { maxAgeMs: 180 * 24 * 60 * 60 * 1000, maxItems: 1000, priority: 3 },
		print_settings: { maxAgeMs: 365 * 24 * 60 * 60 * 1000, maxItems: 100, priority: 5 }
	};

	const finalStoreConfigs: Record<
		string,
		{
			maxAgeMs?: number;
			maxItems?: number;
			priority?: number;
		}
	> = { ...defaultStoreConfigs, ...storeConfigs };

	const storeNames = Object.keys(finalStoreConfigs).sort(
		(a, b) => (finalStoreConfigs[a].priority || 0) - (finalStoreConfigs[b].priority || 0)
	);

	if (verbose) {
		console.log('캐시 정리 시작', {
			maxAgeMs: Math.round(maxAgeMs / 24 / 60 / 60 / 1000) + '일',
			targetUsageRatio: Math.round(targetUsageRatio * 100) + '%',
			storeCount: storeNames.length
		});
	}

	let currentUsage = 0;
	if (maxSizeBytes && 'storage' in navigator && 'estimate' in navigator.storage) {
		try {
			const estimate = await navigator.storage.estimate();
			currentUsage = estimate.usage || 0;
		} catch (error) {
			console.warn('현재 사용량 확인 실패:', error);
		}
	}

	for (const storeName of storeNames) {
		const storeStartTime = Date.now();
		const config = finalStoreConfigs[storeName];
		const storeMaxAge = config.maxAgeMs || maxAgeMs;
		const cutoffDate = new Date(Date.now() - storeMaxAge);

		const storeResult = {
			storeName,
			beforeCount: 0,
			afterCount: 0,
			deletedCount: 0,
			estimatedSizeSaved: 0,
			success: false,
			executionTime: 0,
			error: undefined as string | undefined
		};

		try {
			if (verbose) {
				console.log(`${storeName} 스토어 정리 시작 (${cutoffDate.toISOString()} 이전 데이터)`);
			}

			const allData = await getAllData(db, storeName);
			storeResult.beforeCount = allData.length;
			result.totalItemsBefore += allData.length;

			if (allData.length === 0) {
				if (verbose) {
					console.log(`${storeName} 스토어: 데이터 없음, 건너뜀`);
				}
				storeResult.success = true;
				storeResult.afterCount = 0;
				result.storeResults.push(storeResult);
				continue;
			}

			const recentDataByDate = allData.filter((item) => {
				const itemDate = new Date(
					item.updated_at || item.created_at || item.received_at || item.timestamp || 0
				);
				return itemDate > cutoffDate;
			});

			let recentData = recentDataByDate;
			if (config.maxItems && recentDataByDate.length > config.maxItems) {
				recentData = recentDataByDate
					.sort((a, b) => {
						const dateA = new Date(
							a.updated_at || a.created_at || a.received_at || a.timestamp || 0
						).getTime();
						const dateB = new Date(
							b.updated_at || b.created_at || b.received_at || b.timestamp || 0
						).getTime();
						return dateB - dateA;
					})
					.slice(0, config.maxItems);

				if (verbose) {
					console.log(
						`${storeName}: 개수 제한 적용 (${recentDataByDate.length} -> ${recentData.length})`
					);
				}
			}

			if (recentData.length >= allData.length) {
				if (verbose) {
					console.log(`${storeName} 스토어: 정리할 데이터 없음`);
				}
				storeResult.success = true;
				storeResult.afterCount = allData.length;
				result.storeResults.push(storeResult);
				continue;
			}

			await clearStore(db, storeName);

			if (recentData.length > 0) {
				const batchSize = 1000;
				for (let i = 0; i < recentData.length; i += batchSize) {
					const batch = recentData.slice(i, i + batchSize);
					await putManyData(db, storeName, batch);

					if (verbose && recentData.length > batchSize) {
						console.log(`${storeName}: 배치 저장 ${i + batch.length}/${recentData.length}`);
					}
				}
			}

			storeResult.afterCount = recentData.length;
			storeResult.deletedCount = allData.length - recentData.length;
			storeResult.success = true;

			if (detailed && storeResult.deletedCount > 0) {
				try {
					const deletedData = allData.slice(recentData.length);
					const sampleSize = Math.min(10, deletedData.length);
					const sampleData = deletedData.slice(0, sampleSize);
					const avgItemSize =
						sampleData.reduce((sum, item) => sum + JSON.stringify(item).length, 0) / sampleSize;
					storeResult.estimatedSizeSaved = Math.round(avgItemSize * storeResult.deletedCount);
				} catch (error) {
					console.warn(`${storeName}: 크기 추정 실패`, error);
				}
			}

			result.totalCleanedItems += storeResult.deletedCount;
			result.totalCleanedSize += storeResult.estimatedSizeSaved;
			result.totalItemsAfter += storeResult.afterCount;

			if (verbose || storeResult.deletedCount > 0) {
				console.log(
					`${storeName} 스토어 정리 완료: ${storeResult.deletedCount}개 항목 삭제 ` +
						`(${storeResult.beforeCount} -> ${storeResult.afterCount})`
				);
			}

			if (maxSizeBytes && currentUsage > 0) {
				try {
					const newEstimate = await navigator.storage.estimate();
					const newUsage = newEstimate.usage || 0;
					const newUsageRatio = newUsage / maxSizeBytes;

					if (newUsageRatio <= targetUsageRatio) {
						console.log(
							`목표 사용률 달성: ${Math.round(newUsageRatio * 100)}% <= ${Math.round(targetUsageRatio * 100)}%`
						);
						if (verbose) {
							console.log('목표 사용률 달성으로 정리 조기 종료');
						}
						break;
					}
				} catch (error) {
					console.warn('사용률 확인 실패:', error);
				}
			}
		} catch (error) {
			const errorMsg = `${storeName} 스토어 정리 실패: ${error instanceof Error ? error.message : String(error)}`;
			storeResult.error = errorMsg;
			result.warnings.push(errorMsg);
			console.error(errorMsg);

			if (stopOnError) {
				throw new Error(`캐시 정리 중단: ${errorMsg}`);
			}
		} finally {
			storeResult.executionTime = Date.now() - storeStartTime;
			result.storeResults.push(storeResult);
		}
	}

	result.executionTime = Date.now() - startTime;

	window.dispatchEvent(
		new CustomEvent('cacheCleanupCompleted', {
			detail: {
				...result,
				timestamp: new Date().toISOString()
			}
		})
	);

	if (verbose) {
		console.log(`캐시 정리 완료 (${result.executionTime}ms)`, {
			totalCleaned: result.totalCleanedItems,
			totalBefore: result.totalItemsBefore,
			totalAfter: result.totalItemsAfter,
			estimatedSizeSaved: Math.round(result.totalCleanedSize / 1024) + 'KB'
		});
	}

	return result;
}

export function createCacheScheduler(
	db: IDBDatabase,
	options: CacheSchedulerOptions = {}
): CacheScheduler {
	const {
		cleanupInterval = 60 * 60 * 1000, // 1시간
		monitoringInterval = 10 * 60 * 1000, // 10분
		autoStart = true,
		managementOptions = {},
		cleanupOptions = {}
	} = options;

	let cleanupTimer: NodeJS.Timeout | null = null;
	let monitoringTimer: NodeJS.Timeout | null = null;
	let lastCleanupTime: Date | null = null;
	let lastMonitoringTime: Date | null = null;
	let isActive = false;

	const scheduler: CacheScheduler = {
		start() {
			if (isActive) {
				console.warn('캐시 스케줄러가 이미 실행 중입니다.');
				return;
			}

			console.log('캐시 자동 관리 스케줄러 시작', {
				cleanupInterval: Math.round(cleanupInterval / 60 / 1000) + '분',
				monitoringInterval: Math.round(monitoringInterval / 60 / 1000) + '분'
			});

			isActive = true;

			cleanupTimer = setInterval(async () => {
				try {
					console.log('스케줄된 캐시 정리 실행');
					await cleanupOldCache(db, { ...cleanupOptions, verbose: false });
					lastCleanupTime = new Date();

					window.dispatchEvent(
						new CustomEvent('scheduledCacheCleanup', {
							detail: { timestamp: lastCleanupTime.toISOString() }
						})
					);
				} catch (error) {
					console.error('스케줄된 캐시 정리 실패:', error);
				}
			}, cleanupInterval);

			monitoringTimer = setInterval(async () => {
				try {
					const result = await manageCacheSize(db, { ...managementOptions, verbose: false });
					lastMonitoringTime = new Date();

					if (result.warnings.length > 0) {
						window.dispatchEvent(
							new CustomEvent('cacheWarning', {
								detail: {
									warnings: result.warnings,
									usageRatio: result.usageRatio,
									timestamp: lastMonitoringTime.toISOString()
								}
							})
						);
					}
				} catch (error) {
					console.error('스케줄된 캐시 모니터링 실패:', error);
				}
			}, monitoringInterval);

			setTimeout(() => {
				scheduler.runManagementNow().catch(console.error);
			}, 1000);
		},

		stop() {
			if (!isActive) {
				console.warn('캐시 스케줄러가 실행 중이 아닙니다.');
				return;
			}

			console.log('캐시 자동 관리 스케줄러 중지');

			if (cleanupTimer) {
				clearInterval(cleanupTimer);
				cleanupTimer = null;
			}

			if (monitoringTimer) {
				clearInterval(monitoringTimer);
				monitoringTimer = null;
			}

			isActive = false;
		},

		async runCleanupNow(): Promise<CacheCleanupResult> {
			console.log('즉시 캐시 정리 실행');
			const result = await cleanupOldCache(db, cleanupOptions);
			lastCleanupTime = new Date();
			return result;
		},

		async runManagementNow(): Promise<CacheManagementResult> {
			console.log('즉시 캐시 용량 관리 실행');
			const result = await manageCacheSize(db, managementOptions);
			lastMonitoringTime = new Date();
			return result;
		},

		isRunning(): boolean {
			return isActive;
		},

		getLastRunTime(): Date | null {
			return lastCleanupTime;
		},

		getNextRunTime(): Date | null {
			if (!isActive || !lastCleanupTime) {
				return null;
			}
			return new Date(lastCleanupTime.getTime() + cleanupInterval);
		}
	};

	if (autoStart) {
		scheduler.start();
	}

	return scheduler;
}

/**
 * 캐시 관리 시스템 시작
 *
 * @param db IDBDatabase 객체
 * @param options 캐시 관리 옵션
 * @returns 캐시 관리 인스턴스
 */
export function startCacheManagement(
	db: IDBDatabase,
	options: {
		cleanupInterval?: number;
		monitoringInterval?: number;
		autoCleanup?: boolean;
		maxSizeBytes?: number;
		targetUsageRatio?: number;
		enablePageUnloadCleanup?: boolean;
		verbose?: boolean;
	} = {}
): {
	scheduler: CacheScheduler;
	cleanup: () => void;
	forceCleanup: () => Promise<void>;
	getStatus: () => CacheManagementStatus;
} {
	const {
		cleanupInterval = 60 * 60 * 1000, // 1시간
		monitoringInterval = 10 * 60 * 1000, // 10분
		autoCleanup = true,
		maxSizeBytes = 50 * 1024 * 1024, // 50MB
		targetUsageRatio = 0.7,
		enablePageUnloadCleanup = true,
		verbose = false
	} = options;

	if (verbose) {
		console.log('캐시 관리 시스템 시작', {
			cleanupInterval: Math.round(cleanupInterval / 60 / 1000) + '분',
			monitoringInterval: Math.round(monitoringInterval / 60 / 1000) + '분',
			maxSizeBytes: Math.round(maxSizeBytes / 1024 / 1024) + 'MB',
			targetUsageRatio: Math.round(targetUsageRatio * 100) + '%',
			autoCleanup,
			enablePageUnloadCleanup
		});
	}

	// 캐시 스케줄러 생성
	const scheduler = createCacheScheduler(db, {
		cleanupInterval,
		monitoringInterval,
		autoStart: true,
		managementOptions: {
			maxSizeBytes,
			autoCleanup,
			targetUsageRatio,
			verbose
		},
		cleanupOptions: {
			targetUsageRatio,
			maxSizeBytes,
			verbose
		}
	});

	// 페이지 언로드 시 정리 함수
	let unloadCleanupRegistered = false;
	const pageUnloadHandler = async (event: BeforeUnloadEvent) => {
		if (verbose) {
			console.log('페이지 언로드 감지, 캐시 정리 시작');
		}

		try {
			// 스케줄러 중지
			scheduler.stop();

			// 빠른 캐시 정리 (중요하지 않은 데이터만)
			await cleanupOldCache(db, {
				maxAgeMs: 24 * 60 * 60 * 1000, // 1일
				targetUsageRatio: 0.5,
				verbose: false,
				stopOnError: false,
				storeConfigs: {
					notifications: { maxAgeMs: 3 * 60 * 60 * 1000, maxItems: 100 }, // 3시간, 100개
					categories: { maxAgeMs: 7 * 24 * 60 * 60 * 1000 }, // 7일
					employees: { maxAgeMs: 7 * 24 * 60 * 60 * 1000 }, // 7일
					groups: { maxAgeMs: 7 * 24 * 60 * 60 * 1000 }, // 7일
					repair_grades: { maxAgeMs: 30 * 24 * 60 * 60 * 1000 } // 30일
				}
			});

			// SSE 연결 정리 이벤트 발생
			window.dispatchEvent(
				new CustomEvent('pageUnloadCacheCleanup', {
					detail: { timestamp: new Date().toISOString() }
				})
			);

			if (verbose) {
				console.log('페이지 언로드 캐시 정리 완료');
			}
		} catch (error) {
			console.error('페이지 언로드 캐시 정리 실패:', error);
		}
	};

	// 페이지 가시성 변경 핸들러
	const visibilityChangeHandler = async () => {
		if (document.visibilityState === 'hidden') {
			if (verbose) {
				console.log('페이지가 숨겨짐, 백그라운드 캐시 정리 실행');
			}

			try {
				// 백그라운드에서 가벼운 정리 수행
				setTimeout(async () => {
					await cleanupOldCache(db, {
						maxAgeMs: 7 * 24 * 60 * 60 * 1000, // 7일
						targetUsageRatio: 0.8,
						verbose: false,
						storeConfigs: {
							notifications: { maxAgeMs: 24 * 60 * 60 * 1000, maxItems: 500 } // 1일, 500개
						}
					});
				}, 5000); // 5초 후 실행
			} catch (error) {
				console.error('백그라운드 캐시 정리 실패:', error);
			}
		} else if (document.visibilityState === 'visible') {
			if (verbose) {
				console.log('페이지가 다시 보임, 캐시 상태 확인');
			}

			// 페이지가 다시 보일 때 캐시 상태 확인
			setTimeout(async () => {
				try {
					await manageCacheSize(db, {
						maxSizeBytes,
						autoCleanup: true,
						verbose: false
					});
				} catch (error) {
					console.error('페이지 복귀 시 캐시 상태 확인 실패:', error);
				}
			}, 1000);
		}
	};

	// 이벤트 리스너 등록
	if (enablePageUnloadCleanup && !unloadCleanupRegistered) {
		window.addEventListener('beforeunload', pageUnloadHandler);
		document.addEventListener('visibilitychange', visibilityChangeHandler);
		unloadCleanupRegistered = true;

		if (verbose) {
			console.log('페이지 언로드 및 가시성 변경 이벤트 리스너 등록 완료');
		}
	}

	// 메모리 압박 상황 감지 (지원되는 브라우저에서)
	if ('memory' in performance && 'addEventListener' in window) {
		const memoryPressureHandler = async () => {
			console.warn('메모리 압박 상황 감지, 긴급 캐시 정리 실행');

			try {
				await cleanupOldCache(db, {
					maxAgeMs: 12 * 60 * 60 * 1000, // 12시간
					targetUsageRatio: 0.3,
					verbose: true,
					storeConfigs: {
						notifications: { maxAgeMs: 60 * 60 * 1000, maxItems: 50 }, // 1시간, 50개
						categories: { maxAgeMs: 24 * 60 * 60 * 1000 }, // 1일
						employees: { maxAgeMs: 24 * 60 * 60 * 1000 }, // 1일
						groups: { maxAgeMs: 24 * 60 * 60 * 1000 }, // 1일
						repair_grades: { maxAgeMs: 7 * 24 * 60 * 60 * 1000 } // 7일
					}
				});

				window.dispatchEvent(
					new CustomEvent('emergencyCacheCleanup', {
						detail: {
							reason: 'memory_pressure',
							timestamp: new Date().toISOString()
						}
					})
				);
			} catch (error) {
				console.error('긴급 캐시 정리 실패:', error);
			}
		};

		// 메모리 사용량 모니터링 (실험적 기능)
		const checkMemoryUsage = () => {
			try {
				const memInfo = (performance as any).memory;
				if (memInfo && memInfo.usedJSHeapSize && memInfo.totalJSHeapSize) {
					const usageRatio = memInfo.usedJSHeapSize / memInfo.totalJSHeapSize;

					if (usageRatio > 0.9) {
						memoryPressureHandler();
					}
				}
			} catch (error) {
				// 메모리 정보를 사용할 수 없는 경우 무시
			}
		};

		// 30초마다 메모리 사용량 확인
		const memoryCheckInterval = setInterval(checkMemoryUsage, 30000);

		// cleanup 함수에 메모리 체크 인터벌 정리 추가
		const originalCleanup = cleanup;
		cleanup = () => {
			clearInterval(memoryCheckInterval);
			originalCleanup();
		};
	}

	// 정리 함수
	function cleanup(): void {
		if (verbose) {
			console.log('캐시 관리 시스템 정리 시작');
		}

		// 스케줄러 중지
		scheduler.stop();

		// 이벤트 리스너 제거
		if (unloadCleanupRegistered) {
			window.removeEventListener('beforeunload', pageUnloadHandler);
			document.removeEventListener('visibilitychange', visibilityChangeHandler);
			unloadCleanupRegistered = false;
		}

		// 정리 완료 이벤트 발생
		window.dispatchEvent(
			new CustomEvent('cacheManagementStopped', {
				detail: { timestamp: new Date().toISOString() }
			})
		);

		if (verbose) {
			console.log('캐시 관리 시스템 정리 완료');
		}
	}

	// 강제 정리 함수
	async function forceCleanup(): Promise<void> {
		if (verbose) {
			console.log('강제 캐시 정리 실행');
		}

		try {
			const result = await cleanupOldCache(db, {
				maxAgeMs: 24 * 60 * 60 * 1000, // 1일
				targetUsageRatio: 0.5,
				verbose: true,
				detailed: true
			});

			window.dispatchEvent(
				new CustomEvent('forceCacheCleanup', {
					detail: {
						result,
						timestamp: new Date().toISOString()
					}
				})
			);

			console.log('강제 캐시 정리 완료:', {
				cleanedItems: result.totalCleanedItems,
				estimatedSizeSaved: Math.round(result.totalCleanedSize / 1024) + 'KB'
			});
		} catch (error) {
			console.error('강제 캐시 정리 실패:', error);
			throw error;
		}
	}

	// 상태 조회 함수
	function getStatus(): CacheManagementStatus {
		return {
			isRunning: scheduler.isRunning(),
			lastCleanupTime: scheduler.getLastRunTime(),
			nextCleanupTime: scheduler.getNextRunTime(),
			unloadCleanupEnabled: enablePageUnloadCleanup && unloadCleanupRegistered,
			settings: {
				cleanupInterval,
				monitoringInterval,
				maxSizeBytes,
				targetUsageRatio,
				autoCleanup
			}
		};
	}

	// 초기 캐시 상태 확인
	setTimeout(async () => {
		try {
			await manageCacheSize(db, {
				maxSizeBytes,
				autoCleanup,
				targetUsageRatio,
				verbose
			});
		} catch (error) {
			console.error('초기 캐시 상태 확인 실패:', error);
		}
	}, 2000);

	if (verbose) {
		console.log('캐시 관리 시스템 시작 완료');
	}

	return {
		scheduler,
		cleanup,
		forceCleanup,
		getStatus
	};
}

/**
 * 전역 캐시 관리 인스턴스 (싱글톤 패턴)
 */
let globalCacheManager: ReturnType<typeof startCacheManagement> | null = null;

/**
 * 전역 캐시 관리 시작
 *
 * @param db IDBDatabase 객체
 * @param options 캐시 관리 옵션
 * @returns 캐시 관리 인스턴스
 */
export function startGlobalCacheManagement(
	db: IDBDatabase,
	options: Parameters<typeof startCacheManagement>[1] = {}
): ReturnType<typeof startCacheManagement> {
	if (globalCacheManager) {
		console.warn('전역 캐시 관리가 이미 실행 중입니다. 기존 인스턴스를 반환합니다.');
		return globalCacheManager;
	}

	globalCacheManager = startCacheManagement(db, options);

	// 페이지 언로드 시 전역 캐시 관리 정리
	const globalCleanupHandler = () => {
		if (globalCacheManager) {
			globalCacheManager.cleanup();
			globalCacheManager = null;
		}
	};

	window.addEventListener('beforeunload', globalCleanupHandler);

	return globalCacheManager;
}

/**
 * 전역 캐시 관리 중지
 */
export function stopGlobalCacheManagement(): void {
	if (globalCacheManager) {
		globalCacheManager.cleanup();
		globalCacheManager = null;
		console.log('전역 캐시 관리 중지 완료');
	} else {
		console.warn('전역 캐시 관리가 실행 중이 아닙니다.');
	}
}

/**
 * 전역 캐시 관리 상태 조회
 */
export function getGlobalCacheManagementStatus(): CacheManagementStatus | null {
	return globalCacheManager ? globalCacheManager.getStatus() : null;
}
