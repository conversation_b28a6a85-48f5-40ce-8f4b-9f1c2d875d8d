/**
 * 캐시 관리 시작 및 정리 기능 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	startCacheManagement,
	startGlobalCacheManagement,
	stopGlobalCacheManagement
} from '../cacheManagement';

// 타이머 모킹
const mockSetTimeout = vi.fn((fn, delay) => {
	if (typeof fn === 'function') {
		fn();
	}
	return 1 as any;
});

const mockSetInterval = vi.fn((fn, delay) => {
	return 1 as any;
});

const mockClearInterval = vi.fn();
const mockClearTimeout = vi.fn();

// 전역 타이머 함수 모킹
global.setTimeout = mockSetTimeout;
global.setInterval = mockSetInterval;
global.clearInterval = mockClearInterval;
global.clearTimeout = mockClearTimeout;

// IndexedDB 모킹
const mockDB = {
	transaction: vi.fn(),
	close: vi.fn(),
	objectStoreNames: {
		contains: vi.fn(() => false)
	}
} as unknown as IDBDatabase;

// DOM API 모킹
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();
const mockDispatchEvent = vi.fn();

Object.defineProperty(window, 'addEventListener', {
	value: mockAddEventListener,
	writable: true
});

Object.defineProperty(window, 'removeEventListener', {
	value: mockRemoveEventListener,
	writable: true
});

Object.defineProperty(document, 'addEventListener', {
	value: mockAddEventListener,
	writable: true
});

Object.defineProperty(document, 'removeEventListener', {
	value: mockRemoveEventListener,
	writable: true
});

Object.defineProperty(window, 'dispatchEvent', {
	value: mockDispatchEvent,
	writable: true
});

Object.defineProperty(document, 'visibilityState', {
	value: 'visible',
	writable: true
});

// Navigator API 모킹
Object.defineProperty(navigator, 'storage', {
	value: {
		estimate: vi.fn().mockResolvedValue({
			usage: 10 * 1024 * 1024, // 10MB
			quota: 100 * 1024 * 1024 // 100MB
		})
	},
	writable: true
});

describe('캐시 관리 시작 및 정리 기능', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		mockSetTimeout.mockClear();
		mockSetInterval.mockClear();
		mockClearInterval.mockClear();
		mockClearTimeout.mockClear();
		mockAddEventListener.mockClear();
		mockRemoveEventListener.mockClear();
		mockDispatchEvent.mockClear();
	});

	afterEach(() => {
		stopGlobalCacheManagement();
	});

	describe('startCacheManagement', () => {
		it('기본 옵션으로 캐시 관리를 시작할 수 있어야 한다', () => {
			const result = startCacheManagement(mockDB);

			expect(result).toHaveProperty('scheduler');
			expect(result).toHaveProperty('cleanup');
			expect(result).toHaveProperty('forceCleanup');
			expect(result).toHaveProperty('getStatus');

			expect(result.scheduler.isRunning()).toBe(true);
		});

		it('커스텀 옵션으로 캐시 관리를 시작할 수 있어야 한다', () => {
			const options = {
				cleanupInterval: 30 * 60 * 1000, // 30분
				monitoringInterval: 5 * 60 * 1000, // 5분
				maxSizeBytes: 100 * 1024 * 1024, // 100MB
				targetUsageRatio: 0.8,
				verbose: true
			};

			const result = startCacheManagement(mockDB, options);
			const status = result.getStatus();

			expect(status.settings.cleanupInterval).toBe(options.cleanupInterval);
			expect(status.settings.monitoringInterval).toBe(options.monitoringInterval);
			expect(status.settings.maxSizeBytes).toBe(options.maxSizeBytes);
			expect(status.settings.targetUsageRatio).toBe(options.targetUsageRatio);
		});

		it('페이지 언로드 정리가 활성화되면 이벤트 리스너를 등록해야 한다', () => {
			startCacheManagement(mockDB, {
				enablePageUnloadCleanup: true
			});

			expect(mockAddEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
			expect(mockAddEventListener).toHaveBeenCalledWith('visibilitychange', expect.any(Function));
		});

		it('페이지 언로드 정리가 비활성화되면 이벤트 리스너를 등록하지 않아야 한다', () => {
			startCacheManagement(mockDB, {
				enablePageUnloadCleanup: false
			});

			// beforeunload와 visibilitychange 이벤트 리스너가 등록되지 않았는지 확인
			const beforeUnloadCalls = mockAddEventListener.mock.calls.filter(
				(call) => call[0] === 'beforeunload'
			);
			const visibilityChangeCalls = mockAddEventListener.mock.calls.filter(
				(call) => call[0] === 'visibilitychange'
			);

			expect(beforeUnloadCalls).toHaveLength(0);
			expect(visibilityChangeCalls).toHaveLength(0);
		});
	});

	describe('캐시 관리 상태 조회', () => {
		it('캐시 관리 상태를 올바르게 반환해야 한다', () => {
			const cacheManager = startCacheManagement(mockDB);
			const status = cacheManager.getStatus();

			expect(status).toHaveProperty('isRunning');
			expect(status).toHaveProperty('lastCleanupTime');
			expect(status).toHaveProperty('nextCleanupTime');
			expect(status).toHaveProperty('unloadCleanupEnabled');
			expect(status).toHaveProperty('settings');

			expect(status.isRunning).toBe(true);
			expect(status.unloadCleanupEnabled).toBe(true);
		});
	});

	describe('캐시 관리 정리', () => {
		it('cleanup 함수가 스케줄러를 중지하고 이벤트 리스너를 제거해야 한다', () => {
			const cacheManager = startCacheManagement(mockDB);

			expect(cacheManager.getStatus().isRunning).toBe(true);

			cacheManager.cleanup();

			expect(cacheManager.getStatus().isRunning).toBe(false);
			expect(mockRemoveEventListener).toHaveBeenCalledWith('beforeunload', expect.any(Function));
			expect(mockRemoveEventListener).toHaveBeenCalledWith(
				'visibilitychange',
				expect.any(Function)
			);
		});
	});

	describe('강제 캐시 정리', () => {
		it('forceCleanup 함수가 정의되어 있어야 한다', () => {
			const cacheManager = startCacheManagement(mockDB);

			expect(typeof cacheManager.forceCleanup).toBe('function');
		});
	});

	describe('전역 캐시 관리', () => {
		it('전역 캐시 관리를 시작할 수 있어야 한다', () => {
			const result = startGlobalCacheManagement(mockDB);

			expect(result).toHaveProperty('scheduler');
			expect(result).toHaveProperty('cleanup');
			expect(result).toHaveProperty('forceCleanup');
			expect(result).toHaveProperty('getStatus');
		});

		it('이미 실행 중인 전역 캐시 관리가 있으면 기존 인스턴스를 반환해야 한다', () => {
			const first = startGlobalCacheManagement(mockDB);
			const second = startGlobalCacheManagement(mockDB);

			expect(first).toBe(second);
		});

		it('전역 캐시 관리를 중지할 수 있어야 한다', () => {
			startGlobalCacheManagement(mockDB);

			expect(() => stopGlobalCacheManagement()).not.toThrow();
		});
	});

	describe('페이지 가시성 변경 처리', () => {
		it('페이지가 숨겨질 때 백그라운드 정리를 스케줄해야 한다', () => {
			startCacheManagement(mockDB, {
				enablePageUnloadCleanup: true,
				verbose: true
			});

			// visibilitychange 이벤트 핸들러 찾기
			const visibilityChangeCalls = mockAddEventListener.mock.calls.filter(
				(call) => call[0] === 'visibilitychange'
			);

			expect(visibilityChangeCalls).toHaveLength(1);

			// 페이지 숨김 상태로 변경
			Object.defineProperty(document, 'visibilityState', {
				value: 'hidden',
				writable: true
			});

			// 이벤트 핸들러 실행
			const handler = visibilityChangeCalls[0][1];
			handler();

			// setTimeout이 호출되었는지 확인
			expect(mockSetTimeout).toHaveBeenCalled();
		});

		it('페이지가 다시 보일 때 캐시 상태를 확인해야 한다', () => {
			startCacheManagement(mockDB, {
				enablePageUnloadCleanup: true,
				verbose: true
			});

			// visibilitychange 이벤트 핸들러 찾기
			const visibilityChangeCalls = mockAddEventListener.mock.calls.filter(
				(call) => call[0] === 'visibilitychange'
			);

			expect(visibilityChangeCalls).toHaveLength(1);

			// 페이지 보임 상태로 변경
			Object.defineProperty(document, 'visibilityState', {
				value: 'visible',
				writable: true
			});

			// 이벤트 핸들러 실행
			const handler = visibilityChangeCalls[0][1];
			handler();

			// setTimeout이 호출되었는지 확인
			expect(mockSetTimeout).toHaveBeenCalled();
		});
	});

	describe('메모리 압박 상황 처리', () => {
		it('메모리 사용량이 높을 때 긴급 정리를 실행해야 한다', () => {
			// performance.memory 모킹
			Object.defineProperty(performance, 'memory', {
				value: {
					usedJSHeapSize: 90 * 1024 * 1024, // 90MB
					totalJSHeapSize: 100 * 1024 * 1024 // 100MB
				},
				writable: true
			});

			startCacheManagement(mockDB, {
				verbose: true
			});

			// 메모리 체크 인터벌이 설정되었는지 확인
			expect(mockSetInterval).toHaveBeenCalled();
		});
	});
});
