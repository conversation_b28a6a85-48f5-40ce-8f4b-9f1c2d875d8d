import {
	clearStore,
	putManyData,
	putData,
	deleteData,
	executeWithRetry,
    getData
} from '$lib/services/indexedDBManager';

/**
 * 캐시 업데이트 (전체 교체)
 */
export async function updateCache<T>(
	db: IDBDatabase,
	storeName: string,
	data: T[],
	options: {
		validateData?: boolean;
		batchSize?: number;
		retryOnError?: boolean;
		maxRetries?: number;
	} = {}
): Promise<void> {
	const { validateData = true, batchSize = 1000, retryOnError = true, maxRetries = 3 } = options;

	const updateOperation = async () => {
		if (validateData && (!data || !Array.isArray(data))) {
			throw new Error(`유효하지 않은 데이터: ${storeName}`);
		}

		console.log(`캐시 업데이트 시작: ${storeName}, ${data.length}개 항목`);

		await clearStore(db, storeName);

		if (data.length > 0) {
			if (data.length > batchSize) {
				for (let i = 0; i < data.length; i += batchSize) {
					const batch = data.slice(i, i + batchSize);
					await putManyData(db, storeName, batch);
					console.log(`배치 저장 완료: ${i + batch.length}/${data.length}`);
				}
			} else {
				await putManyData(db, storeName, data);
			}
		}

		await setLastUpdateTime(db, storeName);

		console.log(`캐시 업데이트 완료: ${storeName}`);
	};

	try {
		if (retryOnError) {
			await executeWithRetry(updateOperation, maxRetries);
		} else {
			await updateOperation();
		}
	} catch (error) {
		console.error(`캐시 업데이트 실패 (${storeName}):`, error);
		throw error;
	}
}

/**
 * 선택적 캐시 업데이트 (부분 업데이트)
 */
export async function updateCacheSelectively<T extends { id: any }>(
	db: IDBDatabase,
	storeName: string,
	updates: {
		created?: T[];
		updated?: T[];
		deleted?: any[];
	},
	options: {
		maxRetries?: number;
		validateData?: boolean;
	} = {}
): Promise<void> {
	const { maxRetries = 3, validateData = true } = options;

	const selectiveUpdateOperation = async () => {
		console.log(`선택적 캐시 업데이트 시작: ${storeName}`, {
			created: updates.created?.length || 0,
			updated: updates.updated?.length || 0,
			deleted: updates.deleted?.length || 0
		});

		if (updates.created && updates.created.length > 0) {
			if (validateData) {
				const invalidItems = updates.created.filter((item) => !item.id);
				if (invalidItems.length > 0) {
					throw new Error(`생성 항목에 ID가 없는 데이터가 있습니다: ${invalidItems.length}개`);
				}
			}

			for (const item of updates.created) {
				await putData(db, storeName, item);
			}
			console.log(`생성된 항목 추가 완료: ${updates.created.length}개`);
		}

		if (updates.updated && updates.updated.length > 0) {
			if (validateData) {
				const invalidItems = updates.updated.filter((item) => !item.id);
				if (invalidItems.length > 0) {
					throw new Error(`업데이트 항목에 ID가 없는 데이터가 있습니다: ${invalidItems.length}개`);
				}
			}

			for (const item of updates.updated) {
				await putData(db, storeName, item);
			}
			console.log(`업데이트된 항목 수정 완료: ${updates.updated.length}개`);
		}

		if (updates.deleted && updates.deleted.length > 0) {
			for (const id of updates.deleted) {
				try {
					await deleteData(db, storeName, id);
				} catch (error) {
					console.warn(`삭제 대상 항목이 존재하지 않음: ${id}`);
				}
			}
			console.log(`삭제된 항목 제거 완료: ${updates.deleted.length}개`);
		}

		await setLastUpdateTime(db, storeName);

		console.log(`선택적 캐시 업데이트 완료: ${storeName}`);
	};

	try {
		await executeWithRetry(selectiveUpdateOperation, maxRetries);

		window.dispatchEvent(
			new CustomEvent('cacheSelectivelyUpdated', {
				detail: {
					storeName,
					updates,
					timestamp: new Date().toISOString()
				}
			})
		);
	} catch (error) {
		console.error(`선택적 캐시 업데이트 실패 (${storeName}):`, error);

		window.dispatchEvent(
			new CustomEvent('cacheSelectiveUpdateFailed', {
				detail: {
					storeName,
					updates,
					error: error instanceof Error ? error.message : String(error),
					timestamp: new Date().toISOString()
				}
			})
		);

		throw error;
	}
}

/**
 * 마지막 업데이트 시간 조회
 */
export async function getLastUpdateTime(
	db: IDBDatabase,
	storeName: string
): Promise<string | null> {
	try {
		const setting = await getData(db, 'settings', `${storeName}_last_update`);
		return setting?.value || null;
	} catch (error) {
		console.error(`업데이트 시간 조회 실패 (${storeName}):`, error);
		return null;
	}
}

/**
 * 마지막 업데이트 시간 저장
 */
export async function setLastUpdateTime(db: IDBDatabase, storeName: string): Promise<void> {
	try {
		const now = new Date().toISOString();

		await putData(db, 'settings', {
			key: `${storeName}_last_update`,
			value: now,
			updated_at: now
		});

		console.log(`업데이트 시간 저장 완료 (${storeName}): ${now}`);
	} catch (error) {
		console.error(`업데이트 시간 저장 실패 (${storeName}):`, error);
		throw error;
	}
}
