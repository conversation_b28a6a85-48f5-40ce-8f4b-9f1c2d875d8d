import { getAllData, getData, putData } from '$lib/services/indexedDBManager';
import type { SyncPerformanceStats, SyncState } from '$lib/services/cacheManager/cacheTypes';
import { getLastUpdateTime } from '$lib/services/cacheManager/cacheUpdate';
import { isCacheValid } from '$lib/services/cacheManager/cacheCore';
import { getActiveSyncStates } from '$lib/services/cacheManager/cacheSync';

export async function getCacheMetrics(
	db: IDBDatabase,
	storeName: string
): Promise<{
	storeName: string;
	dataCount: number;
	lastUpdate: string | null;
	cacheAge: number; // 밀리초
	isValid: boolean;
	estimatedSize: number; // 바이트 (추정치)
	syncStatus: 'idle' | 'syncing' | 'error';
}> {
	try {
		const [lastUpdate, allData] = await Promise.all([
			getLastUpdateTime(db, storeName),
			getAllData(db, storeName)
		]);

		const dataCount = allData.length;
		const cacheAge = lastUpdate ? Date.now() - new Date(lastUpdate).getTime() : -1;
		const isValid = await isCacheValid(db, storeName);

		const estimatedSize = JSON.stringify(allData).length * 2;

		const updateKey = `updating_${storeName}`;
		const syncStatus = (window as any)[updateKey] ? 'syncing' : 'idle';

		return {
			storeName,
			dataCount,
			lastUpdate,
			cacheAge,
			isValid,
			estimatedSize,
			syncStatus
		};
	} catch (error) {
		console.error(`캐시 메트릭 수집 실패 (${storeName}):`, error);
		throw error;
	}
}

export async function getAllCacheMetrics(db: IDBDatabase): Promise<
	Array<{
		storeName: string;
		dataCount: number;
		lastUpdate: string | null;
		cacheAge: number;
		isValid: boolean;
		estimatedSize: number;
	}>
> {
	const storeNames = ['categories', 'employees', 'groups', 'repair_grades', 'notifications'];
	const metrics = [];

	for (const storeName of storeNames) {
		try {
			const metric = await getCacheMetrics(db, storeName);
			metrics.push(metric);
		} catch (error) {
			console.error(`메트릭 수집 실패 (${storeName}):`, error);
			metrics.push({
				storeName,
				dataCount: 0,
				lastUpdate: null,
				cacheAge: -1,
				isValid: false,
				estimatedSize: 0
			});
		}
	}

	return metrics;
}

export async function getSyncPerformanceStats(
	db: IDBDatabase,
	storeName?: string
): Promise<SyncPerformanceStats[]> {
	try {
		const allSettings = await getAllData(db, 'settings');
		const statsSettings = allSettings.filter((setting) => setting.key.startsWith('sync_stats_'));

		const stats: SyncPerformanceStats[] = [];

		for (const statsSetting of statsSettings) {
			const storeNameFromKey = statsSetting.key.replace('sync_stats_', '');

			if (storeName && storeNameFromKey !== storeName) {
				continue;
			}

			const data = statsSetting.value;
			const errorRate = data.totalSyncs > 0 ? (data.failedSyncs / data.totalSyncs) * 100 : 0;

			stats.push({
				storeName: storeNameFromKey,
				totalSyncs: data.totalSyncs || 0,
				successfulSyncs: data.successfulSyncs || 0,
				failedSyncs: data.failedSyncs || 0,
				averageDuration: data.averageDuration || 0,
				lastSyncTime: data.lastSyncTime || null,
				errorRate: Math.round(errorRate * 100) / 100
			});
		}

		return stats;
	} catch (error) {
		console.error('동기화 성능 통계 조회 실패:', error);
		return [];
	}
}

export async function updateSyncStats(
	db: IDBDatabase,
	storeName: string,
	duration: number,
	success: boolean
): Promise<void> {
	try {
		const statsKey = `sync_stats_${storeName}`;
		const existingStats = await getData(db, 'settings', statsKey);

		const stats = existingStats?.value || {
			totalSyncs: 0,
			successfulSyncs: 0,
			failedSyncs: 0,
			totalDuration: 0,
			averageDuration: 0,
			lastSyncTime: null
		};

		stats.totalSyncs++;
		stats.totalDuration += duration;
		stats.averageDuration = Math.round(stats.totalDuration / stats.totalSyncs);
		stats.lastSyncTime = new Date().toISOString();

		if (success) {
			stats.successfulSyncs++;
		} else {
			stats.failedSyncs++;
		}

		await putData(db, 'settings', {
			key: statsKey,
			value: stats,
			updated_at: new Date().toISOString()
		});
	} catch (error) {
		console.error('동기화 성능 통계 업데이트 실패:', error);
	}
}

export async function getSystemStatus(db: IDBDatabase): Promise<{
	isOnline: boolean;
	activeSyncs: SyncState[];
	cacheMetrics: Awaited<ReturnType<typeof getAllCacheMetrics>>;
	performanceStats: SyncPerformanceStats[];
	queuedSyncs: number;
	systemHealth: 'healthy' | 'warning' | 'error';
}> {
	try {
		const [activeSyncs, cacheMetrics, performanceStats] = await Promise.all([
			Promise.resolve(getActiveSyncStates()),
			getAllCacheMetrics(db),
			getSyncPerformanceStats(db)
		]);

		const allSettings = await getAllData(db, 'settings');
		const queuedSyncs = allSettings.filter((setting) =>
			setting.key.startsWith('sync_queue_')
		).length;

		let systemHealth: 'healthy' | 'warning' | 'error' = 'healthy';

		if (!navigator.onLine || activeSyncs.length > 5) {
			systemHealth = 'warning';
		}

		const errorCaches = cacheMetrics.filter((metric) => (metric as any).syncStatus === 'error');
		if (errorCaches.length > 0) {
			systemHealth = 'error';
		}

		const highErrorRateStats = performanceStats.filter((stat) => stat.errorRate > 50);
		if (highErrorRateStats.length > 0) {
			systemHealth = 'error';
		}

		return {
			isOnline: navigator.onLine,
			activeSyncs,
			cacheMetrics,
			performanceStats,
			queuedSyncs,
			systemHealth
		};
	} catch (error) {
		console.error('시스템 상태 조회 실패:', error);
		throw error;
	}
}
