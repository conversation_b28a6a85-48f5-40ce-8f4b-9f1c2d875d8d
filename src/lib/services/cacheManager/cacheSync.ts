import { executeWithRetry, getAllData, putData, deleteData } from '$lib/services/indexedDBManager';
import type { ServerFetchFunction, SyncState, RetryStrategy, NetworkAwareSyncOptions } from '$lib/services/cacheManager/cacheTypes';
import { detectDataChanges, calculateRetryDelay, classifySyncError } from '$lib/services/cacheManager/cacheUtils';
import { setLastUpdateTime, updateCache } from '$lib/services/cacheManager/cacheUpdate';
import { DEFAULT_RETRY_STRATEGY } from '$lib/services/cacheManager/cacheConstants';
import { getCachedData, isCacheValid } from '$lib/services/cacheManager/cacheCore';
import type { CacheOptions } from '$lib/services/cacheManager/cacheTypes';

export function updateCacheInBackground<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	options: {
		maxRetries?: number;
		retryDelay?: number;
		exponentialBackoff?: boolean;
		priority?: 'low' | 'normal' | 'high';
		onProgress?: (progress: { current: number; total: number; stage: string }) => void;
		onSuccess?: (data: T[]) => void;
		onError?: (error: Error, retryCount: number) => void;
	} = {}
): void {
	const {
		maxRetries = 3,
		retryDelay = 1000,
		exponentialBackoff = true,
		priority = 'normal',
		onProgress,
		onSuccess,
		onError
	} = options;

	const updateKey = `updating_${storeName}`;
	if ((window as any)[updateKey]) {
		console.log(`백그라운드 업데이트 이미 진행 중: ${storeName}`);
		return;
	}

	(window as any)[updateKey] = {
		startTime: Date.now(),
		priority,
		retryCount: 0
	};

	const priorityDelays = {
		high: 50,
		normal: 100,
		low: 500
	};

	const initialDelay = priorityDelays[priority];

	setTimeout(async () => {
		let retryCount = 0;
		let lastError: Error | null = null;

		const performUpdate = async (): Promise<T[]> => {
			onProgress?.({ current: 1, total: 4, stage: '네트워크 상태 확인' });

			if (!navigator.onLine) {
				throw new Error('오프라인 상태');
			}

			onProgress?.({ current: 2, total: 4, stage: '서버 데이터 조회' });

			const serverData = await executeWithRetry(serverFetchFn, maxRetries);

			if (!serverData || !Array.isArray(serverData)) {
				throw new Error('서버에서 유효하지 않은 데이터를 반환했습니다');
			}

			onProgress?.({ current: 3, total: 4, stage: '변경사항 감지' });

			const existingData = await getAllData(db, storeName);
			const hasChanges = await detectDataChanges(existingData, serverData);

			if (!hasChanges) {
				console.log(`백그라운드 업데이트: 변경사항 없음 (${storeName})`);
				await setLastUpdateTime(db, storeName);
				return serverData;
			}

			onProgress?.({ current: 4, total: 4, stage: '캐시 업데이트' });

			await updateCache(db, storeName, serverData, {
				validateData: true,
				retryOnError: true,
				maxRetries: 2
			});

			return serverData;
		};

		while (retryCount <= maxRetries) {
			try {
				console.log(
					`백그라운드 업데이트 시작: ${storeName} (시도 ${retryCount + 1}/${maxRetries + 1})`
				);

				const serverData = await performUpdate();

				console.log(`백그라운드 업데이트 완료: ${storeName}, ${serverData.length}개 항목`);

				onSuccess?.(serverData);

				window.dispatchEvent(
					new CustomEvent('cacheUpdated', {
						detail: {
							storeName,
							dataCount: serverData.length,
							hasChanges: true,
							retryCount,
							duration: Date.now() - ((window as any)[updateKey]?.startTime || 0),
							timestamp: new Date().toISOString()
						}
					})
				);

				break;
			} catch (error) {
				lastError = error instanceof Error ? error : new Error(String(error));
				retryCount++;

				console.error(
					`백그라운드 캐시 업데이트 실패 (${storeName}) - 시도 ${retryCount}/${maxRetries + 1}:`,
					lastError.message
				);

				onError?.(lastError, retryCount);

				if (retryCount > maxRetries) {
					console.error(`백그라운드 캐시 업데이트 최종 실패 (${storeName}):`, lastError.message);

					window.dispatchEvent(
						new CustomEvent('cacheUpdateFailed', {
							detail: {
								storeName,
								error: lastError.message,
								retryCount: retryCount - 1,
								maxRetries,
								duration: Date.now() - ((window as any)[updateKey]?.startTime || 0),
								timestamp: new Date().toISOString()
							}
						})
					);
					break;
				}

				const delay = exponentialBackoff ? retryDelay * Math.pow(2, retryCount - 1) : retryDelay;

				console.log(`${delay}ms 후 재시도 예정 (${storeName})`);

				await new Promise((resolve) => setTimeout(resolve, delay));

				if (!navigator.onLine) {
					console.log(`재시도 중단: 오프라인 상태 (${storeName})`);
					break;
				}
			}
		}

		delete (window as any)[updateKey];
	}, initialDelay);
}

export function getActiveSyncStates(): SyncState[] {
	const states: SyncState[] = [];

	for (const key in window as any) {
		if (key.startsWith('updating_')) {
			const storeName = key.replace('updating_', '');
			const updateInfo = (window as any)[key];

			if (typeof updateInfo === 'object' && updateInfo.startTime) {
				states.push({
					storeName,
					isActive: true,
					startTime: updateInfo.startTime,
					retryCount: updateInfo.retryCount || 0,
					lastError: updateInfo.lastError,
					priority: updateInfo.priority || 'normal'
				});
			}
		}
	}

	return states;
}

export function cancelAllActiveSyncs(reason: string = '사용자 요청'): void {
	const activeStates = getActiveSyncStates();

	console.log(`모든 활성 동기화 중단: ${activeStates.length}개 (사유: ${reason})`);

	for (const state of activeStates) {
		const updateKey = `updating_${state.storeName}`;
		delete (window as any)[updateKey];

		window.dispatchEvent(
			new CustomEvent('cacheSyncCancelled', {
				detail: {
					storeName: state.storeName,
					reason,
					duration: Date.now() - state.startTime,
					timestamp: new Date().toISOString()
				}
			})
		);
	}
}

export function cancelSyncForStore(storeName: string, reason: string = '사용자 요청'): boolean {
	const updateKey = `updating_${storeName}`;
	const updateInfo = (window as any)[updateKey];

	if (updateInfo) {
		delete (window as any)[updateKey];

		console.log(`동기화 중단: ${storeName} (사유: ${reason})`);

		window.dispatchEvent(
			new CustomEvent('cacheSyncCancelled', {
				detail: {
					storeName,
					reason,
					duration: Date.now() - (updateInfo.startTime || 0),
					timestamp: new Date().toISOString()
				}
			})
		);

		return true;
	}

	return false;
}

export async function smartSync<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	options: {
		retryStrategy?: RetryStrategy;
		onRetry?: (attempt: number, error: Error, classification: any) => void;
		onFinalError?: (error: Error, classification: any) => void;
		abortSignal?: AbortSignal;
	} = {}
): Promise<T[]> {
	const { retryStrategy = DEFAULT_RETRY_STRATEGY, onRetry, onFinalError, abortSignal } = options;

	let lastError: Error | null = null;
	let lastClassification: any = null;

	for (let attempt = 0; attempt <= retryStrategy.maxRetries; attempt++) {
		try {
			if (abortSignal?.aborted) {
				throw new Error('동기화가 중단되었습니다.');
			}

			console.log(
				`스마트 동기화 시도 ${attempt + 1}/${retryStrategy.maxRetries + 1}: ${storeName}`
			);

			const data = await serverFetchFn();

			await updateCache(db, storeName, data, {
				validateData: true,
				retryOnError: false
			});

			console.log(`스마트 동기화 성공: ${storeName}, ${data.length}개 항목`);
			return data;
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));
			lastClassification = classifySyncError(lastError);

			console.error(`스마트 동기화 실패 (시도 ${attempt + 1}): ${storeName}`, {
				error: lastError.message,
				classification: lastClassification
			});

			if (!lastClassification.isRetryable) {
				console.error(`재시도 불가능한 오류: ${storeName}`, lastClassification);
				break;
			}

			if (attempt >= retryStrategy.maxRetries) {
				break;
			}

			onRetry?.(attempt + 1, lastError, lastClassification);

			const delay = calculateRetryDelay(attempt, retryStrategy);
			console.log(`${delay}ms 후 재시도 예정: ${storeName}`);

			await new Promise((resolve, reject) => {
				const timeoutId = setTimeout(resolve, delay);

				if (abortSignal) {
					const abortHandler = () => {
						clearTimeout(timeoutId);
						reject(new Error('동기화가 중단되었습니다.'));
					};

					abortSignal.addEventListener('abort', abortHandler, { once: true });
				}
			});
		}
	}

	if (lastError && lastClassification) {
		onFinalError?.(lastError, lastClassification);

		window.dispatchEvent(
			new CustomEvent('smartSyncFailed', {
				detail: {
					storeName,
					error: lastError.message,
					classification: lastClassification,
					attempts: retryStrategy.maxRetries + 1,
					timestamp: new Date().toISOString()
				}
			})
		);
	}

	throw lastError || new Error('알 수 없는 동기화 오류');
}

export async function batchSync(
	db: IDBDatabase,
	syncConfigs: Array<{
		storeName: string;
		serverFetchFn: ServerFetchFunction<any>;
		priority?: number;
		retryStrategy?: RetryStrategy;
	}>,
	options: {
		concurrency?: number;
		continueOnError?: boolean;
		onProgress?: (progress: { completed: number; total: number; current: string }) => void;
		abortSignal?: AbortSignal;
	} = {}
): Promise<{
	successful: string[];
	failed: Array<{ storeName: string; error: string }>;
	duration: number;
}> {
	const { concurrency = 3, continueOnError = true, onProgress, abortSignal } = options;

	const startTime = Date.now();
	const successful: string[] = [];
	const failed: Array<{ storeName: string; error: string }> = [];

	const sortedConfigs = syncConfigs.sort((a, b) => (b.priority || 0) - (a.priority || 0));

	console.log(`배치 동기화 시작: ${sortedConfigs.length}개 스토어, 동시 실행: ${concurrency}개`);

	const semaphore = new Array(concurrency).fill(null);
	let configIndex = 0;

	const processSyncConfig = async (slotIndex: number): Promise<void> => {
		while (configIndex < sortedConfigs.length) {
			if (abortSignal?.aborted) {
				throw new Error('배치 동기화가 중단되었습니다.');
			}

			const currentIndex = configIndex++;
			const config = sortedConfigs[currentIndex];

			if (!config) break;

			try {
				console.log(`배치 동기화 진행 중 (슬롯 ${slotIndex}): ${config.storeName}`);

				onProgress?.({
					completed: successful.length + failed.length,
					total: sortedConfigs.length,
					current: config.storeName
				});

				await smartSync(db, config.storeName, config.serverFetchFn, {
					retryStrategy: config.retryStrategy || DEFAULT_RETRY_STRATEGY,
					abortSignal
				});

				successful.push(config.storeName);
				console.log(`배치 동기화 성공 (슬롯 ${slotIndex}): ${config.storeName}`);
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : String(error);
				failed.push({ storeName: config.storeName, error: errorMessage });

				console.error(`배치 동기화 실패 (슬롯 ${slotIndex}): ${config.storeName}`, errorMessage);

				if (!continueOnError) {
					throw error;
				}
			}
		}
	};

	try {
		await Promise.all(semaphore.map((_, index) => processSyncConfig(index)));

		const duration = Date.now() - startTime;

		console.log(
			`배치 동기화 완료: 성공 ${successful.length}개, 실패 ${failed.length}개, 소요시간 ${duration}ms`
		);

		window.dispatchEvent(
			new CustomEvent('batchSyncCompleted', {
				detail: {
					successful,
					failed,
					duration,
					timestamp: new Date().toISOString()
				}
			})
		);

		return { successful, failed, duration };
	} catch (error) {
		const duration = Date.now() - startTime;

		console.error('배치 동기화 중단:', error);

		window.dispatchEvent(
			new CustomEvent('batchSyncFailed', {
				detail: {
					successful,
					failed,
					error: error instanceof Error ? error.message : String(error),
					duration,
					timestamp: new Date().toISOString()
				}
			})
		);

		throw error;
	}
}

export function monitorCacheSync<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	options: {
		interval?: number;
		validityPeriod?: number;
		onSyncStart?: () => void;
		onSyncComplete?: (data: T[]) => void;
		onSyncError?: (error: Error) => void;
	} = {}
): () => void {
	const {
		interval = 30 * 1000, // 30초
		validityPeriod = 5 * 60 * 1000, // 5분
		onSyncStart,
		onSyncComplete,
		onSyncError
	} = options;

	let isMonitoring = true;
	let timeoutId: NodeJS.Timeout;

	const checkAndSync = async () => {
		if (!isMonitoring) return;

		try {
			const isValid = await isCacheValid(db, storeName, validityPeriod);

			if (!isValid && navigator.onLine) {
				console.log(`캐시 동기화 필요: ${storeName}`);

				onSyncStart?.();

				const data = await getCachedData(db, storeName, serverFetchFn, {
					forceRefresh: true,
					enableBackgroundUpdate: false
				});

				onSyncComplete?.(data);
				console.log(`캐시 동기화 완료: ${storeName}`);
			}
		} catch (error) {
			console.error(`캐시 동기화 모니터링 오류 (${storeName}):`, error);
			onSyncError?.(error instanceof Error ? error : new Error(String(error)));
		}

		if (isMonitoring) {
			timeoutId = setTimeout(checkAndSync, interval);
		}
	};

	checkAndSync();

	return () => {
		isMonitoring = false;
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
		console.log(`캐시 동기화 모니터링 중단: ${storeName}`);
	};
}

export async function networkAwareSync<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>,
	options: NetworkAwareSyncOptions = {
		onlineStrategy: 'normal',
		offlineStrategy: 'cache-only'
	}
): Promise<T[]> {
	const isOnline = navigator.onLine;
	const connection = (navigator as any).connection;
	const connectionType = connection?.effectiveType || 'unknown';

	console.log(`네트워크 인식 동기화: ${storeName}`, {
		isOnline,
		connectionType,
		options
	});

	if (!isOnline) {
		switch (options.offlineStrategy) {
			case 'cache-only':
				console.log(`오프라인 모드: 캐시만 사용 (${storeName})`);
				return (await getAllData(db, storeName)) as T[];

			case 'queue-requests':
				console.log(`오프라인 모드: 요청 대기열에 추가 (${storeName})`);
				await queueSyncRequest(db, storeName, serverFetchFn);
				return (await getAllData(db, storeName)) as T[];

			case 'fail-fast':
				throw new Error('오프라인 상태에서는 동기화할 수 없습니다.');
		}
	}

	const syncOptions: CacheOptions = {
		enableBackgroundUpdate: true,
		allowOffline: true
	};

	switch (options.onlineStrategy) {
		case 'aggressive':
			syncOptions.forceRefresh = true;
			syncOptions.validityPeriod = 30 * 1000; // 30초
			break;

		case 'conservative':
			syncOptions.validityPeriod = 30 * 60 * 1000; // 30분
			if (connectionType === 'slow-2g' || connectionType === '2g') {
				syncOptions.enableBackgroundUpdate = false;
			}
			break;

		case 'normal':
		default:
			syncOptions.validityPeriod = 5 * 60 * 1000; // 5분
			break;
	}

	if (connectionType === 'slow-2g' || connectionType === '2g') {
		syncOptions.maxRetries = 2;
	}

	return getCachedData(db, storeName, serverFetchFn, syncOptions);
}

async function queueSyncRequest<T>(
	db: IDBDatabase,
	storeName: string,
	serverFetchFn: ServerFetchFunction<T>
): Promise<void> {
	const queueItem = {
		id: crypto.randomUUID(),
		storeName,
		timestamp: new Date().toISOString(),
		retryCount: 0
	};

	try {
		await putData(db, 'settings', {
			key: `sync_queue_${queueItem.id}`,
			value: queueItem,
			updated_at: new Date().toISOString()
		});

		console.log(`동기화 요청 큐에 추가: ${storeName}`);
	} catch (error) {
		console.error('동기화 요청 큐 저장 실패:', error);
	}
}

export async function processSyncQueue(
	db: IDBDatabase,
	syncFunctions: Map<string, ServerFetchFunction<any>>
): Promise<void> {
	try {
		const allSettings = await getAllData(db, 'settings');
		const queueItems = allSettings.filter((setting) => setting.key.startsWith('sync_queue_'));

		if (queueItems.length === 0) {
			console.log('처리할 동기화 요청이 없습니다.');
			return;
		}

		console.log(`큐에서 ${queueItems.length}개의 동기화 요청 처리 시작`);

		for (const queueItem of queueItems) {
			const { storeName } = queueItem.value;
			const syncFn = syncFunctions.get(storeName);

			if (!syncFn) {
				console.warn(`동기화 함수를 찾을 수 없음: ${storeName}`);
				continue;
			}

			try {
				await getCachedData(db, storeName, syncFn, {
					forceRefresh: true,
					enableBackgroundUpdate: false
				});

				await deleteData(db, 'settings', queueItem.key);
				console.log(`큐 동기화 완료: ${storeName}`);
			} catch (error) {
				console.error(`큐 동기화 실패: ${storeName}`, error);

				queueItem.value.retryCount++;

				if (queueItem.value.retryCount >= 3) {
					await deleteData(db, 'settings', queueItem.key);
					console.error(`큐 동기화 최종 실패: ${storeName}`);
				} else {
					await putData(db, 'settings', {
						key: queueItem.key,
						value: queueItem.value,
						updated_at: new Date().toISOString()
					});
				}
			}
		}

		console.log('큐 동기화 처리 완료');
	} catch (error) {
		console.error('큐 동기화 처리 실패:', error);
	}
}
