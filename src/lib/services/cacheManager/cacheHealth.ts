import { getAllData } from '$lib/services/indexedDBManager';
import type { CacheHealthOptions, CacheHealthReport, CacheStatus } from '$lib/services/cacheManager/cacheTypes';
import { isCacheValid } from '$lib/services/cacheManager/cacheCore';
import { getLastUpdateTime } from '$lib/services/cacheManager/cacheUpdate';

export async function generateCacheHealthReport(
	db: IDBDatabase,
	options: CacheHealthOptions = {}
): Promise<CacheHealthReport> {
	const { detailed = false, performanceTest = false, generateRecommendations = true } = options;

	const report: CacheHealthReport = {
		overallScore: 0,
		overallGrade: 'good',
		storeHealth: [],
		storageInfo: {
			currentUsage: 0,
			maxSize: 50 * 1024 * 1024,
			usageRatio: 0,
			availableSpace: 0
		},
		recommendations: [],
		warnings: [],
		timestamp: new Date().toISOString()
	};

	try {
		if ('storage' in navigator && 'estimate' in navigator.storage) {
			const estimate = await navigator.storage.estimate();
			report.storageInfo.currentUsage = estimate.usage || 0;
			report.storageInfo.usageRatio = report.storageInfo.currentUsage / report.storageInfo.maxSize;
			report.storageInfo.availableSpace =
				report.storageInfo.maxSize - report.storageInfo.currentUsage;
		}

		const storeNames = ['notifications', 'categories', 'employees', 'groups', 'repair_grades'];
		let totalScore = 0;

		for (const storeName of storeNames) {
			const storeHealth = {
				storeName,
				itemCount: 0,
				isValid: false,
				lastUpdate: null as string | null,
				ageInHours: 0,
				score: 0,
				issues: [] as string[]
			};

			try {
				const data = await getAllData(db, storeName);
				storeHealth.itemCount = data.length;

				storeHealth.isValid = await isCacheValid(db, storeName);
				storeHealth.lastUpdate = await getLastUpdateTime(db, storeName);

				if (storeHealth.lastUpdate) {
					const lastUpdateTime = new Date(storeHealth.lastUpdate).getTime();
					const ageMs = Date.now() - lastUpdateTime;
					storeHealth.ageInHours = Math.round(ageMs / (60 * 60 * 1000));
				}

				let score = 100;

				if (!storeHealth.isValid) {
					score -= 40;
					storeHealth.issues.push('캐시가 만료되었습니다');
				}

				if (storeHealth.ageInHours > 24) {
					const agePenalty = Math.min(30, Math.floor(storeHealth.ageInHours / 24) * 5);
					score -= agePenalty;
					storeHealth.issues.push(`캐시가 ${storeHealth.ageInHours}시간 전에 업데이트되었습니다`);
				}

				if (storeHealth.itemCount === 0) {
					score -= 20;
					storeHealth.issues.push('데이터가 없습니다');
				} else if (storeHealth.itemCount > 10000) {
					score -= 10;
					storeHealth.issues.push('데이터가 너무 많습니다 (정리 권장)');
				}

				if (performanceTest) {
					const readStartTime = Date.now();
					await getAllData(db, storeName);
					const readTime = Date.now() - readStartTime;

					if (readTime > 1000) {
						score -= 10;
						storeHealth.issues.push('읽기 성능이 느립니다');
					}
				}

				storeHealth.score = Math.max(0, score);
				totalScore += storeHealth.score;
			} catch (error) {
				storeHealth.score = 0;
				storeHealth.issues.push(
					`오류 발생: ${error instanceof Error ? error.message : String(error)}`
				);
				report.warnings.push(`${storeName} 스토어 분석 실패: ${error}`);
			}

			report.storeHealth.push(storeHealth);
		}

		report.overallScore = Math.round(totalScore / storeNames.length);

		if (report.overallScore >= 90) report.overallGrade = 'excellent';
		else if (report.overallScore >= 75) report.overallGrade = 'good';
		else if (report.overallScore >= 60) report.overallGrade = 'fair';
		else if (report.overallScore >= 40) report.overallGrade = 'poor';
		else report.overallGrade = 'critical';

		if (generateRecommendations) {
			if (report.storageInfo.usageRatio > 0.8) {
				report.recommendations.push('저장소 사용량이 높습니다. 캐시 정리를 권장합니다.');
			}

			const expiredStores = report.storeHealth.filter((store) => !store.isValid);
			if (expiredStores.length > 0) {
				report.recommendations.push(
					`${expiredStores.length}개 스토어의 캐시가 만료되었습니다. 새로고침을 권장합니다.`
				);
			}

			const oldStores = report.storeHealth.filter((store) => store.ageInHours > 48);
			if (oldStores.length > 0) {
				report.recommendations.push(
					`${oldStores.length}개 스토어의 데이터가 48시간 이상 오래되었습니다.`
				);
			}

			const emptyStores = report.storeHealth.filter((store) => store.itemCount === 0);
			if (emptyStores.length > 0) {
				report.recommendations.push(
					`${emptyStores.length}개 스토어가 비어있습니다. 데이터 동기화를 확인하세요.`
				);
			}

			if (report.overallScore < 60) {
				report.recommendations.push(
					'전체적인 캐시 성능이 낮습니다. 캐시 정리 및 최적화를 권장합니다.'
				);
			}
		}

		console.log(`캐시 상태 보고서 생성 완료: ${report.overallGrade} (${report.overallScore}점)`);
	} catch (error) {
		report.warnings.push(`보고서 생성 중 오류 발생: ${error}`);
		console.error('캐시 상태 보고서 생성 실패:', error);
	}

	return report;
}

export async function getCacheStatus(
	db: IDBDatabase,
	storeName: string,
	validityPeriod: number = 5 * 60 * 1000
): Promise<CacheStatus> {
	try {
		const [isValid, lastUpdate, cachedData] = await Promise.all([
			isCacheValid(db, storeName, validityPeriod),
			getLastUpdateTime(db, storeName),
			getAllData(db, storeName)
		]);

		return {
			isValid,
			lastUpdate,
			dataCount: cachedData.length,
			isOnline: navigator.onLine
		};
	} catch (error) {
		console.error(`캐시 상태 조회 실패 (${storeName}):`, error);
		return {
			isValid: false,
			lastUpdate: null,
			dataCount: 0,
			isOnline: navigator.onLine
		};
	}
}
