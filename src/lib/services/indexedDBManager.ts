import type { PrintSetting } from '$lib/types/indexedDB';

/**
 * IndexedDB 초기화
 *
 * @param dbName 데이터베이스 이름 (기본값: 'EmployeeNotificationDB')
 * @param version 데이터베이스 버전 (기본값: 1)
 * @returns Promise<IDBDatabase>
 */
export async function initializeIndexedDB(
	dbName: string = 'EmployeeNotificationDB',
	version: number = 1
): Promise<IDBDatabase> {
	return new Promise((resolve, reject) => {
		const request = indexedDB.open(dbName, version);

		request.onerror = () => {
			console.error('IndexedDB 열기 실패:', request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			const db = request.result;
			console.log('IndexedDB 초기화 완료:', dbName);
			resolve(db);
		};

		request.onupgradeneeded = (event) => {
			const db = (event.target as IDBOpenDBRequest).result;
			console.log('IndexedDB 업그레이드 시작:', dbName);

			try {
				createObjectStores(db);
				console.log('객체 스토어 생성 완료');
			} catch (error) {
				console.error('객체 스토어 생성 실패:', error);
				reject(error);
			}
		};
	});
}

/**
 * 객체 스토어 생성
 *
 * @param db IDBDatabase 인스턴스
 */
function createObjectStores(db: IDBDatabase): void {
	// 카테고리 스토어 (cate4, cate5 데이터)
	if (!db.objectStoreNames.contains('categories')) {
		const categoriesStore = db.createObjectStore('categories', { keyPath: 'key' });
		categoriesStore.createIndex('updated_at', 'updated_at', { unique: false });
		console.log('categories 스토어 생성 완료');
	}

	// 직원 스토어
	if (!db.objectStoreNames.contains('employees')) {
		const employeesStore = db.createObjectStore('employees', { keyPath: 'id' });
		employeesStore.createIndex('email', 'email', { unique: true });
		employeesStore.createIndex('group_id', 'group_id', { unique: false });
		console.log('employees 스토어 생성 완료');
	}

	// 그룹 스토어
	if (!db.objectStoreNames.contains('groups')) {
		const groupsStore = db.createObjectStore('groups', { keyPath: 'id' });
		groupsStore.createIndex('name', 'name', { unique: false });
		console.log('groups 스토어 생성 완료');
	}

	// 알림 스토어
	if (!db.objectStoreNames.contains('notifications')) {
		const notificationsStore = db.createObjectStore('notifications', { keyPath: 'id' });
		notificationsStore.createIndex('priority', 'priority', { unique: false });
		notificationsStore.createIndex('read', 'read', { unique: false });
		notificationsStore.createIndex('received_at', 'received_at', { unique: false });
		notificationsStore.createIndex('user_id', 'user_id', { unique: false });
		console.log('notifications 스토어 생성 완료');
	}

	// 설정 스토어
	if (!db.objectStoreNames.contains('settings')) {
		db.createObjectStore('settings', { keyPath: 'key' });
		console.log('settings 스토어 생성 완료');
	}

	// 인쇄 설정 스토어 (기존 printSetting DB 통합)
	if (!db.objectStoreNames.contains('print_settings')) {
		db.createObjectStore('print_settings', { keyPath: 'settingName' });
		console.log('print_settings 스토어 생성 완료');
	}

	// 수리 등급 스토어
	if (!db.objectStoreNames.contains('repair_grades')) {
		db.createObjectStore('repair_grades', { keyPath: 'id' });
		console.log('repair_grades 스토어 생성 완료');
	}

	// 오프라인 알림 스토어 (요구사항 1.4: 오프라인 중 알림 저장)
	if (!db.objectStoreNames.contains('offline_notifications')) {
		const offlineNotificationsStore = db.createObjectStore('offline_notifications', {
			keyPath: 'id'
		});
		offlineNotificationsStore.createIndex('stored_at', 'stored_at', { unique: false });
		offlineNotificationsStore.createIndex('priority', 'priority', { unique: false });
		console.log('offline_notifications 스토어 생성 완료');
	}

	// 업데이트 히스토리 스토어
	if (!db.objectStoreNames.contains('update_history')) {
		const historyStore = db.createObjectStore('update_history', { keyPath: 'id' });
		historyStore.createIndex('timestamp', 'timestamp', { unique: false });
		historyStore.createIndex('model', 'model', { unique: false });
		console.log('update_history 스토어 생성 완료');
	}

	// 삭제 히스토리 스토어 (데이터 복구용)
	if (!db.objectStoreNames.contains('deletion_history')) {
		const deletionHistoryStore = db.createObjectStore('deletion_history', { keyPath: 'id' });
		deletionHistoryStore.createIndex('deleted_at', 'deleted_at', { unique: false });
		deletionHistoryStore.createIndex('data_type', 'data_type', { unique: false });
		deletionHistoryStore.createIndex('item_count', 'item_count', { unique: false });
		console.log('deletion_history 스토어 생성 완료');
	}
}

/**
 * 데이터 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param key 조회할 키
 * @returns Promise<any>
 */
export async function getData(db: IDBDatabase, storeName: string, key: any): Promise<any> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.get(key);

		request.onerror = () => {
			console.error(`데이터 조회 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve(request.result);
		};
	});
}

/**
 * 모든 데이터 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @returns Promise<any[]>
 */
export async function getAllData(db: IDBDatabase, storeName: string): Promise<any[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.getAll();

		request.onerror = () => {
			console.error(`전체 데이터 조회 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve(request.result || []);
		};
	});
}
/**
 * 데이터 저장
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param data 저장할 데이터
 * @returns Promise<void>
 */
export async function putData(db: IDBDatabase, storeName: string, data: any): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);
		const request = store.put(data);

		request.onerror = () => {
			console.error(`데이터 저장 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve();
		};
	});
}

/**
 * 다중 데이터 저장
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param dataArray 저장할 데이터 배열
 * @returns Promise<void>
 */
export async function putManyData(
	db: IDBDatabase,
	storeName: string,
	dataArray: any[]
): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);

		let hasError = false;
		let errorToReject: any = null;

		transaction.onerror = () => {
			if (!hasError) {
				hasError = true;
				errorToReject = transaction.error;
				console.error(`배치 저장 실패 (${storeName}):`, transaction.error);
			}
		};

		transaction.oncomplete = () => {
			if (hasError && errorToReject) {
				reject(errorToReject);
			} else {
				resolve();
			}
		};

		for (const data of dataArray) {
			const request = store.put(data);

			request.onerror = () => {
				if (!hasError) {
					hasError = true;
					errorToReject = request.error;
					console.error(`배치 저장 중 개별 데이터 실패 (${storeName}):`, request.error);
				}
			};
		}
	});
}

/**
 * 데이터 삭제
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param key 삭제할 키
 * @returns Promise<void>
 */
export async function deleteData(db: IDBDatabase, storeName: string, key: any): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);
		const request = store.delete(key);

		request.onerror = () => {
			console.error(`데이터 삭제 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve();
		};
	});
}

/**
 * 스토어 전체 삭제
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @returns Promise<void>
 */
export async function clearStore(db: IDBDatabase, storeName: string): Promise<void> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readwrite');
		const store = transaction.objectStore(storeName);
		const request = store.clear();

		request.onerror = () => {
			console.error(`스토어 전체 삭제 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			console.log(`스토어 전체 삭제 완료 (${storeName})`);
			resolve();
		};
	});
}

/**
 * 기존 printSetting DB 마이그레이션
 *
 * @param targetDb 대상 데이터베이스
 * @returns Promise<void>
 */
export async function migratePrintSettingDB(targetDb: IDBDatabase): Promise<void> {
    const MIGRATION_FLAG = 'printSettingMigrated';
    if (localStorage.getItem(MIGRATION_FLAG) === 'true') {
        return;
    }

	try {
		console.log('printSetting DB 마이그레이션 시작...');

		// 기존 printSetting DB 열기
		const oldDb = await new Promise<IDBDatabase>((resolve, reject) => {
			const request = indexedDB.open('printSetting');

			request.onerror = () => {
				// 기존 DB가 없는 경우 (새 설치)
				reject(new Error('기존 printSetting DB가 존재하지 않음'));
			};

			request.onsuccess = () => {
				resolve(request.result);
			};
		});

		// object store 존재 여부 확인
		if (!oldDb.objectStoreNames.contains('settings')) {
			console.log('기존 printSetting DB에 settings 스토어가 없음 - 마이그레이션 건너뜀');
			oldDb.close();
            // 스토어가 없어도, DB자체는 있었을 수 있으므로 삭제 시도 및 플래그 설정
            await new Promise<void>((resolve) => {
                const deleteRequest = indexedDB.deleteDatabase('printSetting');
                deleteRequest.onsuccess = () => resolve();
                deleteRequest.onerror = () => resolve();
                deleteRequest.onblocked = () => resolve();
            });
            localStorage.setItem(MIGRATION_FLAG, 'true');
			return;
		}

		// 기존 데이터 조회
		const oldData = await getAllData(oldDb, 'settings');
		console.log(`마이그레이션할 데이터 ${oldData.length}개 발견`);

		// 새로운 스키마로 데이터 변환 및 저장
        if (oldData.length > 0) {
		    for (const item of oldData) {
			    const migratedData: PrintSetting = {
				    settingName: item.settingName,
				    settings: item.settings,
				    updatedAt: item.updatedAt,
				    migrated: true,
				    migratedAt: new Date().toISOString()
			    };

			    await putData(targetDb, 'print_settings', migratedData);
		    }
        }

		console.log('printSetting DB 마이그레이션 완료');
		oldDb.close();

		// 기존 DB 삭제
		await new Promise<void>((resolve) => {
			const deleteRequest = indexedDB.deleteDatabase('printSetting');

			deleteRequest.onerror = () => {
				console.warn('기존 printSetting DB 삭제 실패:', deleteRequest.error);
				resolve(); // 삭제 실패는 치명적이지 않음
			};

			deleteRequest.onsuccess = () => {
				console.log('기존 printSetting DB 삭제 완료');
				resolve();
			};
            deleteRequest.onblocked = () => {
                console.warn('기존 printSetting DB 삭제가 차단되었습니다.');
                resolve();
            }
		});

		localStorage.setItem(MIGRATION_FLAG, 'true');
		console.log('printSetting 마이그레이션 플래그 설정 완료.');
	} catch (error) {
		console.error('printSetting DB 마이그레이션 실패:', error);
		// 기존 DB가 없는 경우 등 에러 발생 시, 마이그레이션이 더 이상 필요 없다고 간주하고 플래그 설정
		localStorage.setItem(MIGRATION_FLAG, 'true');
	}
}

/**
 * 대용량 데이터 배치 처리 최적화
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param dataArray 저장할 데이터 배열
 * @param batchSize 배치 크기 (기본값: 100)
 * @returns Promise<void>
 */
export async function putManyDataOptimized(
	db: IDBDatabase,
	storeName: string,
	dataArray: any[],
	batchSize: number = 100
): Promise<void> {
	const batches = [];
	for (let i = 0; i < dataArray.length; i += batchSize) {
		batches.push(dataArray.slice(i, i + batchSize));
	}

	console.log(`${dataArray.length}개 데이터를 ${batches.length}개 배치로 처리`);

	for (let i = 0; i < batches.length; i++) {
		const batch = batches[i];
		await putManyData(db, storeName, batch);

		// 브라우저가 다른 작업을 처리할 수 있도록 양보
		if (i < batches.length - 1) {
			await new Promise((resolve) => setTimeout(resolve, 0));
		}

		console.log(`배치 ${i + 1}/${batches.length} 완료`);
	}
}

/**
 * 메모리 효율적인 대용량 데이터 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param pageSize 페이지 크기 (기본값: 1000)
 * @returns Promise<any[]>
 */
export async function getAllDataPaginated(
	db: IDBDatabase,
	storeName: string,
	pageSize: number = 1000
): Promise<any[]> {
	const results: any[] = [];
	let cursor: IDBCursorWithValue | null = null;
	let count = 0;

	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const request = store.openCursor();

		request.onsuccess = (event) => {
			cursor = (event.target as IDBRequest).result;

			if (cursor && count < pageSize) {
				results.push(cursor.value);
				count++;
				cursor.continue();
			} else {
				resolve(results);
			}
		};

		request.onerror = () => {
			console.error(`페이지네이션 조회 실패 (${storeName}):`, request.error);
			reject(request.error);
		};
	});
}

/**
 * 데이터베이스 복구
 *
 * @param dbName 데이터베이스 이름
 * @returns Promise<IDBDatabase>
 */
export async function repairDatabase(dbName: string): Promise<IDBDatabase> {
	try {
		console.log(`데이터베이스 복구 시작: ${dbName}`);

		// 기존 DB 삭제
		await new Promise<void>((resolve, reject) => {
			const deleteRequest = indexedDB.deleteDatabase(dbName);

			deleteRequest.onerror = () => reject(deleteRequest.error);
			deleteRequest.onsuccess = () => resolve();
		});

		// 새로운 DB 생성
		const newDb = await initializeIndexedDB(dbName, 1);
		console.log(`데이터베이스 복구 완료: ${dbName}`);

		return newDb;
	} catch (error) {
		console.error('데이터베이스 복구 실패:', error);
		throw new Error('데이터베이스를 복구할 수 없습니다.');
	}
}

/**
 * 트랜잭션 재시도 로직
 *
 * @param operation 실행할 작업
 * @param maxRetries 최대 재시도 횟수 (기본값: 3)
 * @param delay 재시도 간격 (기본값: 1000ms)
 * @returns Promise<T>
 */
export async function executeWithRetry<T>(
	operation: () => Promise<T>,
	maxRetries: number = 3,
	delay: number = 1000
): Promise<T> {
	for (let attempt = 1; attempt <= maxRetries; attempt++) {
		try {
			return await operation();
		} catch (error) {
			if (attempt === maxRetries) {
				throw error;
			}

			console.warn(`작업 실패 (시도 ${attempt}/${maxRetries}):`, error);
			await new Promise((resolve) => setTimeout(resolve, delay * attempt));
		}
	}

	throw new Error('최대 재시도 횟수 초과');
}

/**
 * 통합 데이터베이스 초기화 (마이그레이션 포함)
 *
 * @param dbName 데이터베이스 이름
 * @param version 데이터베이스 버전
 * @returns Promise<IDBDatabase>
 */
export async function initializeWithMigration(
	dbName: string = 'EmployeeNotificationDB',
	version: number = 1
): Promise<IDBDatabase> {
	try {
		// 새로운 통합 DB 초기화
		const db = await initializeIndexedDB(dbName, version);

		// 기존 printSetting DB 마이그레이션
		await migratePrintSettingDB(db);

		console.log('통합 데이터베이스 초기화 및 마이그레이션 완료');
		return db;
	} catch (error) {
		console.error('통합 데이터베이스 초기화 실패:', error);
		throw error;
	}
}
/**
 * 인덱스를 사용한 데이터 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param indexName 인덱스 이름
 * @param value 조회할 값
 * @returns Promise<any[]>
 */
export async function getDataByIndex(
	db: IDBDatabase,
	storeName: string,
	indexName: string,
	value: any
): Promise<any[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const index = store.index(indexName);
		const request = index.getAll(value);

		request.onerror = () => {
			console.error(`인덱스 조회 실패 (${storeName}.${indexName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve(request.result || []);
		};
	});
}

/**
 * 범위 조건으로 데이터 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param indexName 인덱스 이름 (선택사항)
 * @param range IDBKeyRange 객체
 * @returns Promise<any[]>
 */
export async function getDataByRange(
	db: IDBDatabase,
	storeName: string,
	range: IDBKeyRange,
	indexName?: string
): Promise<any[]> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const source = indexName ? store.index(indexName) : store;
		const request = source.getAll(range);

		request.onerror = () => {
			console.error(`범위 조회 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve(request.result || []);
		};
	});
}

/**
 * 데이터 개수 조회
 *
 * @param db IDBDatabase 인스턴스
 * @param storeName 스토어 이름
 * @param indexName 인덱스 이름 (선택사항)
 * @param query 조회 조건 (선택사항)
 * @returns Promise<number>
 */
export async function getDataCount(
	db: IDBDatabase,
	storeName: string,
	indexName?: string,
	query?: any
): Promise<number> {
	return new Promise((resolve, reject) => {
		const transaction = db.transaction([storeName], 'readonly');
		const store = transaction.objectStore(storeName);
		const source = indexName ? store.index(indexName) : store;
		const request = source.count(query);

		request.onerror = () => {
			console.error(`개수 조회 실패 (${storeName}):`, request.error);
			reject(request.error);
		};

		request.onsuccess = () => {
			resolve(request.result);
		};
	});
}

/**
 * 데이터베이스 상태 확인
 *
 * @param dbName 데이터베이스 이름
 * @returns Promise<boolean>
 */
export async function checkDatabaseHealth(dbName: string): Promise<boolean> {
	try {
		const db = await initializeIndexedDB(dbName, 1);

		// 모든 필수 스토어가 존재하는지 확인
		const requiredStores = [
			'categories',
			'employees',
			'groups',
			'notifications',
			'settings',
			'print_settings',
			'repair_grades',
			'update_history',
			'deletion_history'
		];

		for (const storeName of requiredStores) {
			if (!db.objectStoreNames.contains(storeName)) {
				console.error(`필수 스토어 누락: ${storeName}`);
				db.close();
				return false;
			}
		}

		// 간단한 읽기 테스트
		await getAllData(db, 'settings');

		db.close();
		return true;
	} catch (error) {
		console.error('데이터베이스 상태 확인 실패:', error);
		return false;
	}
}

/**
 * 데이터베이스 정보 조회
 *
 * @param dbName 데이터베이스 이름
 * @returns Promise<DatabaseInfo>
 */
export async function getDatabaseInfo(dbName: string): Promise<{
	name: string;
	version: number;
	stores: string[];
	size?: number;
}> {
	const db = await initializeIndexedDB(dbName, 1);

	const info = {
		name: db.name,
		version: db.version,
		stores: Array.from(db.objectStoreNames)
	};

	db.close();
	return info;
}
