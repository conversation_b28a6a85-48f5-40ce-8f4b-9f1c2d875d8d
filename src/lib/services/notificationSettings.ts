/**
 * 알림 설정 관리 서비스
 */

import { getData, putData } from './indexedDBManager';

/**
 * 근무 시간 설정
 */
export interface WorkingHours {
	start: string; // HH:MM 형식
	end: string; // HH:MM 형식
	enabled: boolean;
}

/**
 * 알림 설정 인터페이스
 */
export interface NotificationSettings {
	enableBrowserNotifications: boolean;
	enableSounds: boolean;
	enableVibration: boolean;
	workingHours: WorkingHours;
	soundFile: string;
	displayDuration: {
		low: number;
		normal: number;
		high: number;
		urgent: number;
	};
	disabledTypes: string[];
}

/**
 * 알림 설정 조회
 *
 * @param db IDBDatabase 객체
 * @returns Promise<NotificationSettings>
 */
export async function getNotificationSettings(db?: IDBDatabase): Promise<NotificationSettings> {
	if (!db) {
		return getDefaultNotificationSettings();
	}

	try {
		const settings = await getData(db, 'settings', 'notification_settings');
		return settings?.value || getDefaultNotificationSettings();
	} catch (error) {
		console.error('설정 로드 실패:', error);
		return getDefaultNotificationSettings();
	}
}

/**
 * 기본 알림 설정
 *
 * @returns NotificationSettings
 */
export function getDefaultNotificationSettings(): NotificationSettings {
	return {
		enableBrowserNotifications: true,
		enableSounds: true,
		enableVibration: true,
		workingHours: {
			start: '09:00',
			end: '18:00',
			enabled: false
		},
		soundFile: '/sounds/notification.mp3',
		displayDuration: {
			low: 3000,
			normal: 5000,
			high: 8000,
			urgent: 0 // 수동 닫기
		},
		disabledTypes: []
	};
}

/**
 * 알림 설정 저장
 *
 * @param db IDBDatabase 객체
 * @param settings 알림 설정
 * @returns Promise<void>
 */
export async function saveNotificationSettings(
	db: IDBDatabase,
	settings: NotificationSettings
): Promise<void> {
	try {
		await putData(db, 'settings', {
			key: 'notification_settings',
			value: settings,
			updated_at: new Date().toISOString()
		});
		console.log('알림 설정 저장 완료');
	} catch (error) {
		console.error('알림 설정 저장 실패:', error);
		throw error;
	}
}

/**
 * 근무 시간 확인
 *
 * @param workingHours 근무 시간 설정
 * @returns boolean
 */
export function isWorkingHours(workingHours: WorkingHours): boolean {
	if (!workingHours.enabled) {
		return true;
	}

	const now = new Date();
	const currentTime = now.getHours() * 60 + now.getMinutes();

	const [startHour, startMin] = workingHours.start.split(':').map(Number);
	const [endHour, endMin] = workingHours.end.split(':').map(Number);

	const startTime = startHour * 60 + startMin;
	const endTime = endHour * 60 + endMin;

	return currentTime >= startTime && currentTime <= endTime;
}

/**
 * 알림 타입 활성화 여부 확인
 *
 * @param type 알림 타입
 * @param settings 알림 설정
 * @returns boolean
 */
export function isNotificationTypeEnabled(type: string, settings: NotificationSettings): boolean {
	return !settings.disabledTypes.includes(type);
}
