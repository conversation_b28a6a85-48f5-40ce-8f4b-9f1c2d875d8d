# SSE 연결 관리 서비스

이 디렉토리는 Server-Sent Events(SSE)를 통한 실시간 통신 기능을 제공합니다.

## 파일 구조

```
src/lib/services/
├── sseConnection.ts          # 메인 SSE 연결 관리
├── sseConnectionState.ts     # 연결 상태 관리
├── sseStats.ts              # 연결 통계 관리
├── sseMessageRouter.ts       # 메시지 라우팅 시스템
├── sseDataHandlers.ts        # 데이터 업데이트 핸들러
├── sseDataSync.ts           # 데이터 동기화
├── index.ts                 # 통합 인덱스
└── __tests__/
    ├── sseConnection.test.ts        # 연결 테스트
    ├── sseMessageRouter.test.ts     # 라우팅 테스트
    ├── sseConnectionState.test.ts   # 상태 관리 테스트
    └── sseDataSync.test.ts         # 동기화 테스트
```

## 주요 기능

### 1. SSE 연결 초기화 (`sseConnection.ts`)

- **기본 연결**: `createDefaultSseConnection()`
- **알림 전용 연결**: `createNotificationSseConnection()`
- **커스텀 연결**: `initializeSseConnection(url, options)`
- **연결 종료**: `closeSseConnection(eventSource)`
- **하트비트 모니터링**: 자동 연결 건강성 확인

### 2. 메시지 라우팅 (`sseMessageRouter.ts`)

- **메시지 타입별 라우팅**: notification, data_update, heartbeat, connection, system
- **메시지 유효성 검증**: 타입별 스키마 검증
- **핸들러 레지스트리**: 동적 핸들러 등록/해제
- **브라우저 알림**: 우선순위별 알림 표시

### 3. 데이터 핸들러 (`sseDataHandlers.ts`)

- **스토어 연동**: 각 데이터 모델별 스토어 등록
- **CRUD 작업**: 생성, 수정, 삭제, 배치 업데이트
- **모델별 처리**: categories, employees, groups, repair_grades

### 4. 연결 상태 관리 (`sseConnectionState.ts`)

- **상태 추적**: disconnected, connecting, connected, reconnecting, error, closed
- **네트워크 모니터링**: 온라인/오프라인 상태 감지
- **페이지 가시성**: 탭 전환 시 연결 상태 확인
- **건강성 검사**: 고급 연결 품질 분석
- **지연시간 측정**: 실시간 네트워크 지연시간 추적
- **하트비트 모니터링**: 연결 활성 상태 확인

### 5. 데이터 동기화 (`sseDataSync.ts`)

- **누락 데이터 복구**: 오프라인 중 누락된 데이터 동기화
- **전체/증분 동기화**: 첫 연결 시 전체, 이후 증분 동기화
- **재시도 로직**: 동기화 실패 시 자동 재시도
- **오류 처리**: 네트워크 오류 및 HTTP 오류 처리

### 6. 연결 통계 (`sseStats.ts`)

- 연결 횟수, 메시지 수, 오류 수 추적
- 평균 지연시간 계산
- 연결 지속시간 측정
- 분당 메시지 수 계산

## 사용 방법

### 간단한 시작

```typescript
import { startSseSystem } from '$lib/services';

// 전체 SSE 시스템 시작
const eventSource = startSseSystem();
```

### 기본 사용법

```typescript
import { createDefaultSseConnection, closeSseConnection } from '$lib/services/sseConnection';
import { sseConnectionState, isConnected } from '$lib/services/sseConnectionState';
import { messageHandlerRegistry } from '$lib/services/sseMessageRouter';

// 연결 생성
const eventSource = createDefaultSseConnection();

// 메시지 핸들러 등록
messageHandlerRegistry.register('notification', (notification) => {
	console.log('알림 수신:', notification);
});

// 연결 상태 구독
sseConnectionState.subscribe((state) => {
	console.log('연결 상태:', state.status);
});

// 연결 종료
closeSseConnection(eventSource);
```

### 고급 연결 상태 관리

```typescript
import {
	performAdvancedHealthCheck,
	startAllMonitoring,
	getDebugInfo,
	connectionQuality,
	currentLatency
} from '$lib/services/sseConnectionState';

// 모든 모니터링 시작
startAllMonitoring();

// 연결 건강성 검사
const healthCheck = await performAdvancedHealthCheck();
console.log('연결 상태:', healthCheck.isHealthy);
console.log('문제점:', healthCheck.issues);
console.log('권장사항:', healthCheck.recommendations);

// 연결 품질 구독
connectionQuality.subscribe((quality) => {
	console.log('연결 품질:', quality); // excellent, good, poor, unknown
});

// 지연시간 구독
currentLatency.subscribe((latency) => {
	console.log('현재 지연시간:', latency, 'ms');
});

// 디버그 정보
const debugInfo = getDebugInfo();
console.log('디버그 정보:', debugInfo);
```

### 데이터 동기화

```typescript
import {
	requestMissedData,
	retrySyncIfNeeded,
	triggerManualSync,
	getSyncState
} from '$lib/services/sseDataSync';

// 누락 데이터 동기화
await requestMissedData();

// 동기화 재시도
await retrySyncIfNeeded();

// 수동 동기화
await triggerManualSync();

// 동기화 상태 확인
const syncState = getSyncState();
console.log('동기화 상태:', syncState);
```

### 메시지 라우팅

```typescript
import { messageHandlerRegistry, type NotificationMessage } from '$lib/services/sseMessageRouter';

// 알림 핸들러 등록
messageHandlerRegistry.register('notification', (notification: NotificationMessage) => {
	// 알림 처리 로직
	showNotification(notification);
});

// 데이터 업데이트 핸들러 등록
messageHandlerRegistry.register('data_update', (update) => {
	// 데이터 업데이트 처리
	updateLocalData(update);
});
```

### 데이터 스토어 연동

```typescript
import { storeRegistry, initializeDataHandlers } from '$lib/services/sseDataHandlers';

// 데이터 핸들러 초기화
initializeDataHandlers();

// 카테고리 스토어 등록
const categoriesStore = {
	addItem: (item) => {
		/* 추가 로직 */
	},
	updateItem: (id, item) => {
		/* 수정 로직 */
	},
	removeItem: (id) => {
		/* 삭제 로직 */
	},
	batchUpdate: (items) => {
		/* 배치 업데이트 로직 */
	}
};

storeRegistry.register('categories', categoriesStore);
```

## 메시지 타입

### 알림 메시지

```typescript
interface NotificationMessage {
	id: number;
	title: string;
	message: string;
	type: string;
	priority: 'low' | 'normal' | 'high' | 'urgent';
	target_type: 'all' | 'group' | 'individual';
	target_ids?: number[];
	action_url?: string;
	action_text?: string;
	expires_at?: string;
}
```

### 데이터 업데이트 메시지

```typescript
interface DataUpdateMessage {
	model: 'categories' | 'employees' | 'groups' | 'repair_grades';
	action: 'created' | 'updated' | 'deleted' | 'batch_updated';
	payload: any;
	affected_ids: number[];
	timestamp: string;
}
```

## 환경 변수

`.env` 파일에서 다음 변수들을 설정할 수 있습니다:

```env
VITE_SSE_ENDPOINT=http://localhost:8000/api/sse/stream
VITE_SSE_NOTIFICATION_ENDPOINT=http://localhost:8000/api/sse/notifications
```

## 연결 상태 모니터링

### 자동 모니터링

- **네트워크 상태**: 온라인/오프라인 감지
- **페이지 가시성**: 탭 전환 시 연결 확인
- **연결 품질**: 30초마다 자동 품질 검사
- **하트비트**: 연결 활성 상태 확인

### 수동 검사

```typescript
// 건강성 검사
const result = await performAdvancedHealthCheck();

// 디버그 정보
const debug = getDebugInfo();
```

## 브라우저 알림

- 자동 권한 요청
- 우선순위별 알림 표시
- 사운드 재생 (설정 가능)
- 클릭 액션 지원

## 테스트

```bash
# 연결 테스트
npm run test src/lib/services/__tests__/sseConnection.test.ts

# 메시지 라우팅 테스트
npm run test src/lib/services/__tests__/sseMessageRouter.test.ts

# 연결 상태 관리 테스트
npm run test src/lib/services/__tests__/sseConnectionState.test.ts

# 데이터 동기화 테스트
npm run test src/lib/services/__tests__/sseDataSync.test.ts

# 모든 SSE 테스트
npm run test src/lib/services/__tests__/
```

## 데모

`SseConnectionDemo.svelte` 컴포넌트를 사용하여 SSE 연결 기능을 테스트할 수 있습니다:

```svelte
<script>
	import SseConnectionDemo from '$lib/components/SseConnectionDemo.svelte';
</script>

<SseConnectionDemo />
```

## 초기화 순서

```typescript
import { startSseSystem } from '$lib/services';

// 전체 시스템 시작 (권장)
const eventSource = startSseSystem();

// 또는 개별 초기화
import {
	startAllMonitoring,
	initializeDataHandlers,
	initializeNotificationStore
} from '$lib/services';

startAllMonitoring(); // 모든 모니터링 시작
initializeDataHandlers(); // 데이터 핸들러 초기화
initializeNotificationStore(); // 알림 스토어 초기화
```

## 이벤트

SSE 시스템은 다음 커스텀 이벤트를 발생시킵니다:

- `sse-connection-status-change`: 연결 상태 변경
- `sse-reconnection-needed`: 재연결 필요
- `sse-connection-quality-issue`: 연결 품질 문제

```typescript
// 이벤트 리스너 등록
window.addEventListener('sse-connection-status-change', (event) => {
	console.log('연결 상태 변경:', event.detail);
});
```
