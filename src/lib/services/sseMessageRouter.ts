/**
 * SSE 메시지 라우팅 시스템
 *
 * 수신된 SSE 메시지를 타입별로 분류하고 적절한 핸들러로 라우팅합니다.
 */

import type {
	SseMessage,
	NotificationMessage,
	DataUpdateMessage,
	MessageHandler
} from '$lib/types/sseTypes';
import {
	recordMessage,
	recordError,
	startMessageProcessingTimer,
	endMessageProcessingTimer
} from '$lib/services/sseStats';
import { updateConnectionStatus } from '$lib/services/sseConnectionState';
import { addMessageToBatch } from '$lib/services/sseMessageBatcher';
import { addMessageToHistory } from '$lib/services/sseMessageHistory';
import { getCurrentUserId } from '$lib/utils/authUtils';

/**
 * 메시지 핸들러 레지스트리 상태 관리
 */
export const messageHandlerRegistry: Map<string, MessageHandler[]> = new Map();

/**
 * 핸들러 등록
 */
export function registerMessageHandler<T>(messageType: string, handler: MessageHandler<T>): void {
	if (!messageHandlerRegistry.has(messageType)) {
		messageHandlerRegistry.set(messageType, []);
	}
	messageHandlerRegistry.get(messageType)!.push(handler);
}

/**
 * 핸들러 제거
 */
export function unregisterMessageHandler<T>(messageType: string, handler: MessageHandler<T>): void {
	const messageHandlers = messageHandlerRegistry.get(messageType);
	if (messageHandlers) {
		const index = messageHandlers.indexOf(handler);
		if (index > -1) {
			messageHandlers.splice(index, 1);
		}
	}
}

/**
 * 메시지 처리
 */
function handleMessage(messageType: string, data: any): void {
	const messageHandlers = messageHandlerRegistry.get(messageType);
	if (messageHandlers) {
		messageHandlers.forEach((handler) => {
			try {
				handler(data);
			} catch (error) {
				console.error(`메시지 핸들러 오류 (${messageType}):`, error);
			}
		});
	}
}

/**
 * 등록된 핸들러 수 확인
 */
export function getHandlerCount(messageType: string): number {
	return messageHandlerRegistry.get(messageType)?.length || 0;
}

/**
 * 메시지 라우팅 및 처리
 */
export function handleSseMessage(event: MessageEvent): void {
	const processingStart = startMessageProcessingTimer();

	try {
		const message: SseMessage = JSON.parse(event.data);

		// 메시지 유효성 검증
		if (!validateMessage(message)) {
			console.warn('유효하지 않은 SSE 메시지:', message);
			recordError('invalid_message');
			return;
		}

		// 히스토리에 메시지 추가
		addMessageToHistory(message);

		// 메시지 수신 통계 업데이트
		recordMessage(message.type, event.data.length);

		// 배치 처리 시스템을 통한 메시지 처리
		if (shouldUseBatchProcessing(message)) {
			addMessageToBatch(message);
		} else {
			// 즉시 처리가 필요한 메시지는 직접 라우팅
			routeMessage(message);
		}
	} catch (error) {
		console.error('SSE 메시지 파싱 오류:', error);
		recordError('message_processing');
	} finally {
		endMessageProcessingTimer(processingStart);
	}
}

/**
 * 배치 처리 사용 여부 결정
 */
function shouldUseBatchProcessing(message: SseMessage): boolean {
	// 긴급하지 않은 메시지들은 배치 처리
	switch (message.type) {
		case 'notification': {
			// 긴급 알림은 즉시 처리, 나머지는 배치 처리
			const priority = (message.data as any).priority;
			return priority !== 'urgent';
		}
		case 'data_update':
			return true; // 데이터 업데이트는 배치 처리로 중복 제거
		case 'system':
			return false; // 시스템 메시지는 즉시 처리
		case 'heartbeat':
			return false; // 하트비트는 즉시 처리
		case 'connection_established':
			return false; // 연결 성립 메시지는 즉시 처리
		default:
			return true; // 기본적으로 배치 처리
	}
}
/**
 * 메시지 타입별 라우팅
 *
 * 메시지 타입별 핸들러 분기 처리 (notification, data_update, heartbeat, connection)
 * JSON 파싱 및 에러 처리 포함
 */
export function routeMessage(message: SseMessage): void {
	try {
		// 메시지 유효성 검증
		if (!validateMessage(message)) {
			console.warn('유효하지 않은 메시지:', message);
			recordError('invalid_message_format', `Invalid message: ${JSON.stringify(message)}`);
			return;
		}

		// 메시지 타입별 분기 처리
		switch (message.type) {
			case 'notification':
				try {
					handleNotificationMessage(message.data as NotificationMessage);
					console.log('알림 메시지 처리 완료:', message.data);
				} catch (error) {
					console.error('알림 메시지 처리 오류:', error);
					recordError('notification_handler_error', String(error));
				}
				break;

			case 'data_update':
				try {
					handleDataUpdateMessage(message.data as DataUpdateMessage);
					console.log('데이터 업데이트 메시지 처리 완료:', message.data);
				} catch (error) {
					console.error('데이터 업데이트 메시지 처리 오류:', error);
					recordError('data_update_handler_error', String(error));
				}
				break;

			case 'heartbeat':
				try {
					handleHeartbeatMessage(message.data);
					// 서버 하트비트 수신: 연결이 살아있음을 표시
					updateConnectionStatus('connected');
					console.log('하트비트 메시지 처리 완료');
				} catch (error) {
					console.error('하트비트 메시지 처리 오류:', error);
					recordError('heartbeat_handler_error', String(error));
				}
				break;

			case 'connection':
				try {
					handleConnectionMessage(message.data);
					console.log('연결 메시지 처리 완료:', message.data);
				} catch (error) {
					console.error('연결 메시지 처리 오류:', error);
					recordError('connection_handler_error', String(error));
				}
				break;

			case 'system':
				try {
					handleSystemMessage(message.data);
					console.log('시스템 메시지 처리 완료:', message.data);
				} catch (error) {
					console.error('시스템 메시지 처리 오류:', error);
					recordError('system_handler_error', String(error));
				}
				break;

			case 'connection_established':
				try {
					updateConnectionStatus('connected');
					console.log('연결 성립 메시지 처리 완료');
				} catch (error) {
					console.error('연결 성립 메시지 처리 오류:', error);
					recordError('connection_established_error', String(error));
				}
				break;

			default:
				console.warn('알 수 없는 메시지 타입:', message.type, message);
				recordError('unknown_message_type', `Unknown message type: ${message.type}`);

				// 알 수 없는 타입이라도 등록된 핸들러가 있다면 처리 시도
				if (messageHandlerRegistry.has(message.type)) {
					console.log(`등록된 커스텀 핸들러로 처리 시도: ${message.type}`);
					handleMessage(message.type, message.data);
				}
				break;
		}

		// 등록된 핸들러들에게도 메시지 전달
		try {
			handleMessage(message.type, message.data);
		} catch (error) {
			console.error('등록된 핸들러 실행 오류:', error);
			recordError('registered_handler_error', String(error));
		}
	} catch (error) {
		console.error('메시지 라우팅 중 예상치 못한 오류:', error);
		recordError('message_routing_error', String(error));
	}
}

/**
 * 알림 메시지 처리
 */
function handleNotificationMessage(notification: NotificationMessage): void {
	console.log('알림 수신:', notification);

	// 대상 확인 (현재 사용자가 대상인지)
	if (!isTargetedToCurrentUser(notification)) {
		return;
	}

	// 등록된 핸들러들에게 알림 전달 (알림 스토어 등)
	handleMessage('notification', notification);

	// 브라우저 알림 표시 (권한이 있는 경우)
	if (notification.priority === 'urgent' || notification.priority === 'high') {
		showBrowserNotification(notification);
	}

	// 사운드 재생 (설정에 따라)
	if (shouldPlayNotificationSound(notification)) {
		playNotificationSound(notification.priority);
	}
}

/**
 * 데이터 업데이트 메시지 처리
 */
export function handleDataUpdateMessage(update: DataUpdateMessage): void {
	console.log('데이터 업데이트 수신:', update);

	// 등록된 핸들러들에게 메시지 전달
	handleMessage('data_update', update);
}

/**
 * 하트비트 메시지 처리
 */
function handleHeartbeatMessage(data: any): void {
	console.log('하트비트 메시지 수신:', data);

	// 서버에서 타임스탬프를 보내주는 경우, 지연시간 계산
	if (data && data.timestamp) {
		try {
			const serverTimestamp = new Date(data.timestamp).getTime();
			const currentTimestamp = Date.now();
			const latency = currentTimestamp - serverTimestamp;

			console.log(`하트비트 지연시간: ${latency}ms`);

			// 지연시간이 너무 큰 경우 경고
			if (latency > 5000) {
				console.warn(`높은 네트워크 지연시간 감지: ${latency}ms`);
				recordError('high_latency', `High latency detected: ${latency}ms`);
			}
		} catch (error) {
			console.error('하트비트 타임스탬프 처리 오류:', error);
		}
	}

	// 연결 상태를 활성으로 업데이트
	updateConnectionStatus('connected');
}

/**
 * 연결 메시지 처리
 */
function handleConnectionMessage(data: any): void {
	console.log('연결 메시지:', data);

	if (data.status === 'authenticated') {
		updateConnectionStatus('connected');
		console.log('인증 성공');
	} else if (data.status === 'unauthenticated') {
		updateConnectionStatus('error', '인증 실패');
		console.warn('인증 실패 - 토큰 갱신 시도');
		// 토큰 갱신 시도
		refreshAuthToken();
	} else if (data.status === 'reconnected') {
		updateConnectionStatus('connected');
		console.log('재연결 성공');
	} else if (data.status === 'disconnecting') {
		updateConnectionStatus('disconnected');
		console.log('서버에서 연결 종료 예정');
	} else {
		console.log('알 수 없는 연결 상태:', data.status);
	}
}

/**
 * 시스템 메시지 처리
 */
function handleSystemMessage(data: any): void {
	console.log('시스템 메시지:', data);

	switch (data.type) {
		case 'maintenance':
			showMaintenanceNotification(data);
			break;
		case 'version_update':
			showVersionUpdateNotification(data);
			break;
		case 'server_restart':
			prepareForServerRestart(data);
			break;
		default:
			console.log('알 수 없는 시스템 메시지:', data.type);
	}
}

/**
 * 현재 사용자가 알림 대상인지 확인
 */
function isTargetedToCurrentUser(notification: NotificationMessage): boolean {
	const currentUserId = getCurrentUserId();

	if (!currentUserId) {
		return false;
	}

	switch (notification.target_type) {
		case 'all':
			return true;
		case 'individual':
			return notification.target_ids?.includes(currentUserId) ?? false;
		case 'group':
			// 현재 사용자의 그룹 확인 필요
			return isUserInTargetGroups(currentUserId, notification.target_ids || []);
		default:
			return false;
	}
}

/**
 * 사용자가 대상 그룹에 속하는지 확인
 */
function isUserInTargetGroups(userId: number, groupIds: number[]): boolean {
	// 실제 구현에서는 사용자의 그룹 정보를 확인해야 함
	// 현재는 기본적으로 false 반환
	const userGroups = getUserGroups(userId);
	return groupIds.some((groupId) => userGroups.includes(groupId));
}

/**
 * 사용자 그룹 정보 가져오기
 */
function getUserGroups(userId: number): number[] {
	// localStorage 또는 스토어에서 사용자 그룹 정보 가져오기
	const userStr = localStorage.getItem('current_user');
	if (userStr) {
		try {
			const user = JSON.parse(userStr);
			// 전달된 userId가 현재 로그인된 사용자의 ID와 일치하는지 확인
			if (user.id === userId) {
				return user.groups || [];
			} else {
				// userId가 일치하지 않는 경우 경고를 로깅하고 빈 배열 반환
				console.warn(
					`getUserGroups: 요청된 사용자 ID(${userId})가 현재 localStorage의 사용자 ID(${user.id})와 일치하지 않습니다.`
				);
				return [];
			}
		} catch (error) {
			console.error('사용자 그룹 정보 파싱 오류:', error);
		}
	}
	// 사용자 정보가 없거나 파싱 오류 발생 시 빈 배열 반환
	return [];
}

/**
 * 메시지 유효성 검증
 *
 * JSON 파싱 및 에러 처리를 포함한 강력한 메시지 검증
 */
export function validateMessage(message: any): message is SseMessage {
	try {
		// 기본 구조 검증
		if (!message || typeof message !== 'object') {
			console.warn('메시지가 객체가 아닙니다:', typeof message);
			return false;
		}

		// 필수 필드 확인
		if (!message.type || typeof message.type !== 'string') {
			console.warn('메시지 타입이 없거나 문자열이 아닙니다:', message.type);
			return false;
		}

		// 타임스탬프 검증 (선택적이지만 있다면 유효해야 함)
		if (message.timestamp && !isValidTimestamp(message.timestamp)) {
			console.warn('유효하지 않은 타임스탬프:', message.timestamp);
			return false;
		}

		// 타입별 추가 검증
		switch (message.type) {
			case 'notification':
				return validateNotificationMessage(message.data);
			case 'data_update':
				return validateDataUpdateMessage(message.data);
			case 'heartbeat':
				return validateHeartbeatMessage(message.data);
			case 'connection':
				return validateConnectionMessage(message.data);
			case 'system':
				return validateSystemMessage(message.data);
			default:
				// 알 수 없는 타입이라도 기본 구조가 맞다면 유효로 처리
				console.log('알 수 없는 메시지 타입이지만 기본 구조는 유효:', message.type);
				return true;
		}
	} catch (error) {
		console.error('메시지 유효성 검증 중 오류:', error);
		return false;
	}
}

/**
 * 타임스탬프 유효성 검증
 */
function isValidTimestamp(timestamp: any): boolean {
	if (typeof timestamp === 'string') {
		const date = new Date(timestamp);
		return !isNaN(date.getTime());
	}
	if (typeof timestamp === 'number') {
		return timestamp > 0 && timestamp <= Date.now() + 60000; // 미래 1분까지 허용
	}
	return false;
}

/**
 * 하트비트 메시지 유효성 검증
 */
function validateHeartbeatMessage(data: any): boolean {
	// 하트비트는 데이터가 없어도 유효
	if (!data) {
		return true;
	}

	// 타임스탬프가 있다면 유효성 검증
	return !(data.timestamp && !isValidTimestamp(data.timestamp));
}

/**
 * 연결 메시지 유효성 검증
 */
function validateConnectionMessage(data: any): boolean {
	if (!data || typeof data !== 'object') {
		return false;
	}

	// status 필드가 있다면 유효한 값인지 확인
	if (data.status) {
		const validStatuses = ['authenticated', 'unauthenticated', 'reconnected', 'disconnecting'];
		return validStatuses.includes(data.status);
	}

	return true;
}

/**
 * 시스템 메시지 유효성 검증
 */
function validateSystemMessage(data: any): boolean {
	if (!data || typeof data !== 'object') {
		return false;
	}

	// type 필드가 있다면 유효한 값인지 확인
	if (data.type) {
		const validTypes = ['maintenance', 'version_update', 'server_restart'];
		return validTypes.includes(data.type);
	}

	return true;
}

/**
 * 알림 메시지 유효성 검증
 */
function validateNotificationMessage(data: any): boolean {
	try {
		if (!data || typeof data !== 'object') {
			console.warn('알림 데이터가 객체가 아닙니다');
			return false;
		}

		// 필수 필드 검증
		if (typeof data.id !== 'number' || data.id <= 0) {
			console.warn('유효하지 않은 알림 ID:', data.id);
			return false;
		}

		if (typeof data.title !== 'string' || data.title.trim().length === 0) {
			console.warn('유효하지 않은 알림 제목:', data.title);
			return false;
		}

		if (typeof data.message !== 'string' || data.message.trim().length === 0) {
			console.warn('유효하지 않은 알림 메시지:', data.message);
			return false;
		}

		// 우선순위 검증
		const validPriorities = ['low', 'normal', 'high', 'urgent'];
		if (!validPriorities.includes(data.priority)) {
			console.warn('유효하지 않은 알림 우선순위:', data.priority);
			return false;
		}

		// 대상 타입 검증
		const validTargetTypes = ['all', 'group', 'individual'];
		if (!validTargetTypes.includes(data.target_type)) {
			console.warn('유효하지 않은 대상 타입:', data.target_type);
			return false;
		}

		// 대상 ID 검증 (group, individual인 경우)
		if (['group', 'individual'].includes(data.target_type)) {
			if (!Array.isArray(data.target_ids) || data.target_ids.length === 0) {
				console.warn('대상 ID가 없거나 배열이 아닙니다:', data.target_ids);
				return false;
			}

			// 모든 ID가 숫자인지 확인
			if (!data.target_ids.every((id: any) => typeof id === 'number' && id > 0)) {
				console.warn('유효하지 않은 대상 ID들:', data.target_ids);
				return false;
			}
		}

		return true;
	} catch (error) {
		console.error('알림 메시지 검증 중 오류:', error);
		return false;
	}
}

/**
 * 데이터 업데이트 메시지 유효성 검증
 */
function validateDataUpdateMessage(data: any): boolean {
	try {
		if (!data || typeof data !== 'object') {
			console.warn('데이터 업데이트가 객체가 아닙니다');
			return false;
		}

		// 모델 타입 검증
		const validModels = ['categories', 'employees', 'groups', 'repair_grades'];
		if (!validModels.includes(data.model)) {
			console.warn('유효하지 않은 모델 타입:', data.model);
			return false;
		}

		// 액션 타입 검증
		const validActions = ['created', 'updated', 'deleted', 'batch_updated'];
		if (!validActions.includes(data.action)) {
			console.warn('유효하지 않은 액션 타입:', data.action);
			return false;
		}

		// 영향받은 ID 검증
		if (!Array.isArray(data.affected_ids)) {
			console.warn('affected_ids가 배열이 아닙니다:', data.affected_ids);
			return false;
		}

		// ID가 모두 숫자인지 확인
		if (!data.affected_ids.every((id: any) => typeof id === 'number' && id > 0)) {
			console.warn('유효하지 않은 affected_ids:', data.affected_ids);
			return false;
		}

		// payload 검증 (created, updated, batch_updated인 경우)
		if (['created', 'updated', 'batch_updated'].includes(data.action)) {
			if (!data.payload) {
				console.warn('payload가 없습니다:', data.action);
				return false;
			}
		}

		return true;
	} catch (error) {
		console.error('데이터 업데이트 메시지 검증 중 오류:', error);
		return false;
	}
}

/**
 * 브라우저 알림 표시
 */
function showBrowserNotification(notification: NotificationMessage): void {
	if ('Notification' in window && Notification.permission === 'granted') {
		const browserNotification = new Notification(notification.title, {
			body: notification.message,
			icon: '/favicon.ico',
			tag: `notification-${notification.id}`,
			requireInteraction: notification.priority === 'urgent'
		});

		// 클릭 시 액션 URL로 이동
		if (notification.action_url) {
			browserNotification.onclick = () => {
				window.open(notification.action_url, '_blank');
				browserNotification.close();
			};
		}

		// 자동 닫기 (긴급하지 않은 경우)
		if (notification.priority !== 'urgent') {
			setTimeout(() => {
				browserNotification.close();
			}, 5000);
		}
	}
}

/**
 * 알림 사운드 재생 여부 확인
 */
function shouldPlayNotificationSound(notification: NotificationMessage): boolean {
	// 사용자 설정에서 사운드 재생 여부 확인
	const soundEnabled = localStorage.getItem('notification_sound_enabled') !== 'false';
	return soundEnabled && ['high', 'urgent'].includes(notification.priority);
}

/**
 * 알림 사운드 재생
 */
function playNotificationSound(priority: string): void {
	try {
		const audio = new Audio();

		switch (priority) {
			case 'urgent':
				audio.src = '/sounds/urgent-notification.mp3';
				break;
			case 'high':
				audio.src = '/sounds/high-notification.mp3';
				break;
			default:
				audio.src = '/sounds/default-notification.mp3';
		}

		audio.volume = 0.5;
		audio.play().catch((error) => {
			console.warn('알림 사운드 재생 실패:', error);
		});
	} catch (error) {
		console.warn('알림 사운드 재생 오류:', error);
	}
}

/**
 * 인증 토큰 갱신
 */
function refreshAuthToken(): void {
	// 토큰 갱신 로직 구현
	console.log('인증 토큰 갱신 시도');
	// 실제 구현에서는 API 호출을 통해 토큰 갱신
}

/**
 * 유지보수 알림 표시
 */
function showMaintenanceNotification(data: any): void {
	console.log('유지보수 알림:', data);
	// 유지보수 모달 또는 배너 표시
}

/**
 * 버전 업데이트 알림 표시
 */
function showVersionUpdateNotification(data: any): void {
	console.log('버전 업데이트 알림:', data);
	// 버전 업데이트 알림 표시
}

/**
 * 서버 재시작 준비
 */
function prepareForServerRestart(data: any): void {
	console.log('서버 재시작 준비:', data);
	// 사용자에게 알림 표시 및 자동 저장 등 준비 작업
}

/**
 * 커스텀 이벤트 핸들러들
 */

/**
 * 알림 이벤트 핸들러
 */
export function handleNotificationEvent(event: MessageEvent): void {
	try {
		const notification = JSON.parse(event.data);
		handleNotificationMessage(notification);
	} catch (error) {
		console.error('알림 이벤트 처리 오류:', error);
		recordError('notification_event_error', String(error));
	}
}

/**
 * 데이터 업데이트 이벤트 핸들러
 */
export function handleDataUpdateEvent(event: MessageEvent): void {
	try {
		const update = JSON.parse(event.data);
		handleDataUpdateMessage(update);
	} catch (error) {
		console.error('데이터 업데이트 이벤트 처리 오류:', error);
		recordError('data_update_event_error', String(error));
	}
}
