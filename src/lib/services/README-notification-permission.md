# 브라우저 알림 권한 관리 시스템

이 시스템은 브라우저 알림 권한을 관리하고, 권한이 없을 때 대체 알림 방식을 제공합니다.

## 주요 기능

- 브라우저 알림 권한 요청 및 상태 확인
- 권한 거부 시 대체 알림 방식 제공
- 사용자 친화적인 권한 안내 메시지
- 권한 상태 모니터링 및 자동 업데이트
- 다양한 우선순위별 알림 처리

## 사용 방법

### 1. 기본 설정

```typescript
import { initializeAppNotifications } from '$lib/services/notificationIntegration';

// 애플리케이션 시작 시 초기화
onMount(async () => {
	await initializeAppNotifications({
		autoRequestPermission: false, // 수동 권한 요청
		enableMonitoring: true, // 권한 상태 모니터링
		onInitialized: (status) => {
			console.log('알림 시스템 초기화:', status);
		}
	});
});
```

### 2. UI 컴포넌트 사용

```svelte
<script>
	import NotificationPermissionManager from '$lib/components/NotificationPermissionManager.svelte';
</script>

<!-- 권한 관리 UI 표시 -->
<NotificationPermissionManager showCard={true} autoRequest={false} showTestButton={true} />
```

### 3. 수동 권한 요청

```typescript
import { requestNotificationPermissionEnhanced as requestNotificationPermission } from '$lib/services/notifications/permissions';

async function handleRequestPermission() {
	const result = await requestNotificationPermission();

	if (result === 'granted') {
		console.log('알림 권한이 허용되었습니다.');
	} else {
		console.log('알림 권한이 거부되었습니다. 대체 알림을 사용합니다.');
	}
}
```

### 4. 알림 전송

```typescript
import { sendManualNotification } from '$lib/services/notificationIntegration';

// 일반 알림
sendManualNotification('새 메시지', '새로운 메시지가 도착했습니다.', 'normal');

// 긴급 알림 (권한 무시하고 강제 표시)
sendManualNotification('긴급 공지', '즉시 확인이 필요한 공지사항입니다.', 'urgent');
```

### 5. SSE 메시지 처리

```typescript
import { handleSseNotification } from '$lib/services/notificationIntegration';

// SSE 메시지를 알림으로 변환
function handleSseMessage(event) {
	const message = JSON.parse(event.data);

	if (message.type === 'notification') {
		handleSseNotification(message.data, {
			enableBrowserNotifications: true,
			enableSounds: true,
			enableVibration: true
		});
	}
}
```

### 6. 권한 상태 구독

```typescript
import { subscribeToPermissionStatus } from '$lib/services/notificationIntegration';

// 권한 상태 변경 감지
const unsubscribe = subscribeToPermissionStatus((permission) => {
	console.log('권한 상태 변경:', permission);

	if (permission === 'granted') {
		// 브라우저 알림 사용 가능
	} else {
		// 대체 알림 사용
	}
});

// 컴포넌트 언마운트 시 구독 해제
onDestroy(() => {
	unsubscribe();
});
```

## API 참조

### 주요 함수

#### `requestNotificationPermission()`

브라우저 알림 권한을 요청합니다.

**반환값:** `Promise<NotificationPermission>`

#### `getNotificationPermissionStatus()`

현재 알림 권한 상태를 확인합니다.

**반환값:** `NotificationPermission`

#### `isBrowserNotificationAvailable()`

브라우저 알림 사용 가능 여부를 확인합니다.

**반환값:** `boolean`

#### `getPermissionGuideMessage()`

사용자에게 표시할 권한 안내 메시지를 생성합니다.

**반환값:** `PermissionGuideMessage`

### 컴포넌트

#### `NotificationPermissionManager`

알림 권한 관리 UI 컴포넌트입니다.

**Props:**

- `showCard: boolean` - 카드 형태로 표시 여부
- `autoRequest: boolean` - 자동 권한 요청 여부
- `showTestButton: boolean` - 테스트 버튼 표시 여부

### 스토어

#### `notificationPermissionStore`

현재 알림 권한 상태를 저장하는 Svelte 스토어입니다.

#### `alternativeNotificationModeStore`

대체 알림 모드 활성화 여부를 저장하는 Svelte 스토어입니다.

## 우선순위별 동작

### 낮음 (low)

- 사용자 설정을 완전히 따름
- 비활성화 시 알림 표시 안함

### 보통 (normal)

- 기본 알림 설정을 따름
- 완전 비활성화는 무시

### 높음 (high)

- 개인 설정 무시하고 모든 대상자에게 표시
- 브라우저 알림 강제 활성화

### 긴급 (urgent)

- 모든 설정 무시하고 강제 표시
- 브라우저 알림, 사운드, 진동 모두 강제 활성화
- 사용자가 명시적으로 확인할 때까지 유지

## 대체 알림 방식

브라우저 알림을 사용할 수 없는 경우 다음 방식을 사용합니다:

1. **화면 내 토스트 알림** - 기본 대체 방식
2. **사운드 알림** - 오디오 재생 가능 시
3. **진동 알림** - 모바일 디바이스에서
4. **콘솔 로그** - 개발 환경에서

## 브라우저 호환성

- **Chrome/Edge:** 완전 지원
- **Firefox:** 완전 지원
- **Safari:** 부분 지원 (일부 제한사항 있음)
- **모바일 브라우저:** 브라우저별 상이

## 문제 해결

### 권한이 거부된 경우

1. 브라우저 주소창의 자물쇠 아이콘 클릭
2. 알림 설정을 "허용"으로 변경
3. 페이지 새로고침

### 알림이 표시되지 않는 경우

1. 브라우저 알림 설정 확인
2. 운영체제 알림 설정 확인
3. 방해 금지 모드 해제
4. 브라우저 업데이트

### 개발 환경에서 테스트

```typescript
import { testNotification } from '$lib/services/notifications/permissions';

// 테스트 알림 전송
const success = await testNotification();
console.log('테스트 알림 성공:', success);
```
