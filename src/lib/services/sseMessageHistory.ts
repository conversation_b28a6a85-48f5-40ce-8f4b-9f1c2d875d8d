/**
 * SSE 메시지 히스토리 관리 시스템 (함수 기반)
 *
 * 메모리 효율적인 메시지 히스토리 관리 및 정리 기능을 제공합니다.
 */

import type { SseMessage, HistoryConfig } from '$lib/types/sseTypes';

/**
 * 메시지 히스토리 상태
 */
let messageHistory: SseMessage[] = [];
let cleanupTimer: NodeJS.Timeout | null = null;
let historyConfig: HistoryConfig = {
	maxHistorySize: 1000,
	cleanupInterval: 300000, // 5분
	maxAge: 3600000 // 1시간
};

/**
 * 히스토리 관리 시작
 */
export function startHistoryManagement(config?: Partial<HistoryConfig>): void {
	if (config) {
		historyConfig = { ...historyConfig, ...config };
	}

	// 기존 타이머 정리
	if (cleanupTimer) {
		clearInterval(cleanupTimer);
	}

	// 주기적 정리 시작
	cleanupTimer = setInterval(() => {
		cleanupOldMessages();
	}, historyConfig.cleanupInterval);

	console.log('메시지 히스토리 관리 시작:', historyConfig);
}

/**
 * 히스토리 관리 중지
 */
export function stopHistoryManagement(): void {
	if (cleanupTimer) {
		clearInterval(cleanupTimer);
		cleanupTimer = null;
	}
	console.log('메시지 히스토리 관리 중지');
}

/**
 * 메시지를 히스토리에 추가
 */
export function addMessageToHistory(message: SseMessage): void {
	messageHistory.push(message);

	// 최대 크기 초과 시 오래된 메시지 제거
	if (messageHistory.length > historyConfig.maxHistorySize) {
		const removeCount = messageHistory.length - historyConfig.maxHistorySize;
		messageHistory = messageHistory.slice(removeCount);
		console.log(`히스토리 크기 제한으로 ${removeCount}개 메시지 제거`);
	}
}

/**
 * 오래된 메시지 정리
 */
function cleanupOldMessages(): void {
	const cutoffTime = new Date(Date.now() - historyConfig.maxAge);
	const initialCount = messageHistory.length;

	messageHistory = messageHistory.filter((message) => {
		return new Date(message.timestamp) > cutoffTime;
	});

	const removedCount = initialCount - messageHistory.length;
	if (removedCount > 0) {
		console.log(
			`메시지 히스토리 정리: ${removedCount}개 메시지 제거, ${messageHistory.length}개 메시지 유지`
		);
	}
}

/**
 * 최근 메시지 조회
 */
export function getRecentMessages(count: number = 100): SseMessage[] {
	return messageHistory.slice(-count);
}

/**
 * 특정 타입의 메시지 조회
 */
export function getMessagesByType(type: string, count: number = 50): SseMessage[] {
	return messageHistory.filter((message) => message.type === type).slice(-count);
}

/**
 * 특정 시간 범위의 메시지 조회
 */
export function getMessagesByTimeRange(startTime: Date, endTime: Date): SseMessage[] {
	return messageHistory.filter((message) => {
		const messageTime = new Date(message.timestamp);
		return messageTime >= startTime && messageTime <= endTime;
	});
}

/**
 * 메시지 검색
 */
export function searchMessages(searchTerm: string, maxResults: number = 100): SseMessage[] {
	const results: SseMessage[] = [];
	const searchLower = searchTerm.toLowerCase();

	for (let i = messageHistory.length - 1; i >= 0 && results.length < maxResults; i--) {
		const message = messageHistory[i];
		const messageStr = JSON.stringify(message.data).toLowerCase();

		if (messageStr.includes(searchLower)) {
			results.unshift(message); // 시간순 정렬 유지
		}
	}

	return results;
}

/**
 * 히스토리 통계 조회
 */
export function getHistoryStats(): {
	totalMessages: number;
	messagesByType: Record<string, number>;
	oldestMessage?: Date;
	newestMessage?: Date;
	memoryUsage: number;
} {
	const messagesByType: Record<string, number> = {};

	messageHistory.forEach((message) => {
		messagesByType[message.type] = (messagesByType[message.type] || 0) + 1;
	});

	const oldestMessage =
		messageHistory.length > 0 ? new Date(messageHistory[0].timestamp) : undefined;

	const newestMessage =
		messageHistory.length > 0
			? new Date(messageHistory[messageHistory.length - 1].timestamp)
			: undefined;

	// 대략적인 메모리 사용량 계산 (바이트)
	const memoryUsage = messageHistory.reduce((total, message) => {
		return total + JSON.stringify(message).length * 2; // UTF-16 문자당 2바이트
	}, 0);

	return {
		totalMessages: messageHistory.length,
		messagesByType,
		oldestMessage,
		newestMessage,
		memoryUsage
	};
}

/**
 * 히스토리 설정 업데이트
 */
export function updateHistoryConfig(config: Partial<HistoryConfig>): void {
	const oldConfig = { ...historyConfig };
	historyConfig = { ...historyConfig, ...config };

	// 정리 간격이 변경된 경우 타이머 재시작
	if (oldConfig.cleanupInterval !== historyConfig.cleanupInterval && cleanupTimer) {
		stopHistoryManagement();
		startHistoryManagement();
	}

	// 최대 크기가 줄어든 경우 즉시 정리
	if (historyConfig.maxHistorySize < messageHistory.length) {
		const removeCount = messageHistory.length - historyConfig.maxHistorySize;
		messageHistory = messageHistory.slice(removeCount);
		console.log(`히스토리 크기 조정으로 ${removeCount}개 메시지 제거`);
	}

	console.log('히스토리 설정 업데이트:', historyConfig);
}

/**
 * 현재 히스토리 설정 조회
 */
export function getHistoryConfig(): HistoryConfig {
	return { ...historyConfig };
}

/**
 * 히스토리 초기화
 */
export function clearHistory(): void {
	const removedCount = messageHistory.length;
	messageHistory = [];
	console.log(`히스토리 초기화: ${removedCount}개 메시지 제거`);
}

/**
 * 강제 정리 실행
 */
export function forceCleanup(): void {
	console.log('강제 히스토리 정리 실행');
	cleanupOldMessages();
}

/**
 * 메시지 히스토리 내보내기 (디버깅용)
 */
export function exportHistory(): SseMessage[] {
	return [...messageHistory];
}

/**
 * 메시지 히스토리 가져오기 (복원용)
 */
export function importHistory(messages: SseMessage[]): void {
	messageHistory = [...messages];

	// 크기 제한 적용
	if (messageHistory.length > historyConfig.maxHistorySize) {
		const removeCount = messageHistory.length - historyConfig.maxHistorySize;
		messageHistory = messageHistory.slice(removeCount);
		console.log(`가져온 히스토리 크기 조정: ${removeCount}개 메시지 제거`);
	}

	console.log(`히스토리 가져오기 완료: ${messageHistory.length}개 메시지`);
}
