import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	manageCacheSize,
	cleanupOldCache,
	createCacheScheduler,
	generateCacheHealthReport,
	type CacheManagementOptions,
	type CacheCleanupOptions
} from '../cacheManager';
import { initializeIndexedDB } from '../indexedDBManager';

// IndexedDB 모킹
const mockIndexedDB = {
	open: vi.fn(),
	deleteDatabase: vi.fn()
};

// Navigator 모킹
const mockNavigator = {
	onLine: true,
	storage: {
		estimate: vi.fn().mockResolvedValue({
			usage: 25 * 1024 * 1024, // 25MB
			quota: 100 * 1024 * 1024 // 100MB
		})
	}
};

// Window 객체 모킹
const mockWindow = {
	dispatchEvent: vi.fn(),
	addEventListener: vi.fn(),
	removeEventListener: vi.fn()
};

// 전역 객체 모킹 설정
Object.defineProperty(global, 'indexedDB', {
	value: mockIndexedDB,
	writable: true
});

Object.defineProperty(global, 'navigator', {
	value: mockNavigator,
	writable: true
});

Object.defineProperty(global, 'window', {
	value: mockWindow,
	writable: true
});

// 모킹된 IDBDatabase
const createMockDB = () => {
	const mockData = {
		notifications: Array.from({ length: 100 }, (_, i) => ({
			id: i,
			title: `알림 ${i}`,
			received_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString()
		})),
		categories: Array.from({ length: 50 }, (_, i) => ({
			id: i,
			name: `카테고리 ${i}`,
			updated_at: new Date(Date.now() - i * 60 * 60 * 1000).toISOString()
		})),
		settings: [
			{
				key: 'notifications_last_update',
				value: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2시간 전
				updated_at: new Date().toISOString()
			}
		]
	};

	return {
		transaction: vi.fn().mockImplementation((storeNames, mode) => ({
			objectStore: vi.fn().mockImplementation((storeName) => ({
				getAll: vi.fn().mockImplementation(() => ({
					onsuccess: null,
					onerror: null,
					result: mockData[storeName] || []
				})),
				get: vi.fn().mockImplementation((key) => ({
					onsuccess: null,
					onerror: null,
					result: mockData[storeName]?.find((item) => item.key === key || item.id === key)
				})),
				put: vi.fn().mockImplementation(() => ({
					onsuccess: null,
					onerror: null
				})),
				delete: vi.fn().mockImplementation(() => ({
					onsuccess: null,
					onerror: null
				})),
				clear: vi.fn().mockImplementation(() => ({
					onsuccess: null,
					onerror: null
				}))
			}))
		})),
		close: vi.fn()
	};
};

describe('캐시 관리 및 정리 함수', () => {
	let mockDB: any;

	beforeEach(() => {
		mockDB = createMockDB();
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('manageCacheSize', () => {
		it('캐시 용량을 모니터링하고 관리해야 함', async () => {
			const options: CacheManagementOptions = {
				maxSizeBytes: 50 * 1024 * 1024, // 50MB
				warningThreshold: 0.8,
				cleanupThreshold: 0.9,
				autoCleanup: true,
				verbose: true
			};

			const result = await manageCacheSize(mockDB, options);

			expect(result).toBeDefined();
			expect(result.currentUsage).toBe(25 * 1024 * 1024);
			expect(result.maxSize).toBe(50 * 1024 * 1024);
			expect(result.usageRatio).toBe(0.5);
			expect(result.cleanupPerformed).toBe(false); // 임계치 미달로 정리 안함
			expect(result.warnings).toHaveLength(0);
		});

		it('용량 초과 시 자동 정리를 실행해야 함', async () => {
			// 사용량을 높게 설정
			mockNavigator.storage.estimate.mockResolvedValueOnce({
				usage: 95 * 1024 * 1024, // 95MB
				quota: 100 * 1024 * 1024 // 100MB
			});

			const options: CacheManagementOptions = {
				maxSizeBytes: 100 * 1024 * 1024,
				cleanupThreshold: 0.9,
				autoCleanup: true
			};

			const result = await manageCacheSize(mockDB, options);

			expect(result.usageRatio).toBe(0.95);
			expect(result.cleanupPerformed).toBe(true);
			expect(result.warnings).toContain(
				expect.stringContaining('저장소 사용량이 경고 임계치를 초과했습니다')
			);
		});

		it('Storage API가 없는 경우 대안적 방법을 사용해야 함', async () => {
			// Storage API 비활성화
			const originalStorage = mockNavigator.storage;
			delete (mockNavigator as any).storage;

			const result = await manageCacheSize(mockDB);

			expect(result.warnings).toContain(expect.stringContaining('Storage API를 사용할 수 없어'));

			// Storage API 복원
			mockNavigator.storage = originalStorage;
		});
	});

	describe('cleanupOldCache', () => {
		it('오래된 캐시 데이터를 정리해야 함', async () => {
			const options: CacheCleanupOptions = {
				maxAgeMs: 7 * 24 * 60 * 60 * 1000, // 7일
				verbose: true,
				detailed: true
			};

			const result = await cleanupOldCache(mockDB, options);

			expect(result).toBeDefined();
			expect(result.totalItemsBefore).toBeGreaterThan(0);
			expect(result.storeResults).toHaveLength(6); // 6개 스토어
			expect(result.executionTime).toBeGreaterThan(0);

			// 각 스토어 결과 확인
			result.storeResults.forEach((storeResult) => {
				expect(storeResult.storeName).toBeDefined();
				expect(storeResult.success).toBe(true);
				expect(storeResult.executionTime).toBeGreaterThan(0);
			});
		});

		it('스토어별 개별 설정을 적용해야 함', async () => {
			const options: CacheCleanupOptions = {
				storeConfigs: {
					notifications: { maxAgeMs: 1 * 24 * 60 * 60 * 1000, maxItems: 50 }, // 1일, 50개
					categories: { maxAgeMs: 30 * 24 * 60 * 60 * 1000, maxItems: 1000 } // 30일, 1000개
				},
				detailed: true
			};

			const result = await cleanupOldCache(mockDB, options);

			expect(result.storeResults).toHaveLength(6);

			// notifications 스토어는 더 엄격한 정리 기준 적용
			const notificationResult = result.storeResults.find((r) => r.storeName === 'notifications');
			expect(notificationResult).toBeDefined();
		});

		it('정리 중 오류 발생 시 적절히 처리해야 함', async () => {
			// 특정 스토어에서 오류 발생 시뮬레이션
			const errorDB = {
				...mockDB,
				transaction: vi.fn().mockImplementation((storeNames) => {
					if (storeNames.includes('categories')) {
						throw new Error('스토어 접근 오류');
					}
					return mockDB.transaction(storeNames);
				})
			};

			const options: CacheCleanupOptions = {
				stopOnError: false // 오류 발생 시에도 계속 진행
			};

			const result = await cleanupOldCache(errorDB, options);

			expect(result.warnings).toHaveLength(1);
			expect(result.warnings[0]).toContain('categories 스토어 정리 실패');
		});
	});

	describe('createCacheScheduler', () => {
		it('캐시 스케줄러를 생성하고 시작해야 함', () => {
			const scheduler = createCacheScheduler(mockDB, {
				cleanupInterval: 1000, // 1초 (테스트용)
				monitoringInterval: 500, // 0.5초 (테스트용)
				autoStart: false
			});

			expect(scheduler).toBeDefined();
			expect(scheduler.isRunning()).toBe(false);

			scheduler.start();
			expect(scheduler.isRunning()).toBe(true);

			scheduler.stop();
			expect(scheduler.isRunning()).toBe(false);
		});

		it('즉시 정리 및 관리를 실행할 수 있어야 함', async () => {
			const scheduler = createCacheScheduler(mockDB, {
				autoStart: false
			});

			const cleanupResult = await scheduler.runCleanupNow();
			expect(cleanupResult).toBeDefined();
			expect(cleanupResult.executionTime).toBeGreaterThan(0);

			const managementResult = await scheduler.runManagementNow();
			expect(managementResult).toBeDefined();
			expect(managementResult.executionTime).toBeGreaterThan(0);
		});
	});

	describe('generateCacheHealthReport', () => {
		it('캐시 상태 보고서를 생성해야 함', async () => {
			const report = await generateCacheHealthReport(mockDB, {
				detailed: true,
				generateRecommendations: true
			});

			expect(report).toBeDefined();
			expect(report.overallScore).toBeGreaterThanOrEqual(0);
			expect(report.overallScore).toBeLessThanOrEqual(100);
			expect(report.overallGrade).toMatch(/excellent|good|fair|poor|critical/);
			expect(report.storeHealth).toHaveLength(5); // 5개 스토어
			expect(report.storageInfo).toBeDefined();
			expect(report.timestamp).toBeDefined();

			// 스토어별 상태 확인
			report.storeHealth.forEach((storeHealth) => {
				expect(storeHealth.storeName).toBeDefined();
				expect(storeHealth.score).toBeGreaterThanOrEqual(0);
				expect(storeHealth.score).toBeLessThanOrEqual(100);
				expect(Array.isArray(storeHealth.issues)).toBe(true);
			});
		});

		it('성능 테스트를 포함한 보고서를 생성해야 함', async () => {
			const report = await generateCacheHealthReport(mockDB, {
				performanceTest: true
			});

			expect(report.performance).toBeDefined();
			if (report.performance) {
				expect(report.performance.totalTestTime).toBeGreaterThan(0);
			}
		});

		it('권장사항을 생성해야 함', async () => {
			// 만료된 캐시 시뮬레이션
			const expiredDB = {
				...mockDB,
				transaction: vi.fn().mockImplementation(() => ({
					objectStore: vi.fn().mockImplementation(() => ({
						get: vi.fn().mockImplementation(() => ({
							onsuccess: null,
							onerror: null,
							result: {
								key: 'notifications_last_update',
								value: new Date(Date.now() - 10 * 60 * 60 * 1000).toISOString(), // 10시간 전
								updated_at: new Date().toISOString()
							}
						})),
						getAll: vi.fn().mockImplementation(() => ({
							onsuccess: null,
							onerror: null,
							result: []
						}))
					}))
				}))
			};

			const report = await generateCacheHealthReport(expiredDB, {
				generateRecommendations: true
			});

			expect(report.recommendations.length).toBeGreaterThan(0);
			expect(report.recommendations.some((r) => r.includes('캐시가 만료'))).toBe(true);
		});
	});

	describe('이벤트 발생', () => {
		it('캐시 관리 완료 시 이벤트를 발생시켜야 함', async () => {
			await manageCacheSize(mockDB);

			expect(mockWindow.dispatchEvent).toHaveBeenCalledWith(
				expect.objectContaining({
					type: 'cacheManagementCompleted'
				})
			);
		});

		it('캐시 정리 완료 시 이벤트를 발생시켜야 함', async () => {
			await cleanupOldCache(mockDB);

			expect(mockWindow.dispatchEvent).toHaveBeenCalledWith(
				expect.objectContaining({
					type: 'cacheCleanupCompleted'
				})
			);
		});
	});

	describe('오류 처리', () => {
		it('데이터베이스 오류 시 적절히 처리해야 함', async () => {
			const errorDB = {
				transaction: vi.fn().mockImplementation(() => {
					throw new Error('데이터베이스 연결 오류');
				})
			};

			const result = await manageCacheSize(errorDB);

			expect(result.warnings).toHaveLength(1);
			expect(result.warnings[0]).toContain('캐시 용량 관리 실패');
		});

		it('Storage API 오류 시 대안적 방법을 사용해야 함', async () => {
			mockNavigator.storage.estimate.mockRejectedValueOnce(new Error('Storage API 오류'));

			const result = await manageCacheSize(mockDB);

			expect(result.warnings).toHaveLength(1);
			expect(result.warnings[0]).toContain('캐시 용량 관리 실패');
		});
	});
});
