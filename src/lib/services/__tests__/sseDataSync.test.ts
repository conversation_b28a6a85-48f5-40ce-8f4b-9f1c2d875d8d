/**
 * SSE 데이터 동기화 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	requestMissedData,
	retrySyncIfNeeded,
	getSyncState,
	resetSyncState,
	triggerManualSync
} from '../sseDataSync';

// fetch 모킹
global.fetch = vi.fn();

// localStorage 모킹
const localStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn()
};
global.localStorage = localStorageMock as any;

// getCurrentConnectionState 모킹
vi.mock('../sseConnectionState', () => ({
	getCurrentConnectionState: vi.fn(() => ({
		status: 'connected',
		lastConnected: new Date('2023-01-01T10:00:00Z'),
		isOnline: true
	}))
}));

// handleSseMessage 모킹
vi.mock('../sseMessageRouter', () => ({
	handleSseMessage: vi.fn()
}));

describe('SSE 데이터 동기화', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		resetSyncState();
		localStorageMock.getItem.mockReturnValue(null);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('기본 동기화', () => {
		it('초기 동기화 상태가 올바르게 설정되어야 함', () => {
			const syncState = getSyncState();

			expect(syncState.lastSyncTime).toBeNull();
			expect(syncState.isSyncing).toBe(false);
			expect(syncState.syncErrors).toHaveLength(0);
			expect(syncState.pendingSync).toBe(false);
		});

		it('첫 연결 시 전체 데이터 동기화를 요청해야 함', async () => {
			// getCurrentConnectionState에서 lastConnected가 null인 경우
			const { getCurrentConnectionState } = await import('../sseConnectionState');
			vi.mocked(getCurrentConnectionState).mockReturnValue({
				status: 'connected',
				lastConnected: null,
				isOnline: true
			} as any);

			// fetch 응답 모킹
			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					notifications: [],
					data_updates: [],
					categories: [],
					employees: []
				})
			} as Response);

			await requestMissedData();

			expect(fetch).toHaveBeenCalledWith(
				'/api/sync/full-data',
				expect.objectContaining({
					method: 'GET'
				})
			);
		});

		it('증분 동기화를 요청해야 함', async () => {
			// 인증 토큰 설정
			localStorageMock.getItem.mockImplementation((key) => {
				if (key === 'auth_token') return 'test-token';
				if (key === 'current_user') return JSON.stringify({ id: 123 });
				return null;
			});

			// fetch 응답 모킹
			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					notifications: [
						{
							id: 1,
							title: '테스트 알림',
							message: '테스트 메시지',
							priority: 'normal',
							target_type: 'all'
						}
					],
					data_updates: []
				})
			} as Response);

			await requestMissedData();

			expect(fetch).toHaveBeenCalledWith(
				'/api/sync/missed-data',
				expect.objectContaining({
					method: 'POST',
					headers: expect.objectContaining({
						'Content-Type': 'application/json',
						Authorization: 'Bearer test-token'
					}),
					body: expect.stringContaining('last_sync')
				})
			);
		});

		it('동기화 중에는 중복 요청을 방지해야 함', async () => {
			// 첫 번째 요청이 진행 중일 때
			const firstRequest = requestMissedData();

			// 두 번째 요청
			await requestMissedData();

			// 첫 번째 요청 완료
			await firstRequest;

			// fetch가 한 번만 호출되어야 함
			expect(fetch).toHaveBeenCalledTimes(1);
		});
	});

	describe('오류 처리', () => {
		it('네트워크 오류 시 오류 상태를 기록해야 함', async () => {
			vi.mocked(fetch).mockRejectedValueOnce(new Error('네트워크 오류'));

			await requestMissedData();

			const syncState = getSyncState();
			expect(syncState.syncErrors.length).toBeGreaterThan(0);
			expect(syncState.pendingSync).toBe(true);
		});

		it('HTTP 오류 시 오류 상태를 기록해야 함', async () => {
			vi.mocked(fetch).mockResolvedValueOnce({
				ok: false,
				status: 500,
				statusText: 'Internal Server Error'
			} as Response);

			await requestMissedData();

			const syncState = getSyncState();
			expect(syncState.syncErrors.length).toBeGreaterThan(0);
			expect(syncState.pendingSync).toBe(true);
		});
	});

	describe('재시도 로직', () => {
		it('대기 중인 동기화가 있으면 재시도해야 함', async () => {
			// 첫 번째 동기화 실패
			vi.mocked(fetch).mockRejectedValueOnce(new Error('네트워크 오류'));
			await requestMissedData();

			// 성공하는 응답으로 변경
			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({ notifications: [], data_updates: [] })
			} as Response);

			// 재시도
			await retrySyncIfNeeded();

			const syncState = getSyncState();
			expect(syncState.pendingSync).toBe(false);
		});

		it('대기 중인 동기화가 없으면 재시도하지 않아야 함', async () => {
			await retrySyncIfNeeded();

			// fetch가 호출되지 않아야 함
			expect(fetch).not.toHaveBeenCalled();
		});
	});

	describe('수동 동기화', () => {
		it('수동 동기화를 트리거할 수 있어야 함', async () => {
			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({ notifications: [], data_updates: [] })
			} as Response);

			await triggerManualSync();

			expect(fetch).toHaveBeenCalled();
		});
	});

	describe('데이터 처리', () => {
		it('알림 데이터를 올바르게 처리해야 함', async () => {
			const { handleSseMessage } = await import('../sseMessageRouter');

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					notifications: [
						{
							id: 1,
							title: '테스트 알림',
							message: '테스트 메시지',
							priority: 'normal',
							target_type: 'all'
						}
					]
				})
			} as Response);

			await requestMissedData();

			expect(handleSseMessage).toHaveBeenCalledWith(
				expect.objectContaining({
					data: expect.stringContaining('notification')
				})
			);
		});

		it('데이터 업데이트를 올바르게 처리해야 함', async () => {
			const { handleSseMessage } = await import('../sseMessageRouter');

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					data_updates: [
						{
							model: 'categories',
							action: 'created',
							payload: { id: 1, name: '새 카테고리' },
							affected_ids: [1],
							timestamp: new Date().toISOString()
						}
					]
				})
			} as Response);

			await requestMissedData();

			expect(handleSseMessage).toHaveBeenCalledWith(
				expect.objectContaining({
					data: expect.stringContaining('data_update')
				})
			);
		});

		it('전체 데이터 동기화 시 배치 업데이트를 처리해야 함', async () => {
			const { getCurrentConnectionState } = await import('../sseConnectionState');
			vi.mocked(getCurrentConnectionState).mockReturnValue({
				status: 'connected',
				lastConnected: null,
				isOnline: true
			} as any);

			const { handleSseMessage } = await import('../sseMessageRouter');

			vi.mocked(fetch).mockResolvedValueOnce({
				ok: true,
				json: async () => ({
					categories: [
						{ id: 1, name: '카테고리 1' },
						{ id: 2, name: '카테고리 2' }
					],
					employees: [{ id: 1, name: '직원 1' }]
				})
			} as Response);

			await requestMissedData();

			// 카테고리와 직원 데이터에 대한 배치 업데이트가 처리되어야 함
			expect(handleSseMessage).toHaveBeenCalledTimes(2);
		});
	});
});
