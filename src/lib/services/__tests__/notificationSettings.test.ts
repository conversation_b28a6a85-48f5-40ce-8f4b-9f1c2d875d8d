/**
 * 알림 설정 관리 함수 테스트
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
	getNotificationSettings,
	getDefaultNotificationSettings,
	saveNotificationSettings,
	isWorkingHours,
	isNotificationTypeEnabled,
	setNotificationTypeEnabled,
	updateWorkingHours,
	updateDisplayDuration,
	updateSoundSettings,
	resetNotificationSettings,
	validateNotificationSettings,
	type NotificationSettings,
	type WorkingHours
} from '../notificationSettings';

// IndexedDB 모킹
vi.mock('../indexedDBManager', () => ({
	getData: vi.fn(),
	putData: vi.fn()
}));

// 모킹된 함수들 가져오기
import { getData, putData } from '../indexedDBManager';
const mockGetData = vi.mocked(getData);
const mockPutData = vi.mocked(putData);

// 모킹된 IDBDatabase
const mockDb = {} as IDBDatabase;

describe('알림 설정 관리 함수', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('getDefaultNotificationSettings', () => {
		it('기본 알림 설정을 올바르게 반환해야 함', () => {
			const defaultSettings = getDefaultNotificationSettings();

			expect(defaultSettings).toEqual({
				enableBrowserNotifications: true,
				enableSounds: true,
				enableVibration: true,
				workingHours: {
					start: '09:00',
					end: '18:00',
					enabled: false,
					timezone: expect.any(String),
					weekdays: [1, 2, 3, 4, 5]
				},
				soundFile: '/sounds/notification.mp3',
				displayDuration: {
					low: 3000,
					normal: 5000,
					high: 8000,
					urgent: 0
				},
				disabledTypes: [],
				volume: 0.5,
				customSounds: {
					low: '/sounds/notification-low.mp3',
					normal: '/sounds/notification.mp3',
					high: '/sounds/notification-high.mp3',
					urgent: '/sounds/notification-urgent.mp3'
				}
			});
		});
	});

	describe('getNotificationSettings', () => {
		it('DB가 없으면 기본 설정을 반환해야 함', async () => {
			const settings = await getNotificationSettings();
			const defaultSettings = getDefaultNotificationSettings();

			expect(settings).toEqual(defaultSettings);
		});

		it('저장된 설정이 있으면 기본 설정과 병합하여 반환해야 함', async () => {
			const savedSettings = {
				enableBrowserNotifications: false,
				workingHours: {
					start: '10:00',
					enabled: true
				}
			};

			mockGetData.mockResolvedValue({
				value: savedSettings
			});

			const settings = await getNotificationSettings(mockDb);

			expect(settings.enableBrowserNotifications).toBe(false);
			expect(settings.workingHours.start).toBe('10:00');
			expect(settings.workingHours.enabled).toBe(true);
			expect(settings.workingHours.end).toBe('18:00'); // 기본값 유지
			expect(settings.enableSounds).toBe(true); // 기본값 유지
		});

		it('DB 오류 시 기본 설정을 반환해야 함', async () => {
			mockGetData.mockRejectedValue(new Error('DB 오류'));

			const settings = await getNotificationSettings(mockDb);
			const defaultSettings = getDefaultNotificationSettings();

			expect(settings).toEqual(defaultSettings);
		});
	});

	describe('saveNotificationSettings', () => {
		it('설정을 올바르게 저장해야 함', async () => {
			const settings = getDefaultNotificationSettings();

			await saveNotificationSettings(mockDb, settings);

			expect(mockPutData).toHaveBeenCalledWith(mockDb, 'settings', {
				key: 'notification_settings',
				value: settings,
				updated_at: expect.any(String)
			});
		});

		it('저장 실패 시 에러를 던져야 함', async () => {
			const settings = getDefaultNotificationSettings();
			mockPutData.mockRejectedValue(new Error('저장 실패'));

			await expect(saveNotificationSettings(mockDb, settings)).rejects.toThrow('저장 실패');
		});
	});

	describe('isWorkingHours', () => {
		it('근무 시간이 비활성화되면 항상 true를 반환해야 함', () => {
			const workingHours: WorkingHours = {
				start: '09:00',
				end: '18:00',
				enabled: false
			};

			expect(isWorkingHours(workingHours)).toBe(true);
		});

		it('근무 시간 내에 있으면 true를 반환해야 함', () => {
			const workingHours: WorkingHours = {
				start: '09:00',
				end: '18:00',
				enabled: true,
				weekdays: [1, 2, 3, 4, 5] // 월-금
			};

			// 화요일 오후 2시
			const testTime = new Date('2024-01-02T14:00:00');

			expect(isWorkingHours(workingHours, testTime)).toBe(true);
		});

		it('근무 시간 외에 있으면 false를 반환해야 함', () => {
			const workingHours: WorkingHours = {
				start: '09:00',
				end: '18:00',
				enabled: true,
				weekdays: [1, 2, 3, 4, 5]
			};

			// 화요일 오후 8시
			const testTime = new Date('2024-01-02T20:00:00');

			expect(isWorkingHours(workingHours, testTime)).toBe(false);
		});

		it('주말이면 false를 반환해야 함', () => {
			const workingHours: WorkingHours = {
				start: '09:00',
				end: '18:00',
				enabled: true,
				weekdays: [1, 2, 3, 4, 5]
			};

			// 토요일 오후 2시
			const testTime = new Date('2024-01-06T14:00:00');

			expect(isWorkingHours(workingHours, testTime)).toBe(false);
		});

		it('자정을 넘나드는 근무 시간을 올바르게 처리해야 함', () => {
			const workingHours: WorkingHours = {
				start: '22:00',
				end: '06:00',
				enabled: true
			};

			// 밤 11시
			const nightTime = new Date('2024-01-02T23:00:00');
			expect(isWorkingHours(workingHours, nightTime)).toBe(true);

			// 새벽 3시
			const earlyMorning = new Date('2024-01-02T03:00:00');
			expect(isWorkingHours(workingHours, earlyMorning)).toBe(true);

			// 오후 2시
			const afternoon = new Date('2024-01-02T14:00:00');
			expect(isWorkingHours(workingHours, afternoon)).toBe(false);
		});
	});

	describe('isNotificationTypeEnabled', () => {
		it('비활성화된 타입이 아니면 true를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.disabledTypes = ['system', 'maintenance'];

			expect(isNotificationTypeEnabled('urgent', settings)).toBe(true);
			expect(isNotificationTypeEnabled('normal', settings)).toBe(true);
		});

		it('비활성화된 타입이면 false를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.disabledTypes = ['system', 'maintenance'];

			expect(isNotificationTypeEnabled('system', settings)).toBe(false);
			expect(isNotificationTypeEnabled('maintenance', settings)).toBe(false);
		});
	});

	describe('setNotificationTypeEnabled', () => {
		it('타입을 활성화하면 disabledTypes에서 제거해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			settings.disabledTypes = ['system', 'maintenance'];

			mockGetData.mockResolvedValue({ value: settings });

			await setNotificationTypeEnabled(mockDb, 'system', true);

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.disabledTypes).not.toContain('system');
			expect(savedSettings.disabledTypes).toContain('maintenance');
		});

		it('타입을 비활성화하면 disabledTypes에 추가해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			settings.disabledTypes = ['maintenance'];

			mockGetData.mockResolvedValue({ value: settings });

			await setNotificationTypeEnabled(mockDb, 'system', false);

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.disabledTypes).toContain('system');
			expect(savedSettings.disabledTypes).toContain('maintenance');
		});

		it('중복 추가를 방지해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			settings.disabledTypes = ['system'];

			mockGetData.mockResolvedValue({ value: settings });

			await setNotificationTypeEnabled(mockDb, 'system', false);

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.disabledTypes.filter((type) => type === 'system')).toHaveLength(1);
		});
	});

	describe('updateWorkingHours', () => {
		it('근무 시간 설정을 부분적으로 업데이트해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			mockGetData.mockResolvedValue({ value: settings });

			await updateWorkingHours(mockDb, {
				start: '08:00',
				enabled: true
			});

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.workingHours.start).toBe('08:00');
			expect(savedSettings.workingHours.enabled).toBe(true);
			expect(savedSettings.workingHours.end).toBe('18:00'); // 기존값 유지
		});
	});

	describe('updateDisplayDuration', () => {
		it('특정 우선순위의 지속시간을 업데이트해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			mockGetData.mockResolvedValue({ value: settings });

			await updateDisplayDuration(mockDb, 'high', 10000);

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.displayDuration.high).toBe(10000);
			expect(savedSettings.displayDuration.normal).toBe(5000); // 기존값 유지
		});
	});

	describe('updateSoundSettings', () => {
		it('사운드 설정을 업데이트해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			mockGetData.mockResolvedValue({ value: settings });

			await updateSoundSettings(mockDb, {
				enableSounds: false,
				volume: 0.8,
				customSounds: {
					urgent: '/custom/urgent.mp3'
				}
			});

			const savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.enableSounds).toBe(false);
			expect(savedSettings.volume).toBe(0.8);
			expect(savedSettings.customSounds.urgent).toBe('/custom/urgent.mp3');
			expect(savedSettings.customSounds.normal).toBe('/sounds/notification.mp3'); // 기존값 유지
		});

		it('볼륨을 0-1 범위로 제한해야 함', async () => {
			const settings = getDefaultNotificationSettings();
			mockGetData.mockResolvedValue({ value: settings });

			await updateSoundSettings(mockDb, { volume: 1.5 });
			let savedSettings = mockPutData.mock.calls[0][2].value;
			expect(savedSettings.volume).toBe(1);

			await updateSoundSettings(mockDb, { volume: -0.5 });
			savedSettings = mockPutData.mock.calls[1][2].value;
			expect(savedSettings.volume).toBe(0);
		});
	});

	describe('resetNotificationSettings', () => {
		it('설정을 기본값으로 초기화해야 함', async () => {
			await resetNotificationSettings(mockDb);

			const savedSettings = mockPutData.mock.calls[0][2].value;
			const defaultSettings = getDefaultNotificationSettings();
			expect(savedSettings).toEqual(defaultSettings);
		});
	});

	describe('validateNotificationSettings', () => {
		it('유효한 설정에 대해 isValid: true를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			const result = validateNotificationSettings(settings);

			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});

		it('잘못된 시간 형식에 대해 오류를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.workingHours.start = '25:00'; // 잘못된 시간
			settings.workingHours.end = '9:60'; // 잘못된 분

			const result = validateNotificationSettings(settings);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('근무 시작 시간 형식이 올바르지 않습니다. (HH:MM)');
			expect(result.errors).toContain('근무 종료 시간 형식이 올바르지 않습니다. (HH:MM)');
		});

		it('잘못된 볼륨 범위에 대해 오류를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.volume = 1.5;

			const result = validateNotificationSettings(settings);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('볼륨은 0과 1 사이의 값이어야 합니다.');
		});

		it('잘못된 지속시간에 대해 오류를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.displayDuration.normal = 500; // 너무 짧음
			settings.displayDuration.high = 35000; // 너무 김

			const result = validateNotificationSettings(settings);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('normal 우선순위 지속시간은 1초에서 30초 사이여야 합니다.');
			expect(result.errors).toContain('high 우선순위 지속시간은 1초에서 30초 사이여야 합니다.');
		});

		it('잘못된 요일 설정에 대해 오류를 반환해야 함', () => {
			const settings = getDefaultNotificationSettings();
			settings.workingHours.weekdays = [1, 2, 7, -1]; // 잘못된 요일

			const result = validateNotificationSettings(settings);

			expect(result.isValid).toBe(false);
			expect(result.errors).toContain('요일 설정이 올바르지 않습니다. (0-6 범위)');
		});
	});
});
