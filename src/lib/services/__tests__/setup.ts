/**
 * 테스트 환경 설정
 */

import { vi } from 'vitest';

// IndexedDB 모킹
const mockIDBRequest = {
	result: null,
	error: null,
	onsuccess: null,
	onerror: null,
	onupgradeneeded: null
};

const mockIDBDatabase = {
	createObjectStore: vi.fn(),
	transaction: vi.fn(() => ({
		objectStore: vi.fn(() => ({
			get: vi.fn(() => mockIDBRequest),
			getAll: vi.fn(() => mockIDBRequest),
			put: vi.fn(() => mockIDBRequest),
			delete: vi.fn(() => mockIDBRequest),
			clear: vi.fn(() => mockIDBRequest),
			createIndex: vi.fn()
		}))
	})),
	close: vi.fn(),
	objectStoreNames: {
		contains: vi.fn(() => false)
	}
};

global.indexedDB = {
	open: vi.fn(() => {
		const request = { ...mockIDBRequest };
		setTimeout(() => {
			request.result = mockIDBDatabase;
			if (request.onsuccess) request.onsuccess({ target: request });
		}, 0);
		return request;
	}),
	deleteDatabase: vi.fn(() => mockIDBRequest)
} as any;

// Notification API 모킹
const MockNotification = vi
	.fn()
	.mockImplementation((title: string, options?: NotificationOptions) => ({
		title,
		options,
		onclick: vi.fn(),
		onclose: vi.fn(),
		onerror: vi.fn(),
		close: vi.fn()
	}));

MockNotification.permission = 'granted';
MockNotification.requestPermission = vi.fn().mockResolvedValue('granted');

Object.defineProperty(window, 'Notification', {
	value: MockNotification,
	writable: true
});

// Navigator 모킹
Object.defineProperty(navigator, 'vibrate', {
	value: vi.fn().mockReturnValue(true),
	writable: true
});

// Audio 모킹
global.Audio = vi.fn().mockImplementation(() => ({
	play: vi.fn().mockResolvedValue(undefined),
	pause: vi.fn(),
	volume: 0.5,
	loop: false,
	addEventListener: vi.fn(),
	removeEventListener: vi.fn()
}));

// CustomEvent 모킹
global.CustomEvent = vi.fn().mockImplementation((type, options) => ({
	type,
	detail: options?.detail
}));

// dispatchEvent 모킹
Object.defineProperty(window, 'dispatchEvent', {
	value: vi.fn(),
	writable: true
});

// document 모킹
Object.defineProperty(document, 'title', {
	value: 'Test Title',
	writable: true
});

Object.defineProperty(document, 'querySelector', {
	value: vi.fn().mockReturnValue({
		href: '/favicon.ico'
	}),
	writable: true
});
