/**
 * SSE 메시지 라우터 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	routeMessage,
	validateMessage,
	registerMessageHandler,
	unregisterMessageHandler,
	getHandlerCount
} from '../sseMessageRouter';
import type { SseMessage, NotificationMessage, DataUpdateMessage } from '$lib/types/sseTypes';

// 모킹
vi.mock('$lib/services/sseStats', () => ({
	recordMessage: vi.fn(),
	recordError: vi.fn(),
	startMessageProcessingTimer: vi.fn(() => Date.now()),
	endMessageProcessingTimer: vi.fn()
}));

vi.mock('$lib/services/sseConnectionState', () => ({
	updateConnectionStatus: vi.fn()
}));

vi.mock('$lib/services/sseMessageBatcher', () => ({
	addMessageToBatch: vi.fn()
}));

vi.mock('$lib/services/sseMessageHistory', () => ({
	addMessageToHistory: vi.fn()
}));

vi.mock('$lib/utils/authUtils', () => ({
	getCurrentUserId: vi.fn(() => 1)
}));

// 전역 객체 모킹
global.Notification = {
	permission: 'granted',
	requestPermission: vi.fn(() => Promise.resolve('granted'))
} as any;

global.Audio = vi.fn(() => ({
	play: vi.fn(() => Promise.resolve()),
	volume: 0.5,
	src: ''
})) as any;

// localStorage 모킹
const localStorageMock = {
	getItem: vi.fn(),
	setItem: vi.fn(),
	removeItem: vi.fn(),
	clear: vi.fn()
};
global.localStorage = localStorageMock as any;

describe('SSE 메시지 라우터', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		// 기본 사용자 정보 설정
		localStorageMock.getItem.mockImplementation((key) => {
			if (key === 'current_user') {
				return JSON.stringify({ id: 1, groups: [1, 2] });
			}
			if (key === 'notification_sound_enabled') {
				return 'true';
			}
			return null;
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('validateMessage', () => {
		it('유효한 알림 메시지를 검증해야 함', () => {
			const validNotificationMessage: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 1,
					title: '테스트 알림',
					message: '테스트 메시지입니다',
					type: 'info',
					priority: 'normal',
					target_type: 'all'
				}
			};

			expect(validateMessage(validNotificationMessage)).toBe(true);
		});

		it('유효한 데이터 업데이트 메시지를 검증해야 함', () => {
			const validDataUpdateMessage: SseMessage = {
				type: 'data_update',
				timestamp: new Date().toISOString(),
				data: {
					model: 'categories',
					action: 'created',
					payload: { id: 1, name: '새 카테고리' },
					affected_ids: [1],
					timestamp: new Date().toISOString()
				}
			};

			expect(validateMessage(validDataUpdateMessage)).toBe(true);
		});

		it('유효한 하트비트 메시지를 검증해야 함', () => {
			const validHeartbeatMessage: SseMessage = {
				type: 'heartbeat',
				timestamp: new Date().toISOString(),
				data: {
					timestamp: new Date().toISOString()
				}
			};

			expect(validateMessage(validHeartbeatMessage)).toBe(true);
		});

		it('유효한 연결 메시지를 검증해야 함', () => {
			const validConnectionMessage: SseMessage = {
				type: 'connection',
				timestamp: new Date().toISOString(),
				data: {
					status: 'authenticated',
					user_id: 1
				}
			};

			expect(validateMessage(validConnectionMessage)).toBe(true);
		});

		it('잘못된 메시지 구조를 거부해야 함', () => {
			expect(validateMessage(null)).toBe(false);
			expect(validateMessage(undefined)).toBe(false);
			expect(validateMessage('string')).toBe(false);
			expect(validateMessage({})).toBe(false);
			expect(validateMessage({ type: null })).toBe(false);
		});

		it('잘못된 알림 메시지를 거부해야 함', () => {
			const invalidNotificationMessage: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 'invalid', // 숫자가 아님
					title: '', // 빈 문자열
					message: 'test',
					priority: 'invalid', // 유효하지 않은 우선순위
					target_type: 'all'
				}
			};

			expect(validateMessage(invalidNotificationMessage)).toBe(false);
		});

		it('잘못된 데이터 업데이트 메시지를 거부해야 함', () => {
			const invalidDataUpdateMessage: SseMessage = {
				type: 'data_update',
				timestamp: new Date().toISOString(),
				data: {
					model: 'invalid_model', // 유효하지 않은 모델
					action: 'created',
					payload: {},
					affected_ids: ['invalid'] // 숫자가 아닌 ID
				}
			};

			expect(validateMessage(invalidDataUpdateMessage)).toBe(false);
		});
	});

	describe('routeMessage', () => {
		it('알림 메시지를 올바르게 라우팅해야 함', () => {
			const notificationMessage: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 1,
					title: '테스트 알림',
					message: '테스트 메시지입니다',
					type: 'info',
					priority: 'normal',
					target_type: 'all'
				}
			};

			// 콘솔 로그 모킹
			const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

			expect(() => routeMessage(notificationMessage)).not.toThrow();
			expect(consoleSpy).toHaveBeenCalledWith('알림 메시지 처리 완료:', notificationMessage.data);

			consoleSpy.mockRestore();
		});

		it('데이터 업데이트 메시지를 올바르게 라우팅해야 함', () => {
			const dataUpdateMessage: SseMessage = {
				type: 'data_update',
				timestamp: new Date().toISOString(),
				data: {
					model: 'categories',
					action: 'created',
					payload: { id: 1, name: '새 카테고리' },
					affected_ids: [1],
					timestamp: new Date().toISOString()
				}
			};

			const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

			expect(() => routeMessage(dataUpdateMessage)).not.toThrow();
			expect(consoleSpy).toHaveBeenCalledWith(
				'데이터 업데이트 메시지 처리 완료:',
				dataUpdateMessage.data
			);

			consoleSpy.mockRestore();
		});

		it('하트비트 메시지를 올바르게 라우팅해야 함', () => {
			const heartbeatMessage: SseMessage = {
				type: 'heartbeat',
				timestamp: new Date().toISOString(),
				data: {
					timestamp: new Date().toISOString()
				}
			};

			const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

			expect(() => routeMessage(heartbeatMessage)).not.toThrow();
			expect(consoleSpy).toHaveBeenCalledWith('하트비트 메시지 처리 완료');

			consoleSpy.mockRestore();
		});

		it('연결 메시지를 올바르게 라우팅해야 함', () => {
			const connectionMessage: SseMessage = {
				type: 'connection',
				timestamp: new Date().toISOString(),
				data: {
					status: 'authenticated',
					user_id: 1
				}
			};

			const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

			expect(() => routeMessage(connectionMessage)).not.toThrow();
			expect(consoleSpy).toHaveBeenCalledWith('연결 메시지 처리 완료:', connectionMessage.data);

			consoleSpy.mockRestore();
		});

		it('알 수 없는 메시지 타입을 처리해야 함', () => {
			const unknownMessage: SseMessage = {
				type: 'unknown_type' as any,
				timestamp: new Date().toISOString(),
				data: {}
			};

			const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

			expect(() => routeMessage(unknownMessage)).not.toThrow();
			expect(consoleWarnSpy).toHaveBeenCalledWith(
				'알 수 없는 메시지 타입:',
				'unknown_type',
				unknownMessage
			);

			consoleWarnSpy.mockRestore();
		});

		it('유효하지 않은 메시지를 거부해야 함', () => {
			const invalidMessage = {
				type: null,
				data: {}
			} as any;

			const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

			routeMessage(invalidMessage);
			expect(consoleWarnSpy).toHaveBeenCalledWith('유효하지 않은 메시지:', invalidMessage);

			consoleWarnSpy.mockRestore();
		});
	});

	describe('메시지 핸들러 등록', () => {
		it('핸들러를 등록하고 제거할 수 있어야 함', () => {
			const testHandler = vi.fn();

			// 핸들러 등록
			registerMessageHandler('test_type', testHandler);
			expect(getHandlerCount('test_type')).toBe(1);

			// 핸들러 제거
			unregisterMessageHandler('test_type', testHandler);
			expect(getHandlerCount('test_type')).toBe(0);
		});

		it('여러 핸들러를 등록할 수 있어야 함', () => {
			const handler1 = vi.fn();
			const handler2 = vi.fn();

			registerMessageHandler('test_type', handler1);
			registerMessageHandler('test_type', handler2);

			expect(getHandlerCount('test_type')).toBe(2);

			// 정리
			unregisterMessageHandler('test_type', handler1);
			unregisterMessageHandler('test_type', handler2);
		});
	});

	describe('에러 처리', () => {
		it('메시지 처리 중 오류가 발생해도 계속 실행되어야 함', () => {
			const errorMessage: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 1,
					title: '테스트 알림',
					message: '테스트 메시지입니다',
					type: 'info',
					priority: 'normal',
					target_type: 'all'
				}
			};

			// 에러를 발생시키는 핸들러 등록
			const errorHandler = vi.fn(() => {
				throw new Error('테스트 에러');
			});
			registerMessageHandler('notification', errorHandler);

			const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

			expect(() => routeMessage(errorMessage)).not.toThrow();
			expect(consoleErrorSpy).toHaveBeenCalled();

			consoleErrorSpy.mockRestore();
			unregisterMessageHandler('notification', errorHandler);
		});
	});
});
