/**
 * 알림 상호작용 기능 통합 테스트
 * 작업 5.6: 실제 구현된 함수들의 기본 동작 검증
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
	getNotificationIcon,
	checkIconExists,
	handleNotificationActionUrl
} from '../notificationHandler';

describe('알림 상호작용 기능 통합 테스트', () => {
	beforeEach(() => {
		// DOM 환경 설정
		global.window = {
			...global.window,
			location: {
				origin: 'https://example.com',
				href: 'https://example.com/current'
			},
			history: {
				pushState: () => {},
				replaceState: () => {}
			},
			open: () => null,
			dispatchEvent: () => true,
			console: {
				log: () => {},
				warn: () => {},
				error: () => {}
			}
		} as any;

		// PopStateEvent 생성자 mock
		global.PopStateEvent = class MockPopStateEvent {
			constructor(
				public type: string,
				public eventInitDict?: any
			) {}
		} as any;

		// CustomEvent 생성자 mock
		global.CustomEvent = class MockCustomEvent {
			constructor(
				public type: string,
				public eventInitDict?: any
			) {
				this.detail = eventInitDict?.detail;
			}
			detail: any;
		} as any;
	});

	afterEach(() => {
		// 정리
	});

	describe('getNotificationIcon 함수', () => {
		it('우선순위별 아이콘 경로를 올바르게 반환해야 함', () => {
			// 우선순위별 테스트
			expect(getNotificationIcon('low')).toBe('/icons/notification-low.png');
			expect(getNotificationIcon('normal')).toBe('/icons/notification.png');
			expect(getNotificationIcon('high')).toBe('/icons/notification-high.png');
			expect(getNotificationIcon('urgent')).toBe('/icons/notification-urgent.png');
		});

		it('타입별 아이콘이 우선순위보다 우선해야 함', () => {
			// 타입별 아이콘 테스트
			expect(getNotificationIcon('normal', 'system')).toBe('/icons/notification-system.png');
			expect(getNotificationIcon('high', 'warning')).toBe('/icons/notification-warning.png');
			expect(getNotificationIcon('low', 'success')).toBe('/icons/notification-success.png');
		});

		it('알 수 없는 우선순위의 경우 기본 아이콘을 반환해야 함', () => {
			expect(getNotificationIcon('unknown')).toBe('/icons/notification.png');
			expect(getNotificationIcon('')).toBe('/icons/notification.png');
		});

		it('알 수 없는 타입의 경우 우선순위별 아이콘을 반환해야 함', () => {
			expect(getNotificationIcon('high', 'unknown')).toBe('/icons/notification-high.png');
			expect(getNotificationIcon('urgent', 'nonexistent')).toBe('/icons/notification-urgent.png');
		});
	});

	describe('checkIconExists 함수', () => {
		it('함수가 Promise를 반환해야 함', () => {
			const result = checkIconExists('/icons/test.png');
			expect(result).toBeInstanceOf(Promise);
		});

		it('Image 객체를 생성하고 src를 설정해야 함', () => {
			// Image 생성자 mock
			const mockImage = {
				onload: null,
				onerror: null,
				src: ''
			};

			global.Image = function () {
				return mockImage;
			} as any;

			checkIconExists('/icons/test.png');
			expect(mockImage.src).toBe('/icons/test.png');
		});
	});

	describe('handleNotificationActionUrl 함수', () => {
		it('유효하지 않은 URL을 올바르게 처리해야 함', () => {
			// 빈 문자열
			expect(() => handleNotificationActionUrl('', 1)).not.toThrow();

			// null/undefined (타입 변환)
			expect(() => handleNotificationActionUrl(null as any, 1)).not.toThrow();
		});

		it('javascript: 프로토콜을 차단해야 함', () => {
			// 콘솔 에러 메시지 캡처를 위한 mock
			const consoleSpy = {
				error: [] as string[],
				warn: [] as string[],
				log: [] as string[]
			};

			global.console = {
				...global.console,
				error: (msg: string) => consoleSpy.error.push(msg),
				warn: (msg: string) => consoleSpy.warn.push(msg),
				log: (msg: string) => consoleSpy.log.push(msg)
			} as any;

			handleNotificationActionUrl('javascript:alert("xss")', 1);

			// 보안 에러 메시지가 출력되었는지 확인
			expect(consoleSpy.error.some((msg) => msg.includes('javascript: 프로토콜'))).toBe(true);
		});

		it('상대 경로를 절대 경로로 변환해야 함', () => {
			// 상대 경로 처리 테스트
			expect(() => handleNotificationActionUrl('/relative/path', 1)).not.toThrow();
			expect(() => handleNotificationActionUrl('./relative/path', 1)).not.toThrow();
		});

		it('외부 URL을 올바르게 식별해야 함', () => {
			// 외부 URL 처리 테스트
			expect(() => handleNotificationActionUrl('https://external.com/page', 1)).not.toThrow();
			expect(() => handleNotificationActionUrl('http://external.com/page', 1)).not.toThrow();
		});

		it('내부 URL을 올바르게 식별해야 함', () => {
			// 내부 URL 처리 테스트 (같은 origin)
			expect(() => handleNotificationActionUrl('https://example.com/internal', 1)).not.toThrow();
			expect(() => handleNotificationActionUrl('/internal/page', 1)).not.toThrow();
		});
	});

	describe('함수 통합 동작', () => {
		it('모든 주요 함수들이 정의되어 있어야 함', () => {
			expect(typeof getNotificationIcon).toBe('function');
			expect(typeof checkIconExists).toBe('function');
			expect(typeof handleNotificationActionUrl).toBe('function');
		});

		it('아이콘 경로 생성과 존재 확인이 연동되어야 함', async () => {
			// 아이콘 경로 생성
			const iconPath = getNotificationIcon('high', 'warning');
			expect(iconPath).toBe('/icons/notification-warning.png');

			// 존재 확인 함수 호출 (실제 파일 존재 여부와 관계없이 함수 동작 확인)
			const checkPromise = checkIconExists(iconPath);
			expect(checkPromise).toBeInstanceOf(Promise);
		});

		it('다양한 우선순위와 타입 조합이 올바르게 처리되어야 함', () => {
			const testCases = [
				{ priority: 'low', type: 'system', expected: '/icons/notification-system.png' },
				{
					priority: 'normal',
					type: 'maintenance',
					expected: '/icons/notification-maintenance.png'
				},
				{ priority: 'high', type: 'security', expected: '/icons/notification-security.png' },
				{ priority: 'urgent', type: 'error', expected: '/icons/notification-error.png' },
				{ priority: 'unknown', type: undefined, expected: '/icons/notification.png' },
				{ priority: 'high', type: 'unknown', expected: '/icons/notification-high.png' }
			];

			testCases.forEach(({ priority, type, expected }) => {
				const result = getNotificationIcon(priority, type);
				expect(result).toBe(expected);
			});
		});
	});

	describe('에러 처리', () => {
		it('잘못된 매개변수로 함수 호출 시 에러가 발생하지 않아야 함', () => {
			// getNotificationIcon 에러 처리
			expect(() => getNotificationIcon(undefined as any)).not.toThrow();
			expect(() => getNotificationIcon(null as any)).not.toThrow();
			expect(() => getNotificationIcon(123 as any)).not.toThrow();

			// checkIconExists 에러 처리
			expect(() => checkIconExists(undefined as any)).not.toThrow();
			expect(() => checkIconExists(null as any)).not.toThrow();

			// handleNotificationActionUrl 에러 처리
			expect(() => handleNotificationActionUrl(undefined as any, 1)).not.toThrow();
			expect(() => handleNotificationActionUrl(null as any, 1)).not.toThrow();
		});

		it('DOM 환경이 없어도 기본 동작이 가능해야 함', () => {
			// window 객체 제거
			const originalWindow = global.window;
			delete (global as any).window;

			// 함수들이 여전히 동작해야 함 (에러 없이)
			expect(() => getNotificationIcon('normal')).not.toThrow();
			expect(() => checkIconExists('/test.png')).not.toThrow();

			// window 객체 복원
			global.window = originalWindow;
		});
	});
});
