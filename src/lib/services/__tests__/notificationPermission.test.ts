/**
 * 알림 권한 관리 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	requestNotificationPermissionEnhanced as requestNotificationPermission,
	getNotificationPermissionStatusEnhanced as getNotificationPermissionStatus,
	isBrowserNotificationAvailable,
	canRequestPermission,
	getPermissionGuideMessage
} from '../notifications/permissions';

// 모킹 객체
const mockNotification = {
	permission: 'default' as NotificationPermission,
	requestPermission: vi.fn()
};

describe('알림 권한 관리', () => {
	beforeEach(() => {
		// 각 테스트 전에 모킹 초기화
		vi.clearAllMocks();
		mockNotification.permission = 'default';

		// window.Notification 모킹
		Object.defineProperty(global, 'window', {
			value: {
				Notification: mockNotification
			},
			writable: true,
			configurable: true
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('getNotificationPermissionStatus', () => {
		it('브라우저가 알림을 지원하지 않으면 denied를 반환해야 함', () => {
			// window 객체에서 Notification 제거
			Object.defineProperty(global, 'window', {
				value: {},
				writable: true,
				configurable: true
			});

			const status = getNotificationPermissionStatus();
			expect(status).toBe('denied');
		});

		it('브라우저가 알림을 지원하면 현재 권한 상태를 반환해야 함', () => {
			mockNotification.permission = 'granted';

			const status = getNotificationPermissionStatus();
			expect(status).toBe('granted');
		});
	});

	describe('isBrowserNotificationAvailable', () => {
		it('알림이 지원되고 권한이 부여된 경우 true를 반환해야 함', () => {
			mockNotification.permission = 'granted';

			const available = isBrowserNotificationAvailable();
			expect(available).toBe(true);
		});

		it('알림이 지원되지 않거나 권한이 없는 경우 false를 반환해야 함', () => {
			mockNotification.permission = 'denied';

			const available = isBrowserNotificationAvailable();
			expect(available).toBe(false);
		});
	});

	describe('canRequestPermission', () => {
		it('권한 상태가 default인 경우 true를 반환해야 함', () => {
			mockNotification.permission = 'default';

			const canRequest = canRequestPermission();
			expect(canRequest).toBe(true);
		});

		it('권한이 이미 부여되거나 거부된 경우 false를 반환해야 함', () => {
			mockNotification.permission = 'granted';

			let canRequest = canRequestPermission();
			expect(canRequest).toBe(false);

			mockNotification.permission = 'denied';
			canRequest = canRequestPermission();
			expect(canRequest).toBe(false);
		});
	});

	describe('requestNotificationPermission', () => {
		it('이미 권한이 부여된 경우 granted를 반환해야 함', async () => {
			mockNotification.permission = 'granted';

			const result = await requestNotificationPermission();
			expect(result).toBe('granted');
			expect(mockNotification.requestPermission).not.toHaveBeenCalled();
		});

		it('권한이 거부된 경우 denied를 반환해야 함', async () => {
			mockNotification.permission = 'denied';

			const result = await requestNotificationPermission();
			expect(result).toBe('denied');
			expect(mockNotification.requestPermission).not.toHaveBeenCalled();
		});

		it('권한 요청이 성공하면 granted를 반환해야 함', async () => {
			mockNotification.permission = 'default';
			mockNotification.requestPermission.mockResolvedValue('granted');

			const result = await requestNotificationPermission();
			expect(result).toBe('granted');
			expect(mockNotification.requestPermission).toHaveBeenCalled();
		});

		it('권한 요청이 거부되면 denied를 반환해야 함', async () => {
			mockNotification.permission = 'default';
			mockNotification.requestPermission.mockResolvedValue('denied');

			const result = await requestNotificationPermission();
			expect(result).toBe('denied');
			expect(mockNotification.requestPermission).toHaveBeenCalled();
		});

		it('권한 요청 중 오류가 발생하면 denied를 반환해야 함', async () => {
			mockNotification.permission = 'default';
			mockNotification.requestPermission.mockRejectedValue(new Error('권한 요청 실패'));

			const result = await requestNotificationPermission();
			expect(result).toBe('denied');
		});
	});

	describe('getPermissionGuideMessage', () => {
		it('권한이 부여된 경우 성공 메시지를 반환해야 함', () => {
			mockNotification.permission = 'granted';

			const message = getPermissionGuideMessage();
			expect(message.type).toBe('success');
			expect(message.actionRequired).toBe(false);
		});

		it('권한이 거부된 경우 경고 메시지를 반환해야 함', () => {
			mockNotification.permission = 'denied';

			const message = getPermissionGuideMessage();
			expect(message.type).toBe('warning');
			expect(message.actionRequired).toBe(true);
			expect(message.actionText).toBe('브라우저 설정 열기');
		});

		it('권한이 미설정인 경우 정보 메시지를 반환해야 함', () => {
			mockNotification.permission = 'default';

			const message = getPermissionGuideMessage();
			expect(message.type).toBe('info');
			expect(message.actionRequired).toBe(true);
			expect(message.actionText).toBe('알림 허용하기');
		});
	});
});

describe('알림 권한 관리 - 브라우저 미지원', () => {
	beforeEach(() => {
		// Notification API 미지원 환경 모킹
		Object.defineProperty(global, 'window', {
			value: {},
			writable: true,
			configurable: true
		});
	});

	it('브라우저가 알림을 지원하지 않으면 모든 함수가 적절히 처리해야 함', async () => {
		expect(getNotificationPermissionStatus()).toBe('denied');
		expect(isBrowserNotificationAvailable()).toBe(false);
		expect(canRequestPermission()).toBe(false);

		const result = await requestNotificationPermission();
		expect(result).toBe('denied');

		const message = getPermissionGuideMessage();
		expect(message.type).toBe('error');
		expect(message.actionRequired).toBe(false);
	});
});
