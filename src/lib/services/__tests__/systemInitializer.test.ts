/**
 * 시스템 초기화 함수 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	initializeSystem,
	cleanupSystem,
	getSystemStatus,
	restartSystem,
	initializePartialSystem,
	type SystemInitializationOptions
} from '../systemInitializer';

// 모킹
vi.mock('../indexedDBManager', () => ({
	initializeWithMigration: vi.fn().mockResolvedValue({
		name: 'EmployeeNotificationDB',
		version: 1,
		close: vi.fn()
	}),
	checkDatabaseHealth: vi.fn().mockResolvedValue(true),
	repairDatabase: vi.fn().mockResolvedValue({
		name: 'EmployeeNotificationDB',
		version: 1,
		close: vi.fn()
	})
}));

vi.mock('../sseConnection', () => ({
	connect: vi.fn().mockResolvedValue({
		readyState: 1,
		close: vi.fn()
	}),
	disconnect: vi.fn(),
	isOfflineMode: vi.fn().mockReturnValue(false)
}));

vi.mock('../storeUpdaters', () => ({
	initializeAllStores: vi.fn().mockResolvedValue(undefined),
	initializeSpecificStores: vi.fn().mockResolvedValue(undefined)
}));

vi.mock('$lib/stores', () => ({
	connectionStore: {
		set: vi.fn(),
		subscribe: vi.fn(() => vi.fn()),
		update: vi.fn()
	},
	networkStore: {
		set: vi.fn(),
		subscribe: vi.fn(() => vi.fn()),
		update: vi.fn()
	},
	sseStatsStore: {
		set: vi.fn(),
		subscribe: vi.fn(() => vi.fn()),
		update: vi.fn()
	}
}));

// Svelte의 get 함수도 모킹
vi.mock('svelte/store', () => ({
	get: vi.fn((store) => {
		if (store === require('$lib/stores').connectionStore) {
			return {
				status: 'disconnected',
				lastUpdate: null,
				error: null,
				reconnectAttempts: 0,
				maxReconnectAttempts: 5,
				reconnectDelay: 3000
			};
		}
		if (store === require('$lib/stores').networkStore) {
			return {
				isOnline: true,
				lastOnlineAt: new Date().toISOString(),
				lastOfflineAt: null
			};
		}
		if (store === require('$lib/stores').sseStatsStore) {
			return {
				totalMessagesReceived: 0,
				notificationsReceived: 0,
				dataUpdatesReceived: 0,
				errorsCount: 0,
				lastMessageAt: null,
				connectionUptime: 0,
				averageLatency: 0
			};
		}
		return {};
	})
}));

describe('시스템 초기화 함수', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(async () => {
		await cleanupSystem();
	});

	describe('initializeSystem', () => {
		it('기본 옵션으로 시스템을 성공적으로 초기화해야 함', async () => {
			const result = await initializeSystem();

			expect(result.success).toBe(true);
			expect(result.message).toContain('성공적으로 완료');
			expect(result.duration).toBeGreaterThan(0);
			expect(result.systemState).toBeDefined();
			expect(result.timestamp).toBeDefined();
		});

		it('진행상황 콜백을 호출해야 함', async () => {
			const progressCallback = vi.fn();

			await initializeSystem({}, progressCallback);

			expect(progressCallback).toHaveBeenCalledWith(
				expect.objectContaining({
					step: 'database',
					progress: expect.any(Number),
					message: expect.any(String)
				})
			);
		});

		it('커스텀 옵션을 적용해야 함', async () => {
			const options: SystemInitializationOptions = {
				databaseName: 'CustomDB',
				databaseVersion: 2,
				disableSSE: true,
				showProgress: false
			};

			const result = await initializeSystem(options);

			expect(result.success).toBe(true);
		});

		it('이미 초기화된 시스템에서는 재초기화를 건너뛰어야 함', async () => {
			// 첫 번째 초기화
			await initializeSystem();

			// 두 번째 초기화 (건너뛰어야 함)
			const result = await initializeSystem();

			expect(result.success).toBe(true);
			expect(result.message).toContain('이미 초기화');
		});

		it('강제 재초기화 옵션이 활성화된 경우 재초기화해야 함', async () => {
			// 첫 번째 초기화
			await initializeSystem();

			// 강제 재초기화
			const result = await initializeSystem({ forceReinitialize: true });

			expect(result.success).toBe(true);
		});
	});

	describe('cleanupSystem', () => {
		it('시스템을 정상적으로 정리해야 함', async () => {
			await initializeSystem();

			await expect(cleanupSystem()).resolves.not.toThrow();
		});
	});

	describe('getSystemStatus', () => {
		it('시스템 상태를 반환해야 함', async () => {
			const status = getSystemStatus();

			expect(status).toHaveProperty('isInitialized');
			expect(status).toHaveProperty('database');
			expect(status).toHaveProperty('sse');
			expect(status).toHaveProperty('network');
			expect(status).toHaveProperty('performance');
		});
	});

	describe('restartSystem', () => {
		it('시스템을 재시작해야 함', async () => {
			await initializeSystem();

			const result = await restartSystem();

			expect(result.success).toBe(true);
		});
	});

	describe('initializePartialSystem', () => {
		it('특정 컴포넌트만 초기화해야 함', async () => {
			const result = await initializePartialSystem(['database', 'stores']);

			expect(result.success).toBe(true);
			expect(result.message).toContain('부분 시스템 초기화 완료');
		});
	});

	describe('오류 처리', () => {
		it('데이터베이스 초기화 실패 시 오류를 반환해야 함', async () => {
			const { initializeWithMigration } = await import('../indexedDBManager');
			vi.mocked(initializeWithMigration).mockRejectedValueOnce(new Error('DB 초기화 실패'));

			const result = await initializeSystem({ enableRecovery: false });

			expect(result.success).toBe(false);
			expect(result.error).toBeDefined();
			expect(result.message).toContain('실패');
		});

		it('스토어 초기화 실패 시에도 계속 진행해야 함', async () => {
			const { initializeAllStores } = await import('../storeUpdaters');
			const partialError = new Error('부분 실패');
			partialError.name = 'PartialInitializationError';
			vi.mocked(initializeAllStores).mockRejectedValueOnce(partialError);

			const result = await initializeSystem();

			expect(result.success).toBe(true);
		});

		it('SSE 연결 실패 시에도 계속 진행해야 함', async () => {
			const { connect } = await import('../sseConnection');
			vi.mocked(connect).mockRejectedValueOnce(new Error('SSE 연결 실패'));

			const result = await initializeSystem();

			expect(result.success).toBe(true);
		});

		it('SSE 연결이 필수인 경우 실패 시 오류를 반환해야 함', async () => {
			const { connect } = await import('../sseConnection');
			vi.mocked(connect).mockRejectedValueOnce(new Error('SSE 연결 실패'));

			const result = await initializeSystem({
				requireSSEConnection: true,
				enableRecovery: false
			});

			expect(result.success).toBe(false);
		});
	});

	describe('복구 로직', () => {
		it('초기화 실패 시 복구를 시도해야 함', async () => {
			const { initializeWithMigration } = await import('../indexedDBManager');

			// 첫 번째 시도는 실패, 두 번째 시도부터는 성공
			let callCount = 0;
			vi.mocked(initializeWithMigration).mockImplementation(() => {
				callCount++;
				if (callCount === 1) {
					return Promise.reject(new Error('첫 번째 실패'));
				}
				return Promise.resolve({
					name: 'EmployeeNotificationDB',
					version: 1,
					close: vi.fn()
				} as any);
			});

			const result = await initializeSystem();

			expect(result.success).toBe(true);
		});

		it('복구가 비활성화된 경우 복구를 시도하지 않아야 함', async () => {
			const { initializeWithMigration } = await import('../indexedDBManager');

			// 모든 시도에서 실패하도록 설정
			vi.mocked(initializeWithMigration).mockRejectedValue(new Error('DB 실패'));

			const result = await initializeSystem({ enableRecovery: false });

			expect(result.success).toBe(false);
		});
	});
});
