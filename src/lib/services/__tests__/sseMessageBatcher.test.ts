/**
 * SSE 메시지 배치 처리 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	addMessageToBatch,
	flushBatch,
	cancelBatch,
	getBatchStatus,
	updateBatchConfig,
	getBatchStats,
	resetBatchStats
} from '../sseMessageBatcher';
import type { SseMessage } from '../../types/sseTypes';

// 메시지 라우터 모킹
vi.mock('../sseMessageRouter', () => ({
	routeMessage: vi.fn(),
	handleDataUpdateMessage: vi.fn()
}));

describe('SSE Message Batcher', () => {
	beforeEach(() => {
		// 타이머 모킹
		vi.useFakeTimers();

		// 배치 상태 초기화
		cancelBatch();
		resetBatchStats();

		// 기본 설정으로 리셋
		updateBatchConfig({ batchSize: 10, batchDelay: 100 });
	});

	afterEach(() => {
		vi.useRealTimers();
		vi.clearAllMocks();
		cancelBatch();
	});

	describe('Basic Batching', () => {
		it('메시지를 배치에 추가해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1, title: '테스트', message: '테스트 메시지' }
			};

			addMessageToBatch(message);

			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(1);
			expect(status.isProcessing).toBe(true);
		});

		it('배치 크기에 도달하면 자동으로 처리해야 함', () => {
			// 배치 크기를 3으로 설정
			updateBatchConfig({ batchSize: 3 });

			const messages: SseMessage[] = [
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 2 } },
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 3 } }
			];

			// 메시지 추가
			messages.forEach((message) => addMessageToBatch(message));

			// 배치가 자동으로 처리되어야 함
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});

		it('지연 시간 후 배치를 처리해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);

			// 아직 처리되지 않음
			let status = getBatchStatus();
			expect(status.queuedMessages).toBe(1);

			// 지연 시간 경과
			vi.advanceTimersByTime(150);

			// 배치가 처리되어야 함
			status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});

	describe('Message Type Grouping', () => {
		it('메시지 타입별로 그룹화해야 함', () => {
			const messages: SseMessage[] = [
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'data_update', timestamp: new Date().toISOString(), data: { model: 'employees' } },
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 2 } },
				{ type: 'heartbeat', timestamp: new Date().toISOString(), data: {} }
			];

			// 배치 크기를 4로 설정하여 모든 메시지가 한 번에 처리되도록
			updateBatchConfig({ batchSize: 4 });

			messages.forEach((message) => addMessageToBatch(message));

			// 배치가 처리되었는지 확인
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});

	describe('Data Update Deduplication', () => {
		it('중복된 데이터 업데이트를 제거해야 함', () => {
			const baseTime = new Date();
			const messages: SseMessage[] = [
				{
					type: 'data_update',
					timestamp: new Date(baseTime.getTime()).toISOString(),
					data: {
						model: 'employees',
						action: 'updated',
						payload: { id: 1, name: '홍길동' },
						affected_ids: [1]
					}
				},
				{
					type: 'data_update',
					timestamp: new Date(baseTime.getTime() + 1000).toISOString(), // 1초 후
					data: {
						model: 'employees',
						action: 'updated',
						payload: { id: 1, name: '홍길동 수정' },
						affected_ids: [1]
					}
				}
			];

			updateBatchConfig({ batchSize: 2 });
			messages.forEach((message) => addMessageToBatch(message));

			// 최신 업데이트만 처리되어야 함
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});

	describe('Notification Priority Sorting', () => {
		it('알림을 우선순위별로 정렬해야 함', () => {
			const messages: SseMessage[] = [
				{
					type: 'notification',
					timestamp: new Date().toISOString(),
					data: { id: 1, priority: 'low', title: '낮은 우선순위' }
				},
				{
					type: 'notification',
					timestamp: new Date().toISOString(),
					data: { id: 2, priority: 'urgent', title: '긴급' }
				},
				{
					type: 'notification',
					timestamp: new Date().toISOString(),
					data: { id: 3, priority: 'normal', title: '일반' }
				}
			];

			updateBatchConfig({ batchSize: 3 });
			messages.forEach((message) => addMessageToBatch(message));

			// 우선순위 순으로 처리되어야 함 (urgent > normal > low)
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});

	describe('Batch Control', () => {
		it('강제 배치 처리가 동작해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);

			// 아직 처리되지 않음
			let status = getBatchStatus();
			expect(status.queuedMessages).toBe(1);

			// 강제 처리
			flushBatch();

			// 배치가 처리되어야 함
			status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});

		it('배치 취소가 동작해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);

			// 배치 취소
			cancelBatch();

			// 큐가 비워져야 함
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
			expect(status.isProcessing).toBe(false);
		});
	});

	describe('Configuration', () => {
		it('배치 설정을 업데이트할 수 있어야 함', () => {
			const newConfig = { batchSize: 20, batchDelay: 200 };
			updateBatchConfig(newConfig);

			const status = getBatchStatus();
			expect(status.config.batchSize).toBe(20);
			expect(status.config.batchDelay).toBe(200);
		});

		it('부분 설정 업데이트가 동작해야 함', () => {
			// 기본 설정 확인
			let status = getBatchStatus();
			const originalDelay = status.config.batchDelay;

			// 배치 크기만 변경
			updateBatchConfig({ batchSize: 15 });

			status = getBatchStatus();
			expect(status.config.batchSize).toBe(15);
			expect(status.config.batchDelay).toBe(originalDelay); // 기존 값 유지
		});
	});

	describe('Statistics', () => {
		it('배치 통계를 추적해야 함', () => {
			const messages: SseMessage[] = [
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 2 } }
			];

			updateBatchConfig({ batchSize: 2 });
			messages.forEach((message) => addMessageToBatch(message));

			const stats = getBatchStats();
			expect(stats.totalBatches).toBeGreaterThan(0);
			expect(stats.totalMessages).toBe(2);
		});

		it('통계를 초기화할 수 있어야 함', () => {
			// 일부 메시지 처리
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);
			flushBatch();

			// 통계 초기화
			resetBatchStats();

			const stats = getBatchStats();
			expect(stats.totalBatches).toBe(0);
			expect(stats.totalMessages).toBe(0);
		});
	});

	describe('Performance', () => {
		it('대량 메시지 배치 처리 성능', () => {
			const messageCount = 100;
			const messages: SseMessage[] = [];

			// 대량 메시지 생성
			for (let i = 0; i < messageCount; i++) {
				messages.push({
					type: 'notification',
					timestamp: new Date().toISOString(),
					data: { id: i, title: `메시지 ${i}` }
				});
			}

			const startTime = performance.now();

			// 배치 크기를 크게 설정하여 한 번에 처리
			updateBatchConfig({ batchSize: messageCount });
			messages.forEach((message) => addMessageToBatch(message));

			const endTime = performance.now();
			const processingTime = endTime - startTime;

			// 합리적인 시간 내에 처리되어야 함
			expect(processingTime).toBeLessThan(1000);

			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});

	describe('Edge Cases', () => {
		it('빈 배치 처리를 안전하게 처리해야 함', () => {
			// 빈 상태에서 강제 처리
			flushBatch();

			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});

		it('배치 크기가 1일 때 즉시 처리해야 함', () => {
			updateBatchConfig({ batchSize: 1 });

			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);

			// 즉시 처리되어야 함
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});

		it('지연 시간이 0일 때 즉시 처리해야 함', () => {
			updateBatchConfig({ batchDelay: 0 });

			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToBatch(message);

			// 타이머 진행 없이도 처리되어야 함
			const status = getBatchStatus();
			expect(status.queuedMessages).toBe(0);
		});
	});
});
