/**
 * Svelte 스토어 업데이트 함수 테스트
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
	connectionStore,
	allNotificationsStore,
	categoryStore,
	employeeStore,
	groupStore,
	repairGradeStore,
	updateMessageStore,
	highlightedItemsStore,
	updatedItemsStore,
	unreadNotificationCount,
	connectionSummary,
	dataLoadingState
} from '../../stores';

import {
	updateConnectionStatus,
	incrementReconnectAttempts,
	updateNetworkStatus,
	addUpdateMessage,
	addHighlightEffect,
	clearAllHighlights,
	resetAllStores
} from '../../stores';

// IndexedDB 모킹
const mockDb = {
	transaction: vi.fn(),
	objectStore: vi.fn()
} as any;

// getAllData 함수 모킹
vi.mock('../indexedDBManager', () => ({
	getAllData: vi.fn(),
	getData: vi.fn()
}));

import * as indexedDBManager from '../indexedDBManager';
import {
	updateNotificationStore,
	updateCategoryStore,
	updateEmployeeStore,
	updateGroupStore,
	updateRepairGradeStore,
	initializeAllStores
} from '../storeUpdaters';
import { validateStoreData } from '../../stores';

const mockGetAllData = indexedDBManager.getAllData as any;
const mockGetData = indexedDBManager.getData as any;

describe('스토어 정의 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	it('모든 스토어가 올바르게 정의되어야 함', () => {
		expect(get(connectionStore)).toEqual({
			status: 'disconnected',
			lastUpdate: null,
			error: null,
			reconnectAttempts: 0,
			maxReconnectAttempts: 5,
			reconnectDelay: 3000
		});

		expect(get(allNotificationsStore)).toEqual([]);
		expect(get(categoryStore)).toEqual({
			cate4: [],
			cate5: [],
			lastUpdated: null,
			totalCount: 0
		});
		expect(get(employeeStore)).toEqual([]);
		expect(get(groupStore)).toEqual([]);
		expect(get(repairGradeStore)).toEqual([]);
	});
});

describe('연결 상태 관리 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	it('연결 상태를 올바르게 업데이트해야 함', () => {
		updateConnectionStatus('connecting');

		const state = get(connectionStore);
		expect(state.status).toBe('connecting');
		expect(state.lastUpdate).toBeTruthy();
		expect(state.error).toBeNull();
	});

	it('오류와 함께 연결 상태를 업데이트해야 함', () => {
		const errorMessage = '연결 실패';
		updateConnectionStatus('error', errorMessage);

		const state = get(connectionStore);
		expect(state.status).toBe('error');
		expect(state.error).toBe(errorMessage);
	});

	it('재연결 시도 횟수를 증가시켜야 함', () => {
		const attempts = incrementReconnectAttempts();

		expect(attempts).toBe(1);
		expect(get(connectionStore).reconnectAttempts).toBe(1);
	});

	it('연결 성공 시 재연결 시도 횟수를 리셋해야 함', () => {
		incrementReconnectAttempts();
		incrementReconnectAttempts();

		updateConnectionStatus('connected');

		expect(get(connectionStore).reconnectAttempts).toBe(0);
	});
});

describe('네트워크 상태 관리 테스트', () => {
	it('온라인 상태를 업데이트해야 함', () => {
		updateNetworkStatus(true);
		// 네트워크 스토어는 내부적으로 관리되므로 함수 호출만 확인
		expect(updateNetworkStatus).toBeDefined();
	});
});

describe('업데이트 메시지 관리 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	it('업데이트 메시지를 추가해야 함', () => {
		addUpdateMessage('테스트 메시지', 'success');

		const messages = get(updateMessageStore);
		expect(messages).toHaveLength(1);
		expect(messages[0].message).toBe('테스트 메시지');
		expect(messages[0].type).toBe('success');
		expect(messages[0].id).toBeTruthy();
	});

	it('최대 10개의 메시지만 유지해야 함', () => {
		// 15개 메시지 추가
		for (let i = 0; i < 15; i++) {
			addUpdateMessage(`메시지 ${i}`, 'info');
		}

		const messages = get(updateMessageStore);
		expect(messages).toHaveLength(10);
	});
});

describe('하이라이트 효과 관리 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	it('하이라이트 효과를 추가해야 함', () => {
		addHighlightEffect([1, 2, 3], 'new');

		const highlighted = get(highlightedItemsStore);
		expect(highlighted.has(1)).toBe(true);
		expect(highlighted.has(2)).toBe(true);
		expect(highlighted.has(3)).toBe(true);
	});

	it('모든 하이라이트 효과를 제거해야 함', () => {
		addHighlightEffect([1, 2, 3], 'new');
		addHighlightEffect([4, 5, 6], 'updated');

		clearAllHighlights();

		expect(get(highlightedItemsStore).size).toBe(0);
		expect(get(updatedItemsStore).size).toBe(0);
	});
});

describe('파생 스토어 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	it('읽지 않은 알림 개수를 올바르게 계산해야 함', () => {
		// 알림 데이터 설정
		allNotificationsStore.set([
			{
				id: 1,
				title: '알림1',
				message: '내용1',
				type: 'info',
				priority: 'normal',
				read: false,
				created_at: '2024-01-01',
				received_at: '2024-01-01'
			},
			{
				id: 2,
				title: '알림2',
				message: '내용2',
				type: 'info',
				priority: 'high',
				read: true,
				created_at: '2024-01-01',
				received_at: '2024-01-01'
			},
			{
				id: 3,
				title: '알림3',
				message: '내용3',
				type: 'warning',
				priority: 'urgent',
				read: false,
				created_at: '2024-01-01',
				received_at: '2024-01-01'
			}
		]);

		expect(get(unreadNotificationCount)).toBe(2);
	});

	it('연결 상태 요약을 올바르게 생성해야 함', () => {
		updateConnectionStatus('connected');

		const summary = get(connectionSummary);
		expect(summary.isConnected).toBe(true);
		expect(summary.statusText).toBe('연결됨');
		expect(summary.canReconnect).toBe(false);
	});

	it('데이터 로딩 상태를 올바르게 계산해야 함', () => {
		// 카테고리 데이터 설정
		categoryStore.set({
			cate4: [{ id: 1, name: '카테고리1' }],
			cate5: [],
			lastUpdated: '2024-01-01',
			totalCount: 1
		});

		const loadingState = get(dataLoadingState);
		expect(loadingState.loadedStores).toContain('category');
		expect(loadingState.loadedStores).toContain('notification');
	});
});

describe('스토어 리셋 테스트', () => {
	it('모든 스토어를 초기 상태로 리셋해야 함', () => {
		// 일부 데이터 설정
		updateConnectionStatus('connected');
		addUpdateMessage('테스트', 'info');
		addHighlightEffect([1, 2], 'new');

		// 리셋 실행
		resetAllStores();

		// 모든 스토어가 초기 상태인지 확인
		expect(get(connectionStore).status).toBe('disconnected');
		expect(get(updateMessageStore)).toHaveLength(0);
		expect(get(highlightedItemsStore).size).toBe(0);
	});
});

describe('개별 스토어 업데이트 함수 테스트', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		resetAllStores();
	});

	describe('updateNotificationStore', () => {
		it('알림 데이터를 올바르게 로드하고 정렬해야 함', async () => {
			const mockNotifications = [
				{
					id: 1,
					title: '알림 1',
					message: '메시지 1',
					received_at: '2024-01-01T10:00:00Z',
					read: false
				},
				{
					id: 2,
					title: '알림 2',
					message: '메시지 2',
					received_at: '2024-01-01T11:00:00Z',
					read: true
				}
			];

			mockGetAllData.mockResolvedValue(mockNotifications);

			await updateNotificationStore(mockDb);

			const storeValue = get(allNotificationsStore);
			expect(storeValue).toHaveLength(2);
			expect(storeValue[0].id).toBe(2); // 최신 알림이 먼저
			expect(storeValue[1].id).toBe(1);
		});

		it('유효하지 않은 알림 데이터를 필터링해야 함', async () => {
			const mockNotifications = [
				{
					id: 1,
					title: '유효한 알림',
					message: '메시지',
					received_at: '2024-01-01T10:00:00Z'
				},
				{
					title: '유효하지 않은 알림',
					message: '메시지'
				},
				{
					id: 3,
					message: '메시지'
				}
			];

			mockGetAllData.mockResolvedValue(mockNotifications);

			await updateNotificationStore(mockDb);

			const storeValue = get(allNotificationsStore);
			expect(storeValue).toHaveLength(1);
			expect(storeValue[0].id).toBe(1);
		});
	});

	describe('updateCategoryStore', () => {
		it('카테고리 데이터를 올바르게 로드해야 함', async () => {
			const mockCategoryData = {
				key: 'main',
				cate4: [
					{ id: 1, name: '카테고리 4-1' },
					{ id: 2, name: '카테고리 4-2' }
				],
				cate5: [{ id: 1, name: '카테고리 5-1' }],
				updated_at: '2024-01-01T10:00:00Z'
			};

			mockGetData.mockResolvedValue(mockCategoryData);

			await updateCategoryStore(mockDb);

			const storeValue = get(categoryStore);
			expect(storeValue.cate4).toHaveLength(2);
			expect(storeValue.cate5).toHaveLength(1);
			expect(storeValue.totalCount).toBe(3);
		});
	});

	describe('updateEmployeeStore', () => {
		it('직원 데이터를 이름순으로 정렬해야 함', async () => {
			const mockEmployees = [
				{ id: 1, name: '홍길동', status: 'active' },
				{ id: 2, name: '김철수', status: 'active' },
				{ id: 3, name: '이영희', status: 'inactive' }
			];

			mockGetAllData.mockResolvedValue(mockEmployees);

			await updateEmployeeStore(mockDb);

			const storeValue = get(employeeStore);
			expect(storeValue).toHaveLength(3);
			expect(storeValue[0].name).toBe('김철수');
			expect(storeValue[1].name).toBe('이영희');
			expect(storeValue[2].name).toBe('홍길동');
		});
	});

	describe('initializeAllStores', () => {
		it('모든 스토어를 성공적으로 초기화해야 함', async () => {
			mockGetAllData.mockResolvedValue([]);
			mockGetData.mockResolvedValue(null);

			await expect(initializeAllStores(mockDb, false)).resolves.not.toThrow();
		});
	});
});

describe('스토어 유틸리티 함수 테스트', () => {
	beforeEach(() => {
		resetAllStores();
	});

	describe('validateStoreData', () => {
		it('유효한 스토어 데이터에 대해 true를 반환해야 함', () => {
			allNotificationsStore.set([{ id: 1, title: '테스트', message: '메시지', read: false }]);
			categoryStore.set({ cate4: [], cate5: [], lastUpdated: null, totalCount: 0 });
			employeeStore.set([{ id: 1, name: '홍길동' }]);
			groupStore.set([{ id: 1, name: '개발팀' }]);

			const result = validateStoreData();

			expect(result.isValid).toBe(true);
			expect(result.errors).toHaveLength(0);
		});
	});
});
