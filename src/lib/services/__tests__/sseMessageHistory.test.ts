/**
 * SSE 메시지 히스토리 관리 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	startHistoryManagement,
	stopHistoryManagement,
	addMessageToHistory,
	getRecentMessages,
	getMessagesByType,
	getMessagesByTimeRange,
	searchMessages,
	getHistoryStats,
	updateHistoryConfig,
	getHistoryConfig,
	clearHistory,
	forceCleanup,
	exportHistory,
	importHistory
} from '../sseMessageHistory';
import type { SseMessage } from '../../types/sseTypes';

describe('SSE Message History', () => {
	beforeEach(() => {
		// 타이머 모킹
		vi.useFakeTimers();

		// 히스토리 초기화
		clearHistory();

		// 기본 설정으로 시작
		startHistoryManagement({
			maxHistorySize: 100,
			cleanupInterval: 60000, // 1분
			maxAge: 3600000 // 1시간
		});
	});

	afterEach(() => {
		stopHistoryManagement();
		vi.useRealTimers();
		vi.clearAllMocks();
	});

	describe('History Management Lifecycle', () => {
		it('히스토리 관리를 시작하고 중지할 수 있어야 함', () => {
			// 이미 beforeEach에서 시작됨
			expect(true).toBe(true);

			stopHistoryManagement();
			expect(true).toBe(true);
		});

		it('사용자 정의 설정으로 시작할 수 있어야 함', () => {
			stopHistoryManagement();

			const customConfig = {
				maxHistorySize: 500,
				cleanupInterval: 120000,
				maxAge: 7200000
			};

			startHistoryManagement(customConfig);

			const config = getHistoryConfig();
			expect(config.maxHistorySize).toBe(500);
			expect(config.cleanupInterval).toBe(120000);
			expect(config.maxAge).toBe(7200000);
		});
	});

	describe('Message Storage', () => {
		it('메시지를 히스토리에 추가할 수 있어야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1, title: '테스트', message: '테스트 메시지' }
			};

			addMessageToHistory(message);

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(1);
		});

		it('여러 메시지를 순서대로 저장해야 함', () => {
			const messages: SseMessage[] = [
				{
					type: 'notification',
					timestamp: new Date(Date.now() - 2000).toISOString(),
					data: { id: 1, title: '첫 번째' }
				},
				{
					type: 'notification',
					timestamp: new Date(Date.now() - 1000).toISOString(),
					data: { id: 2, title: '두 번째' }
				},
				{
					type: 'notification',
					timestamp: new Date().toISOString(),
					data: { id: 3, title: '세 번째' }
				}
			];

			messages.forEach((message) => addMessageToHistory(message));

			const recentMessages = getRecentMessages(3);
			expect(recentMessages).toHaveLength(3);
			expect(recentMessages[0].data.title).toBe('첫 번째');
			expect(recentMessages[2].data.title).toBe('세 번째');
		});
	});

	describe('Size Management', () => {
		it('최대 크기 초과 시 오래된 메시지를 제거해야 함', () => {
			// 최대 크기를 5로 설정
			updateHistoryConfig({ maxHistorySize: 5 });

			// 10개 메시지 추가
			for (let i = 0; i < 10; i++) {
				const message: SseMessage = {
					type: 'notification',
					timestamp: new Date(Date.now() + i * 1000).toISOString(),
					data: { id: i, title: `메시지 ${i}` }
				};
				addMessageToHistory(message);
			}

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(5);

			// 최근 5개만 남아있어야 함
			const recentMessages = getRecentMessages(10);
			expect(recentMessages).toHaveLength(5);
			expect(recentMessages[0].data.id).toBe(5); // 인덱스 5부터
			expect(recentMessages[4].data.id).toBe(9); // 인덱스 9까지
		});
	});

	describe('Message Retrieval', () => {
		beforeEach(() => {
			// 테스트용 메시지들 추가
			const baseTime = Date.now();
			const testMessages: SseMessage[] = [
				{
					type: 'notification',
					timestamp: new Date(baseTime - 5000).toISOString(),
					data: { id: 1, title: '알림 1', priority: 'normal' }
				},
				{
					type: 'data_update',
					timestamp: new Date(baseTime - 4000).toISOString(),
					data: { model: 'employees', action: 'created' }
				},
				{
					type: 'notification',
					timestamp: new Date(baseTime - 3000).toISOString(),
					data: { id: 2, title: '알림 2', priority: 'high' }
				},
				{
					type: 'heartbeat',
					timestamp: new Date(baseTime - 2000).toISOString(),
					data: { status: 'alive' }
				},
				{
					type: 'notification',
					timestamp: new Date(baseTime - 1000).toISOString(),
					data: { id: 3, title: '중요한 알림', priority: 'urgent' }
				}
			];

			testMessages.forEach((message) => addMessageToHistory(message));
		});

		it('최근 메시지를 조회할 수 있어야 함', () => {
			const recentMessages = getRecentMessages(3);
			expect(recentMessages).toHaveLength(3);

			// 최근 3개 메시지 (시간순)
			expect(recentMessages[0].type).toBe('data_update');
			expect(recentMessages[1].type).toBe('notification');
			expect(recentMessages[2].type).toBe('notification');
		});

		it('특정 타입의 메시지를 조회할 수 있어야 함', () => {
			const notifications = getMessagesByType('notification', 10);
			expect(notifications).toHaveLength(3);

			notifications.forEach((message) => {
				expect(message.type).toBe('notification');
			});
		});

		it('시간 범위로 메시지를 조회할 수 있어야 함', () => {
			const baseTime = Date.now();
			const startTime = new Date(baseTime - 4500);
			const endTime = new Date(baseTime - 2500);

			const messagesInRange = getMessagesByTimeRange(startTime, endTime);
			expect(messagesInRange).toHaveLength(2); // data_update와 notification
		});

		it('메시지를 검색할 수 있어야 함', () => {
			const searchResults = searchMessages('중요한', 10);
			expect(searchResults).toHaveLength(1);
			expect(searchResults[0].data.title).toBe('중요한 알림');
		});

		it('대소문자 구분 없이 검색해야 함', () => {
			const searchResults = searchMessages('알림', 10);
			expect(searchResults.length).toBeGreaterThan(0);
		});
	});

	describe('Statistics', () => {
		it('히스토리 통계를 제공해야 함', () => {
			const messages: SseMessage[] = [
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 2 } },
				{ type: 'data_update', timestamp: new Date().toISOString(), data: { model: 'test' } }
			];

			messages.forEach((message) => addMessageToHistory(message));

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(3);
			expect(stats.messagesByType['notification']).toBe(2);
			expect(stats.messagesByType['data_update']).toBe(1);
			expect(stats.oldestMessage).toBeDefined();
			expect(stats.newestMessage).toBeDefined();
			expect(stats.memoryUsage).toBeGreaterThan(0);
		});
	});

	describe('Cleanup', () => {
		it('오래된 메시지를 자동으로 정리해야 함', () => {
			const oldTime = Date.now() - 7200000; // 2시간 전
			const recentTime = Date.now() - 1800000; // 30분 전

			const messages: SseMessage[] = [
				{
					type: 'notification',
					timestamp: new Date(oldTime).toISOString(),
					data: { id: 1, title: '오래된 메시지' }
				},
				{
					type: 'notification',
					timestamp: new Date(recentTime).toISOString(),
					data: { id: 2, title: '최근 메시지' }
				}
			];

			messages.forEach((message) => addMessageToHistory(message));

			// 강제 정리 실행
			forceCleanup();

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(1); // 최근 메시지만 남음

			const recentMessages = getRecentMessages(10);
			expect(recentMessages[0].data.title).toBe('최근 메시지');
		});

		it('주기적 정리가 동작해야 함', () => {
			const oldTime = Date.now() - 7200000; // 2시간 전

			const oldMessage: SseMessage = {
				type: 'notification',
				timestamp: new Date(oldTime).toISOString(),
				data: { id: 1, title: '오래된 메시지' }
			};

			addMessageToHistory(oldMessage);

			// 정리 간격만큼 시간 진행
			vi.advanceTimersByTime(60000);

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(0); // 오래된 메시지 제거됨
		});
	});

	describe('Configuration', () => {
		it('히스토리 설정을 업데이트할 수 있어야 함', () => {
			const newConfig = {
				maxHistorySize: 200,
				cleanupInterval: 30000,
				maxAge: 1800000
			};

			updateHistoryConfig(newConfig);

			const config = getHistoryConfig();
			expect(config.maxHistorySize).toBe(200);
			expect(config.cleanupInterval).toBe(30000);
			expect(config.maxAge).toBe(1800000);
		});

		it('부분 설정 업데이트가 동작해야 함', () => {
			const originalConfig = getHistoryConfig();

			updateHistoryConfig({ maxHistorySize: 300 });

			const updatedConfig = getHistoryConfig();
			expect(updatedConfig.maxHistorySize).toBe(300);
			expect(updatedConfig.cleanupInterval).toBe(originalConfig.cleanupInterval);
			expect(updatedConfig.maxAge).toBe(originalConfig.maxAge);
		});
	});

	describe('Import/Export', () => {
		it('히스토리를 내보낼 수 있어야 함', () => {
			const messages: SseMessage[] = [
				{ type: 'notification', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'data_update', timestamp: new Date().toISOString(), data: { model: 'test' } }
			];

			messages.forEach((message) => addMessageToHistory(message));

			const exportedHistory = exportHistory();
			expect(exportedHistory).toHaveLength(2);
			expect(exportedHistory[0].type).toBe('notification');
			expect(exportedHistory[1].type).toBe('data_update');
		});

		it('히스토리를 가져올 수 있어야 함', () => {
			const importMessages: SseMessage[] = [
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 2 } }
			];

			importHistory(importMessages);

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(2);
			expect(stats.messagesByType['imported']).toBe(2);
		});

		it('가져온 히스토리가 크기 제한을 적용해야 함', () => {
			updateHistoryConfig({ maxHistorySize: 3 });

			const importMessages: SseMessage[] = [
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 1 } },
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 2 } },
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 3 } },
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 4 } },
				{ type: 'imported', timestamp: new Date().toISOString(), data: { id: 5 } }
			];

			importHistory(importMessages);

			const stats = getHistoryStats();
			expect(stats.totalMessages).toBe(3); // 최대 크기만큼만 유지
		});
	});

	describe('Edge Cases', () => {
		it('빈 히스토리에서 조회 시 빈 배열을 반환해야 함', () => {
			clearHistory();

			expect(getRecentMessages(10)).toHaveLength(0);
			expect(getMessagesByType('notification', 10)).toHaveLength(0);
			expect(searchMessages('test', 10)).toHaveLength(0);
		});

		it('존재하지 않는 타입 조회 시 빈 배열을 반환해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToHistory(message);

			const results = getMessagesByType('nonexistent', 10);
			expect(results).toHaveLength(0);
		});

		it('잘못된 시간 범위 조회를 안전하게 처리해야 함', () => {
			const message: SseMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: { id: 1 }
			};

			addMessageToHistory(message);

			// 시작 시간이 종료 시간보다 늦은 경우
			const startTime = new Date(Date.now() + 1000);
			const endTime = new Date(Date.now() - 1000);

			const results = getMessagesByTimeRange(startTime, endTime);
			expect(results).toHaveLength(0);
		});
	});
});
