/**
 * SSE 재연결 및 오류 복구 로직 테스트
 *
 * 요구사항 1.4: 재연결 및 오류 복구 기능 검증
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	calculateReconnectDelay,
	setReconnectionStrategy,
	reconnectionStrategies
} from '../sseConnection';

// 모킹
vi.mock('../sseConnectionState', () => ({
	updateConnectionStatus: vi.fn(),
	getCurrentConnectionState: vi.fn(() => ({
		status: 'disconnected',
		reconnectAttempts: 0,
		lastError: null
	})),
	incrementReconnectAttempts: vi.fn(),
	emitConnectionStatusChange: vi.fn()
}));

vi.mock('../sseStats', () => ({
	recordReconnection: vi.fn(),
	recordError: vi.fn()
}));

vi.mock('../sseMessageRouter', () => ({
	routeMessage: vi.fn()
}));

vi.mock('../indexedDBManager', () => ({
	initializeIndexedDB: vi.fn(),
	getAllData: vi.fn(() => Promise.resolve([])),
	clearStore: vi.fn()
}));

vi.mock('../notificationHandler', () => ({
	handleNotificationMessage: vi.fn()
}));

describe('SSE 재연결 및 오류 복구 로직', () => {
	beforeEach(() => {
		vi.useFakeTimers();
		vi.clearAllMocks();
		vi.clearAllTimers();

		// EventSource 모킹
		global.EventSource = vi.fn().mockImplementation(() => ({
			close: vi.fn(),
			readyState: 0,
			addEventListener: vi.fn(),
			removeEventListener: vi.fn(),
			url: 'http://test.com/sse'
		}));

		// localStorage 모킹
		Object.defineProperty(window, 'localStorage', {
			value: {
				getItem: vi.fn(),
				setItem: vi.fn(),
				removeItem: vi.fn()
			},
			writable: true
		});

		// navigator 모킹
		Object.defineProperty(navigator, 'onLine', {
			writable: true,
			value: true
		});
	});

	afterEach(() => {
		vi.useRealTimers();
	});

	describe('calculateReconnectDelay 함수', () => {
		it('지수 백오프 알고리즘을 올바르게 계산해야 함', () => {
			const strategy = reconnectionStrategies.normal;

			// 첫 번째 시도: baseInterval * (backoffMultiplier ^ 0) = 1000 * 1 = 1000ms
			const delay1 = calculateReconnectDelay(1, strategy);
			expect(delay1).toBeGreaterThanOrEqual(1000);
			expect(delay1).toBeLessThan(2000); // 지터 고려

			// 두 번째 시도: baseInterval * (backoffMultiplier ^ 1) = 1000 * 2 = 2000ms
			const delay2 = calculateReconnectDelay(2, strategy);
			expect(delay2).toBeGreaterThanOrEqual(2000);
			expect(delay2).toBeLessThan(3000); // 지터 고려

			// 세 번째 시도: baseInterval * (backoffMultiplier ^ 2) = 1000 * 4 = 4000ms
			const delay3 = calculateReconnectDelay(3, strategy);
			expect(delay3).toBeGreaterThanOrEqual(4000);
			expect(delay3).toBeLessThan(5000); // 지터 고려
		});

		it('최대 지연시간을 초과하지 않아야 함', () => {
			const strategy = reconnectionStrategies.normal;

			// 매우 큰 시도 횟수로 테스트
			const delay = calculateReconnectDelay(20, strategy);
			expect(delay).toBeLessThanOrEqual(strategy.maxInterval + 1000); // 지터 고려
		});

		it('최소 지연시간을 보장해야 함', () => {
			const customStrategy = {
				...reconnectionStrategies.normal,
				baseInterval: 50 // 매우 작은 값
			};

			const delay = calculateReconnectDelay(1, customStrategy);
			expect(delay).toBeGreaterThanOrEqual(100); // 최소 100ms 보장
		});

		it('지터가 비활성화된 경우 정확한 값을 반환해야 함', () => {
			const strategy = {
				...reconnectionStrategies.conservative,
				jitterEnabled: false
			};

			const delay = calculateReconnectDelay(1, strategy);
			expect(delay).toBe(strategy.baseInterval);
		});
	});

	describe('재연결 전략', () => {
		it('재연결 전략을 올바르게 설정해야 함', () => {
			setReconnectionStrategy('aggressive');
			// 전략이 설정되었는지 확인하는 방법이 필요함
			// 현재는 내부 상태이므로 간접적으로 확인
		});

		it('모든 재연결 전략이 유효한 설정을 가져야 함', () => {
			Object.values(reconnectionStrategies).forEach((strategy) => {
				expect(strategy.baseInterval).toBeGreaterThan(0);
				expect(strategy.maxInterval).toBeGreaterThan(strategy.baseInterval);
				expect(strategy.maxAttempts).toBeGreaterThan(0);
				expect(strategy.backoffMultiplier).toBeGreaterThan(1);
				expect(typeof strategy.jitterEnabled).toBe('boolean');
			});
		});

		it('각 전략의 특성이 올바르게 설정되어야 함', () => {
			const { aggressive, normal, conservative } = reconnectionStrategies;

			// 적극적 전략은 빠른 재연결
			expect(aggressive.baseInterval).toBeLessThan(normal.baseInterval);
			expect(aggressive.maxAttempts).toBeGreaterThan(normal.maxAttempts);

			// 보수적 전략은 느린 재연결
			expect(conservative.baseInterval).toBeGreaterThan(normal.baseInterval);
			expect(conservative.maxAttempts).toBeLessThan(normal.maxAttempts);
		});
	});

	describe('지수 백오프 알고리즘 검증', () => {
		it('시도 횟수에 따라 지연시간이 지수적으로 증가해야 함', () => {
			const strategy = reconnectionStrategies.normal;

			const delays = [];
			for (let i = 1; i <= 5; i++) {
				delays.push(calculateReconnectDelay(i, strategy));
			}

			// 각 지연시간이 이전보다 크거나 같아야 함 (지터 때문에 완전히 증가하지 않을 수 있음)
			for (let i = 1; i < delays.length; i++) {
				// 지터를 고려하여 대략적인 증가 패턴 확인
				const expectedMinDelay =
					strategy.baseInterval * Math.pow(strategy.backoffMultiplier, i - 1);
				expect(delays[i]).toBeGreaterThanOrEqual(expectedMinDelay * 0.8); // 20% 여유
			}
		});

		it('최대 지연시간 제한이 올바르게 적용되어야 함', () => {
			const strategy = reconnectionStrategies.normal;

			// 매우 큰 시도 횟수들로 테스트
			const largeAttempts = [10, 15, 20, 25, 30];

			largeAttempts.forEach((attempt) => {
				const delay = calculateReconnectDelay(attempt, strategy);
				expect(delay).toBeLessThanOrEqual(strategy.maxInterval + 1000); // 지터 고려
			});
		});

		it('백오프 배수가 올바르게 적용되어야 함', () => {
			const customStrategy = {
				...reconnectionStrategies.normal,
				backoffMultiplier: 3,
				jitterEnabled: false // 정확한 계산을 위해 지터 비활성화
			};

			const delay1 = calculateReconnectDelay(1, customStrategy);
			const delay2 = calculateReconnectDelay(2, customStrategy);
			const delay3 = calculateReconnectDelay(3, customStrategy);

			expect(delay1).toBe(customStrategy.baseInterval);
			expect(delay2).toBe(customStrategy.baseInterval * 3);
			expect(delay3).toBe(customStrategy.baseInterval * 9);
		});
	});

	describe('오류 복구 시나리오', () => {
		it('네트워크 오프라인 상태를 올바르게 감지해야 함', () => {
			// 네트워크 오프라인 상태 모킹
			Object.defineProperty(navigator, 'onLine', {
				writable: true,
				value: false
			});

			expect(navigator.onLine).toBe(false);
		});

		it('서버 오류 시 보수적 전략을 사용해야 함', () => {
			// 서버 오류 상황에서의 전략 테스트
			setReconnectionStrategy('conservative');

			const strategy = reconnectionStrategies.conservative;
			const delay = calculateReconnectDelay(1, strategy);

			expect(delay).toBeGreaterThanOrEqual(strategy.baseInterval);
		});

		it('연결 품질에 따른 적응형 전략이 올바르게 작동해야 함', () => {
			// 각 전략의 특성 확인
			const strategies = ['aggressive', 'normal', 'conservative'];

			strategies.forEach((strategyName) => {
				setReconnectionStrategy(strategyName);
				const strategy = reconnectionStrategies[strategyName];
				const delay = calculateReconnectDelay(1, strategy);

				expect(delay).toBeGreaterThanOrEqual(100); // 최소 지연시간
				expect(delay).toBeLessThanOrEqual(strategy.maxInterval + 1000); // 최대 지연시간
			});
		});
	});

	describe('재연결 로직 통합 테스트', () => {
		it('재연결 전략별 특성이 올바르게 반영되어야 함', () => {
			const testCases = [
				{ strategy: 'aggressive', expectedFastReconnect: true },
				{ strategy: 'normal', expectedFastReconnect: false },
				{ strategy: 'conservative', expectedFastReconnect: false }
			];

			testCases.forEach(({ strategy, expectedFastReconnect }) => {
				setReconnectionStrategy(strategy);
				const strategyConfig = reconnectionStrategies[strategy];
				const delay = calculateReconnectDelay(1, strategyConfig);

				if (expectedFastReconnect) {
					expect(delay).toBeLessThan(1500); // 빠른 재연결 (지터 고려)
				} else {
					expect(delay).toBeGreaterThanOrEqual(1000); // 일반적인 재연결
				}
			});
		});

		it('최대 재시도 횟수가 전략별로 올바르게 설정되어야 함', () => {
			const { aggressive, normal, conservative } = reconnectionStrategies;

			// 적극적 전략은 더 많은 시도
			expect(aggressive.maxAttempts).toBeGreaterThan(normal.maxAttempts);

			// 보수적 전략은 더 적은 시도
			expect(conservative.maxAttempts).toBeLessThan(normal.maxAttempts);
		});
	});
});
