/**
 * 알림 처리 서비스 테스트
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
	showBrowserNotification,
	playNotificationSound,
	vibrateDevice,
	requestNotificationPermission,
	getNotificationPermissionStatus,
	isVibrationSupported,
	testNotificationSystem,
	diagnoseNotificationSystem
} from '../notificationHandler';
import type { NotificationData } from '$lib/types/notificationTypes';

// Mock 전역 객체들
const mockNotification = vi.fn();
const mockAudio = vi.fn();
const mockNavigatorVibrate = vi.fn();

beforeEach(() => {
	vi.clearAllMocks();

	// Notification API 모킹
	Object.defineProperty(global, 'Notification', {
		value: mockNotification,
		writable: true
	});

	// Audio API 모킹
	Object.defineProperty(global, 'Audio', {
		value: mockAudio,
		writable: true
	});

	// Navigator vibrate 모킹
	Object.defineProperty(global.navigator, 'vibrate', {
		value: mockNavigatorVibrate,
		writable: true
	});
});

describe('알림 처리 서비스', () => {
	const testNotification: NotificationData = {
		id: 1,
		title: '테스트 알림',
		message: '테스트 메시지',
		type: 'test',
		priority: 'normal',
		created_at: new Date().toISOString()
	};

	describe('브라우저 알림', () => {
		it('브라우저 알림 권한 상태를 올바르게 확인해야 함', () => {
			// Notification이 지원되지 않는 경우
			Object.defineProperty(global, 'Notification', {
				value: undefined,
				writable: true
			});

			const status = getNotificationPermissionStatus();
			expect(status.supported).toBe(false);
			expect(status.message).toContain('지원하지 않습니다');
		});

		it('권한이 승인된 경우 브라우저 알림을 표시해야 함', async () => {
			// Notification 권한을 granted로 설정
			Object.defineProperty(global.Notification, 'permission', {
				value: 'granted',
				writable: true
			});

			mockNotification.mockImplementation((title, options) => ({
				title,
				...options,
				onclick: null,
				onclose: null,
				onerror: null,
				close: vi.fn()
			}));

			await showBrowserNotification(testNotification);

			expect(mockNotification).toHaveBeenCalledWith(
				testNotification.title,
				expect.objectContaining({
					body: testNotification.message,
					tag: testNotification.id.toString()
				})
			);
		});
	});

	describe('사운드 알림', () => {
		it('우선순위에 따라 올바른 사운드 파일을 재생해야 함', () => {
			const mockAudioInstance = {
				volume: 0,
				load: vi.fn(),
				play: vi.fn().mockResolvedValue(undefined),
				addEventListener: vi.fn()
			};

			mockAudio.mockReturnValue(mockAudioInstance);

			playNotificationSound('high');

			expect(mockAudio).toHaveBeenCalledWith('/sounds/notification-high.mp3');
			expect(mockAudioInstance.volume).toBe(0.7); // high 우선순위 볼륨
		});

		it('사용자 지정 사운드 파일을 사용해야 함 (낮음/보통 우선순위)', () => {
			const mockAudioInstance = {
				volume: 0,
				load: vi.fn(),
				play: vi.fn().mockResolvedValue(undefined),
				addEventListener: vi.fn()
			};

			mockAudio.mockReturnValue(mockAudioInstance);

			const customSound = '/custom/sound.mp3';
			playNotificationSound('normal', customSound);

			expect(mockAudio).toHaveBeenCalledWith(customSound);
		});
	});

	describe('진동 알림', () => {
		it('진동 지원 여부를 올바르게 확인해야 함', () => {
			// 진동이 지원되는 경우
			expect(isVibrationSupported()).toBe(true);

			// 진동이 지원되지 않는 경우
			Object.defineProperty(global.navigator, 'vibrate', {
				value: undefined,
				writable: true
			});

			expect(isVibrationSupported()).toBe(false);
		});

		it('우선순위에 따라 올바른 진동 패턴을 실행해야 함', () => {
			mockNavigatorVibrate.mockReturnValue(true);

			vibrateDevice('urgent');

			expect(mockNavigatorVibrate).toHaveBeenCalledWith([500, 200, 500, 200, 500, 200, 500]);
		});
	});

	describe('시스템 진단', () => {
		it('알림 시스템 상태를 올바르게 진단해야 함', async () => {
			// 모든 기능이 지원되는 환경 설정
			Object.defineProperty(global.Notification, 'permission', {
				value: 'granted',
				writable: true
			});

			const diagnosis = await diagnoseNotificationSystem();

			expect(diagnosis).toHaveProperty('browser');
			expect(diagnosis).toHaveProperty('sound');
			expect(diagnosis).toHaveProperty('vibration');
			expect(diagnosis).toHaveProperty('settings');
			expect(diagnosis).toHaveProperty('recommendations');

			expect(diagnosis.browser.supported).toBe(true);
			expect(diagnosis.sound.supported).toBe(true);
			expect(diagnosis.vibration.supported).toBe(true);
		});
	});

	describe('테스트 함수', () => {
		it('알림 시스템 테스트를 실행해야 함', async () => {
			// 필요한 모킹 설정
			Object.defineProperty(global.Notification, 'permission', {
				value: 'granted',
				writable: true
			});

			mockNotification.mockImplementation(() => ({
				onclick: null,
				onclose: null,
				onerror: null,
				close: vi.fn()
			}));

			const mockAudioInstance = {
				volume: 0,
				load: vi.fn(),
				play: vi.fn().mockResolvedValue(undefined),
				addEventListener: vi.fn()
			};

			mockAudio.mockReturnValue(mockAudioInstance);
			mockNavigatorVibrate.mockReturnValue(true);

			// 테스트 실행 (오류 없이 완료되어야 함)
			await expect(testNotificationSystem('normal', 'all')).resolves.toBeUndefined();
		});
	});
});
