/**
 * 알림 상호작용 기능 테스트
 * 작업 5.6: 알림 상호작용 기능 구현 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	markNotificationAsRead,
	markMultipleNotificationsAsRead,
	markAllNotificationsAsRead,
	toggleNotificationReadStatus,
	handleNotificationClick,
	handleNotificationActionUrl,
	getNotificationIcon,
	preloadNotificationIcons,
	checkIconExists,
	getFallbackNotificationIcon
} from '../notificationHandler';
import type { NotificationData } from '$lib/types/notificationTypes';

// Mock dependencies
vi.mock('$lib/services/indexedDBManager');
vi.mock('$lib/services/storeUpdaters');

// Mock 데이터
const mockNotification: NotificationData = {
	id: 1,
	title: '테스트 알림',
	message: '테스트 메시지',
	priority: 'normal',
	type: 'system',
	action_url: 'https://example.com/action',
	read: false,
	received_at: '2024-01-01T00:00:00Z'
};

const mockUnreadNotification: NotificationData = {
	...mockNotification,
	id: 2,
	read: false
};

const mockReadNotification: NotificationData = {
	...mockNotification,
	id: 3,
	read: true,
	read_at: '2024-01-01T01:00:00Z'
};

describe('알림 읽음 처리 기능', () => {
	beforeEach(() => {
		vi.clearAllMocks();

		// IndexedDB mock 설정
		const mockDb = {
			transaction: vi.fn(),
			objectStore: vi.fn()
		};

		vi.mocked(require('$lib/services/indexedDBManager').initializeIndexedDB).mockResolvedValue(
			mockDb
		);
		vi.mocked(require('$lib/services/indexedDBManager').getData).mockResolvedValue(
			mockUnreadNotification
		);
		vi.mocked(require('$lib/services/indexedDBManager').putData).mockResolvedValue(undefined);
		vi.mocked(require('$lib/services/storeUpdaters').updateNotificationStore).mockResolvedValue(
			undefined
		);
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('markNotificationAsRead', () => {
		it('읽지 않은 알림을 읽음 상태로 변경해야 함', async () => {
			// Given
			const notificationId = 1;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);
			const mockPutData = vi.mocked(require('$lib/services/indexedDBManager').putData);

			mockGetData.mockResolvedValue({ ...mockUnreadNotification, id: notificationId });

			// When
			const result = await markNotificationAsRead(notificationId);

			// Then
			expect(result).toBe(true);
			expect(mockPutData).toHaveBeenCalledWith(
				expect.any(Object),
				'notifications',
				expect.objectContaining({
					id: notificationId,
					read: true,
					read_at: expect.any(String)
				})
			);
		});

		it('이미 읽은 알림의 경우 true를 반환해야 함', async () => {
			// Given
			const notificationId = 3;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);

			mockGetData.mockResolvedValue(mockReadNotification);

			// When
			const result = await markNotificationAsRead(notificationId);

			// Then
			expect(result).toBe(true);
		});

		it('존재하지 않는 알림의 경우 false를 반환해야 함', async () => {
			// Given
			const notificationId = 999;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);

			mockGetData.mockResolvedValue(null);

			// When
			const result = await markNotificationAsRead(notificationId);

			// Then
			expect(result).toBe(false);
		});

		it('데이터베이스 오류 시 false를 반환해야 함', async () => {
			// Given
			const notificationId = 1;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);

			mockGetData.mockRejectedValue(new Error('DB 오류'));

			// When
			const result = await markNotificationAsRead(notificationId);

			// Then
			expect(result).toBe(false);
		});
	});

	describe('markMultipleNotificationsAsRead', () => {
		it('여러 알림을 한 번에 읽음 처리해야 함', async () => {
			// Given
			const notificationIds = [1, 2, 3];
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);

			mockGetData
				.mockResolvedValueOnce({ ...mockUnreadNotification, id: 1 })
				.mockResolvedValueOnce({ ...mockUnreadNotification, id: 2 })
				.mockResolvedValueOnce({ ...mockReadNotification, id: 3 });

			// When
			const result = await markMultipleNotificationsAsRead(notificationIds);

			// Then
			expect(result.success).toEqual([1, 2, 3]);
			expect(result.failed).toEqual([]);
		});

		it('일부 실패한 경우 성공과 실패를 구분해야 함', async () => {
			// Given
			const notificationIds = [1, 2, 999];
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);

			mockGetData
				.mockResolvedValueOnce({ ...mockUnreadNotification, id: 1 })
				.mockResolvedValueOnce({ ...mockUnreadNotification, id: 2 })
				.mockResolvedValueOnce(null); // 존재하지 않는 알림

			// When
			const result = await markMultipleNotificationsAsRead(notificationIds);

			// Then
			expect(result.success).toEqual([1, 2]);
			expect(result.failed).toEqual([999]);
		});
	});

	describe('markAllNotificationsAsRead', () => {
		it('모든 읽지 않은 알림을 읽음 처리해야 함', async () => {
			// Given
			const mockGetAllData = vi.mocked(require('$lib/services/indexedDBManager').getAllData);
			const mockPutManyData = vi.mocked(require('$lib/services/indexedDBManager').putManyData);

			const allNotifications = [
				{ ...mockUnreadNotification, id: 1 },
				{ ...mockReadNotification, id: 2 },
				{ ...mockUnreadNotification, id: 3 }
			];

			mockGetAllData.mockResolvedValue(allNotifications);

			// When
			const result = await markAllNotificationsAsRead();

			// Then
			expect(result).toBe(2); // 2개의 읽지 않은 알림
			expect(mockPutManyData).toHaveBeenCalledWith(
				expect.any(Object),
				'notifications',
				expect.arrayContaining([
					expect.objectContaining({ id: 1, read: true }),
					expect.objectContaining({ id: 3, read: true })
				])
			);
		});

		it('읽지 않은 알림이 없는 경우 0을 반환해야 함', async () => {
			// Given
			const mockGetAllData = vi.mocked(require('$lib/services/indexedDBManager').getAllData);

			mockGetAllData.mockResolvedValue([mockReadNotification]);

			// When
			const result = await markAllNotificationsAsRead();

			// Then
			expect(result).toBe(0);
		});
	});

	describe('toggleNotificationReadStatus', () => {
		it('읽지 않은 알림을 읽음 상태로 토글해야 함', async () => {
			// Given
			const notificationId = 1;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);
			const mockPutData = vi.mocked(require('$lib/services/indexedDBManager').putData);

			mockGetData.mockResolvedValue({ ...mockUnreadNotification, id: notificationId });

			// When
			const result = await toggleNotificationReadStatus(notificationId);

			// Then
			expect(result).toBe(true);
			expect(mockPutData).toHaveBeenCalledWith(
				expect.any(Object),
				'notifications',
				expect.objectContaining({
					id: notificationId,
					read: true,
					read_at: expect.any(String)
				})
			);
		});

		it('읽은 알림을 읽지 않음 상태로 토글해야 함', async () => {
			// Given
			const notificationId = 3;
			const mockGetData = vi.mocked(require('$lib/services/indexedDBManager').getData);
			const mockPutData = vi.mocked(require('$lib/services/indexedDBManager').putData);

			mockGetData.mockResolvedValue({ ...mockReadNotification, id: notificationId });

			// When
			const result = await toggleNotificationReadStatus(notificationId);

			// Then
			expect(result).toBe(false);
			expect(mockPutData).toHaveBeenCalledWith(
				expect.any(Object),
				'notifications',
				expect.objectContaining({
					id: notificationId,
					read: false
				})
			);
		});
	});
});

describe('알림 클릭 처리 기능', () => {
	beforeEach(() => {
		// DOM 이벤트 mock
		global.window = {
			...global.window,
			dispatchEvent: vi.fn(),
			location: {
				origin: 'https://example.com',
				href: 'https://example.com/current'
			},
			history: {
				pushState: vi.fn()
			},
			open: vi.fn()
		} as any;

		// PopStateEvent mock
		global.PopStateEvent = vi.fn() as any;
	});

	describe('handleNotificationClick', () => {
		it('알림 클릭 시 읽음 처리와 이벤트 발생해야 함', async () => {
			// Given
			const mockMarkAsRead = vi.fn().mockResolvedValue(true);
			vi.mocked(markNotificationAsRead).mockImplementation(mockMarkAsRead);

			// When
			handleNotificationClick(mockNotification);

			// Then
			expect(mockMarkAsRead).toHaveBeenCalledWith(mockNotification.id);
			expect(window.dispatchEvent).toHaveBeenCalledWith(
				expect.objectContaining({
					type: 'notification-clicked',
					detail: mockNotification
				})
			);
		});

		it('액션 URL이 있는 경우 URL 처리해야 함', () => {
			// Given
			const notificationWithAction = {
				...mockNotification,
				action_url: 'https://example.com/action'
			};

			// When
			handleNotificationClick(notificationWithAction);

			// Then
			// handleNotificationActionUrl이 호출되었는지 확인하기 위해
			// 실제로는 window.open이나 location 변경이 일어나야 함
			expect(window.dispatchEvent).toHaveBeenCalled();
		});
	});

	describe('handleNotificationActionUrl', () => {
		it('내부 링크의 경우 현재 창에서 이동해야 함', () => {
			// Given
			const internalUrl = '/internal/page';

			// When
			handleNotificationActionUrl(internalUrl, 1);

			// Then
			expect(window.history.pushState).toHaveBeenCalled();
			expect(window.dispatchEvent).toHaveBeenCalledWith(
				expect.objectContaining({
					type: 'notification-action-executed'
				})
			);
		});

		it('외부 링크의 경우 새 창에서 열어야 함', () => {
			// Given
			const externalUrl = 'https://external.com/page';

			// When
			handleNotificationActionUrl(externalUrl, 1);

			// Then
			expect(window.open).toHaveBeenCalledWith(externalUrl, '_blank', 'noopener,noreferrer');
		});

		it('javascript: 프로토콜은 차단해야 함', () => {
			// Given
			const maliciousUrl = 'javascript:alert("xss")';

			// When
			handleNotificationActionUrl(maliciousUrl, 1);

			// Then
			expect(window.open).not.toHaveBeenCalled();
			expect(window.dispatchEvent).toHaveBeenCalledWith(
				expect.objectContaining({
					type: 'show-notification-toast'
				})
			);
		});

		it('유효하지 않은 URL의 경우 에러 처리해야 함', () => {
			// Given
			const invalidUrl = '';

			// When
			handleNotificationActionUrl(invalidUrl, 1);

			// Then
			expect(window.open).not.toHaveBeenCalled();
		});
	});
});

describe('알림 아이콘 기능', () => {
	describe('getNotificationIcon', () => {
		it('우선순위별 아이콘을 반환해야 함', () => {
			// Test cases
			const testCases = [
				{ priority: 'low', expected: '/icons/notification-low.png' },
				{ priority: 'normal', expected: '/icons/notification.png' },
				{ priority: 'high', expected: '/icons/notification-high.png' },
				{ priority: 'urgent', expected: '/icons/notification-urgent.png' }
			];

			testCases.forEach(({ priority, expected }) => {
				const result = getNotificationIcon(priority);
				expect(result).toBe(expected);
			});
		});

		it('타입별 아이콘이 우선순위보다 우선해야 함', () => {
			// Given
			const priority = 'normal';
			const type = 'system';

			// When
			const result = getNotificationIcon(priority, type);

			// Then
			expect(result).toBe('/icons/notification-system.png');
		});

		it('알 수 없는 우선순위의 경우 기본 아이콘을 반환해야 함', () => {
			// Given
			const unknownPriority = 'unknown';

			// When
			const result = getNotificationIcon(unknownPriority);

			// Then
			expect(result).toBe('/icons/notification.png');
		});

		it('알 수 없는 타입의 경우 우선순위별 아이콘을 반환해야 함', () => {
			// Given
			const priority = 'high';
			const unknownType = 'unknown';

			// When
			const result = getNotificationIcon(priority, unknownType);

			// Then
			expect(result).toBe('/icons/notification-high.png');
		});
	});

	describe('checkIconExists', () => {
		it('존재하는 아이콘의 경우 true를 반환해야 함', async () => {
			// Given
			const iconPath = '/icons/notification.png';

			// Image 객체 mock
			const mockImage = {
				onload: null,
				onerror: null,
				src: ''
			};

			global.Image = vi.fn(() => mockImage) as any;

			// When
			const promise = checkIconExists(iconPath);

			// 이미지 로드 성공 시뮬레이션
			setTimeout(() => {
				if (mockImage.onload) mockImage.onload();
			}, 0);

			const result = await promise;

			// Then
			expect(result).toBe(true);
		});

		it('존재하지 않는 아이콘의 경우 false를 반환해야 함', async () => {
			// Given
			const iconPath = '/icons/nonexistent.png';

			// Image 객체 mock
			const mockImage = {
				onload: null,
				onerror: null,
				src: ''
			};

			global.Image = vi.fn(() => mockImage) as any;

			// When
			const promise = checkIconExists(iconPath);

			// 이미지 로드 실패 시뮬레이션
			setTimeout(() => {
				if (mockImage.onerror) mockImage.onerror();
			}, 0);

			const result = await promise;

			// Then
			expect(result).toBe(false);
		});
	});

	describe('getFallbackNotificationIcon', () => {
		it('타입별 아이콘이 존재하는 경우 반환해야 함', async () => {
			// Given
			const priority = 'normal';
			const type = 'system';

			// checkIconExists mock - 타입별 아이콘 존재
			vi.mocked(checkIconExists).mockResolvedValue(true);

			// When
			const result = await getFallbackNotificationIcon(priority, type);

			// Then
			expect(result).toBe('/icons/notification-system.png');
		});

		it('타입별 아이콘이 없고 우선순위별 아이콘이 존재하는 경우 반환해야 함', async () => {
			// Given
			const priority = 'high';
			const type = 'nonexistent';

			// checkIconExists mock - 타입별 아이콘 없음, 우선순위별 아이콘 존재
			vi.mocked(checkIconExists)
				.mockResolvedValueOnce(false) // 타입별 아이콘 없음
				.mockResolvedValueOnce(true); // 우선순위별 아이콘 존재

			// When
			const result = await getFallbackNotificationIcon(priority, type);

			// Then
			expect(result).toBe('/icons/notification-high.png');
		});

		it('모든 아이콘이 없는 경우 기본 SVG 아이콘을 반환해야 함', async () => {
			// Given
			const priority = 'unknown';
			const type = 'unknown';

			// checkIconExists mock - 모든 아이콘 없음
			vi.mocked(checkIconExists).mockResolvedValue(false);

			// When
			const result = await getFallbackNotificationIcon(priority, type);

			// Then
			expect(result).toContain('data:image/svg+xml;base64');
		});
	});
});
