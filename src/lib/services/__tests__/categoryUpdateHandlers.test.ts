/**
 * 카테고리 데이터 업데이트 처리 함수 테스트
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock 설정
const mockGetData = vi.fn();
const mockPutData = vi.fn();
const mockUpdateCategoryStore = vi.fn();
const mockUpdateMessageStore = { update: vi.fn() };
const mockHighlightedItemsStore = { update: vi.fn() };
const mockUpdatedItemsStore = { update: vi.fn() };
const mockCategoryStore = { set: vi.fn() };

vi.mock('../indexedDBManager', () => ({
	getData: mockGetData,
	putData: mockPutData,
	getAllData: vi.fn(),
	deleteData: vi.fn(),
	clearStore: vi.fn(),
	putManyData: vi.fn()
}));

vi.mock('../storeUpdaters', () => ({
	updateCategoryStore: mockUpdateCategoryStore,
	updateMessageStore: mockUpdateMessageStore,
	highlightedItemsStore: mockHighlightedItemsStore,
	updatedItemsStore: mockUpdatedItemsStore,
	categoryStore: mockCategoryStore
}));

// Mock IndexedDB
const mockDB = {} as IDBDatabase;

describe('카테고리 업데이트 핸들러 테스트', () => {
	beforeEach(() => {
		vi.clearAllMocks();

		// Notification API 모킹
		global.Notification = {
			permission: 'granted',
			requestPermission: vi.fn().mockResolvedValue('granted')
		} as any;

		// crypto.randomUUID 모킹
		Object.defineProperty(global, 'crypto', {
			value: {
				randomUUID: vi.fn().mockReturnValue('test-uuid-123')
			},
			writable: true
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('카테고리 생성 처리', () => {
		it('새로운 카테고리가 정상적으로 생성되어야 함', async () => {
			// Given
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [{ id: 1, name: '기존 카테고리' }],
				cate5: []
			});

			const payload = {
				cate4: [{ id: 2, name: '새 카테고리' }],
				cate5: []
			};
			const affectedIds = [2];

			// 실제 함수를 동적으로 import하여 테스트
			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');

			// DB 초기화
			initializeDataHandlers(mockDB);

			// When
			await forceCategoryUpdate('created', payload, affectedIds);

			// Then
			expect(mockPutData).toHaveBeenCalledWith(
				mockDB,
				'categories',
				expect.objectContaining({
					key: 'main',
					cate4: expect.arrayContaining([
						{ id: 1, name: '기존 카테고리' },
						{ id: 2, name: '새 카테고리' }
					]),
					cate5: []
				})
			);

			expect(mockUpdateCategoryStore).toHaveBeenCalledWith(mockDB);
		});

		it('중복된 ID의 카테고리는 제거되어야 함', async () => {
			// Given
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [{ id: 1, name: '기존 카테고리' }],
				cate5: []
			});

			const payload = {
				cate4: [
					{ id: 1, name: '중복 카테고리' }, // 중복 ID
					{ id: 2, name: '새 카테고리' }
				],
				cate5: []
			};
			const affectedIds = [1, 2];

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			await forceCategoryUpdate('created', payload, affectedIds);

			// Then
			const putDataCall = mockPutData.mock.calls[0];
			const savedData = putDataCall[2];

			// ID 1은 하나만 있어야 함
			const id1Items = savedData.cate4.filter((item: any) => item.id === 1);
			expect(id1Items).toHaveLength(1);
		});
	});

	describe('카테고리 업데이트 처리', () => {
		it('기존 카테고리가 정상적으로 업데이트되어야 함', async () => {
			// Given
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [{ id: 1, name: '기존 카테고리' }],
				cate5: []
			});

			const payload = {
				cate4: [{ id: 1, name: '업데이트된 카테고리' }],
				cate5: []
			};
			const affectedIds = [1];

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			await forceCategoryUpdate('updated', payload, affectedIds);

			// Then
			expect(mockPutData).toHaveBeenCalledWith(
				mockDB,
				'categories',
				expect.objectContaining({
					key: 'main',
					cate4: [{ id: 1, name: '업데이트된 카테고리' }],
					cate5: []
				})
			);
		});
	});

	describe('카테고리 삭제 처리', () => {
		it('지정된 카테고리가 정상적으로 삭제되어야 함', async () => {
			// Given
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [
					{ id: 1, name: '유지될 카테고리' },
					{ id: 2, name: '삭제될 카테고리' }
				],
				cate5: []
			});

			const affectedIds = [2];

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			await forceCategoryUpdate('deleted', null, affectedIds);

			// Then
			expect(mockPutData).toHaveBeenCalledWith(
				mockDB,
				'categories',
				expect.objectContaining({
					key: 'main',
					cate4: [{ id: 1, name: '유지될 카테고리' }],
					cate5: []
				})
			);
		});

		it('존재하지 않는 데이터 삭제 시 오류가 발생하지 않아야 함', async () => {
			// Given
			mockGetData.mockResolvedValue(null);

			const affectedIds = [999];

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When & Then
			await expect(forceCategoryUpdate('deleted', null, affectedIds)).resolves.not.toThrow();
		});
	});

	describe('배치 업데이트 처리', () => {
		it('대량의 카테고리가 정상적으로 배치 업데이트되어야 함', async () => {
			// Given
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [],
				cate5: []
			});

			const payload = {
				cate4: Array.from({ length: 50 }, (_, i) => ({
					id: i + 1,
					name: `배치 카테고리 ${i + 1}`
				})),
				cate5: []
			};
			const affectedIds = Array.from({ length: 50 }, (_, i) => i + 1);

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			await forceCategoryUpdate('batch_updated', payload, affectedIds);

			// Then
			expect(mockPutData).toHaveBeenCalledWith(
				mockDB,
				'categories',
				expect.objectContaining({
					key: 'main',
					cate4: expect.arrayContaining(payload.cate4),
					cate5: []
				})
			);
		});
	});

	describe('동기화 상태 확인', () => {
		it('카테고리 동기화 상태를 정확히 반환해야 함', async () => {
			// Given
			const testDate = new Date('2024-01-01T10:00:00Z').toISOString();
			mockGetData.mockResolvedValue({
				key: 'main',
				cate4: [{ id: 1 }, { id: 2 }],
				cate5: [{ id: 3 }],
				updated_at: testDate
			});

			const { getCategorySyncStatus, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			const status = await getCategorySyncStatus();

			// Then
			expect(status).toEqual({
				lastUpdate: testDate,
				itemCount: {
					cate4: 2,
					cate5: 1
				},
				isStale: true // 2024년 데이터이므로 stale
			});
		});

		it('데이터가 없는 경우 적절한 기본값을 반환해야 함', async () => {
			// Given
			mockGetData.mockResolvedValue(null);

			const { getCategorySyncStatus, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When
			const status = await getCategorySyncStatus();

			// Then
			expect(status).toEqual({
				lastUpdate: null,
				itemCount: {
					cate4: 0,
					cate5: 0
				},
				isStale: true
			});
		});
	});

	describe('오류 처리', () => {
		it('IndexedDB 오류 시 적절한 에러 메시지를 표시해야 함', async () => {
			// Given
			mockPutData.mockRejectedValue(new Error('IndexedDB 오류'));

			const payload = { cate4: [], cate5: [] };
			const affectedIds = [1];

			const { forceCategoryUpdate, initializeDataHandlers } = await import('../sseDataHandlers');
			initializeDataHandlers(mockDB);

			// When & Then
			await expect(forceCategoryUpdate('created', payload, affectedIds)).rejects.toThrow(
				'IndexedDB 오류'
			);
		});

		it('DB가 초기화되지 않은 상태에서 오류를 발생시켜야 함', async () => {
			// Given - DB 초기화하지 않음
			// 새로운 모듈 인스턴스를 가져와서 dbInstance가 null인 상태를 보장
			vi.resetModules();
			const { forceCategoryUpdate } = await import('../sseDataHandlers');

			// When & Then
			await expect(forceCategoryUpdate('created', {}, [1])).rejects.toThrow(
				'IndexedDB가 초기화되지 않았습니다.'
			);
		});
	});
});

describe('카테고리 스토어 업데이트 테스트', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('카테고리 데이터가 스토어에 정확히 반영되어야 함', async () => {
		// Given
		const testData = {
			key: 'main',
			cate4: [{ id: 1, name: '카테고리1' }],
			cate5: [{ id: 2, name: '카테고리2' }],
			updated_at: '2024-01-01T10:00:00Z'
		};

		mockGetData.mockResolvedValue(testData);

		// When
		await mockUpdateCategoryStore(mockDB);

		// Then
		expect(mockUpdateCategoryStore).toHaveBeenCalledWith(mockDB);
	});

	it('데이터가 없는 경우 빈 상태로 초기화되어야 함', async () => {
		// Given
		mockGetData.mockResolvedValue(null);

		// When
		await mockUpdateCategoryStore(mockDB);

		// Then
		expect(mockUpdateCategoryStore).toHaveBeenCalledWith(mockDB);
	});

	it('오류 발생 시 빈 상태로 설정되어야 함', async () => {
		// Given
		mockGetData.mockRejectedValue(new Error('데이터베이스 오류'));

		// When
		await mockUpdateCategoryStore(mockDB);

		// Then
		expect(mockUpdateCategoryStore).toHaveBeenCalledWith(mockDB);
	});
});
