/**
 * SSE 연결 상태 관리 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	sseConnectionState,
	updateConnectionStatus,
	incrementReconnectAttempts,
	updateNetworkStatus,
	resetConnectionState,
	isConnected,
	isConnecting,
	hasError,
	canReconnect,
	updateLatency,
	updateHeartbeat,
	checkConnectionHealth,
	performAdvancedHealthCheck,
	getCurrentConnectionState,
	getConnectionStatus,
	isConnectedState,
	getDebugInfo,
} from '../sseConnectionState';

// DOM 모킹
Object.defineProperty(document, 'visibilityState', {
	value: 'visible',
	writable: true
});

Object.defineProperty(navigator, 'onLine', {
	value: true,
	writable: true
});

// 이벤트 리스너 모킹
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();
Object.defineProperty(window, 'addEventListener', { value: mockAddEventListener });
Object.defineProperty(window, 'removeEventListener', { value: mockRemoveEventListener });
Object.defineProperty(document, 'addEventListener', { value: mockAddEventListener });
Object.defineProperty(document, 'removeEventListener', { value: mockRemoveEventListener });

describe('SSE 연결 상태 관리', () => {
	beforeEach(() => {
		vi.clearAllMocks();
		resetConnectionState();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('기본 상태 관리', () => {
		it('초기 상태가 올바르게 설정되어야 함', () => {
			const state = getCurrentConnectionState();

			expect(state.status).toBe('disconnected');
			expect(state.lastConnected).toBeNull();
			expect(state.lastError).toBeNull();
			expect(state.reconnectAttempts).toBe(0);
			expect(state.messageCount).toBe(0);
			expect(state.latency).toBeNull();
		});

		it('연결 상태를 업데이트해야 함', () => {
			updateConnectionStatus('connecting');
			expect(getConnectionStatus()).toBe('connecting');

			updateConnectionStatus('connected');
			expect(getConnectionStatus()).toBe('connected');
			expect(isConnectedState()).toBe(true);
		});

		it('오류 메시지와 함께 상태를 업데이트해야 함', () => {
			const errorMessage = '연결 실패';
			updateConnectionStatus('error', errorMessage);

			const state = getCurrentConnectionState();
			expect(state.status).toBe('error');
			expect(state.lastError).toBe(errorMessage);
		});

		it('재연결 시도 횟수를 증가시켜야 함', () => {
			incrementReconnectAttempts();
			incrementReconnectAttempts();

			const state = getCurrentConnectionState();
			expect(state.reconnectAttempts).toBe(2);
		});

		it('네트워크 상태를 업데이트해야 함', () => {
			updateNetworkStatus(false);

			const state = getCurrentConnectionState();
			expect(state.isOnline).toBe(false);
		});
	});

	describe('파생 스토어', () => {
		it('연결 상태 파생 스토어가 올바르게 작동해야 함', () => {
			let connectedValue = false;
			let connectingValue = false;
			let errorValue = false;
			let canReconnectValue = false;

			// 구독 설정
			const unsubscribeConnected = isConnected.subscribe((value) => (connectedValue = value));
			const unsubscribeConnecting = isConnecting.subscribe((value) => (connectingValue = value));
			const unsubscribeError = hasError.subscribe((value) => (errorValue = value));
			const unsubscribeCanReconnect = canReconnect.subscribe(
				(value) => (canReconnectValue = value)
			);

			// 연결 중 상태
			updateConnectionStatus('connecting');
			expect(connectingValue).toBe(true);
			expect(connectedValue).toBe(false);

			// 연결됨 상태
			updateConnectionStatus('connected');
			expect(connectedValue).toBe(true);
			expect(connectingValue).toBe(false);
			expect(canReconnectValue).toBe(true);

			// 오류 상태
			updateConnectionStatus('error');
			expect(errorValue).toBe(true);
			expect(connectedValue).toBe(false);

			// 정리
			unsubscribeConnected();
			unsubscribeConnecting();
			unsubscribeError();
			unsubscribeCanReconnect();
		});
	});

	describe('고급 기능', () => {
		it('메시지 수를 증가시켜야 함', () => {
			const state = getCurrentConnectionState();
			expect(state.messageCount).toBe(2);
		});

		it('지연시간을 업데이트해야 함', () => {
			updateLatency(150);

			const state = getCurrentConnectionState();
			expect(state.latency).toBe(150);
			expect(state.connectionQuality).toBe('good');
		});

		it('하트비트를 업데이트해야 함', () => {
			const beforeTime = Date.now();
			updateHeartbeat();
			const afterTime = Date.now();

			const state = getCurrentConnectionState();
			expect(state.lastHeartbeat).toBeDefined();
			expect(state.lastHeartbeat!.getTime()).toBeGreaterThanOrEqual(beforeTime);
			expect(state.lastHeartbeat!.getTime()).toBeLessThanOrEqual(afterTime);
		});

		it('연결 품질을 올바르게 계산해야 함', () => {
			updateLatency(50);
			expect(getCurrentConnectionState().connectionQuality).toBe('excellent');

			updateLatency(200);
			expect(getCurrentConnectionState().connectionQuality).toBe('good');

			updateLatency(800);
			expect(getCurrentConnectionState().connectionQuality).toBe('poor');

			updateLatency(2000);
			expect(getCurrentConnectionState().connectionQuality).toBe('poor');
		});
	});

	describe('연결 건강성 검사', () => {
		it('연결되지 않은 상태에서는 건강하지 않다고 판단해야 함', () => {
			updateConnectionStatus('disconnected');
			expect(checkConnectionHealth()).toBe(false);
		});

		it('연결된 상태에서는 건강하다고 판단해야 함', () => {
			updateConnectionStatus('connected');
			updateHeartbeat();
			expect(checkConnectionHealth()).toBe(true);
		});

		it('오래된 하트비트는 건강하지 않다고 판단해야 함', () => {
			updateConnectionStatus('connected');

			// 3분 전 하트비트 설정
			const oldHeartbeat = new Date(Date.now() - 180000);
			sseConnectionState.update((state) => ({
				...state,
				lastHeartbeat: oldHeartbeat
			}));

			expect(checkConnectionHealth()).toBe(false);
		});

		it('높은 지연시간은 건강하지 않다고 판단해야 함', () => {
			updateConnectionStatus('connected');
			updateHeartbeat();
			updateLatency(6000); // 6초

			expect(checkConnectionHealth()).toBe(false);
		});
	});

	describe('고급 건강성 검사', () => {
		it('건강한 연결 상태를 올바르게 판단해야 함', async () => {
			updateConnectionStatus('connected');
			updateHeartbeat();
			updateLatency(100);

			const result = await performAdvancedHealthCheck();

			expect(result.isHealthy).toBe(true);
			expect(result.issues).toHaveLength(0);
			expect(result.recommendations).toHaveLength(0);
		});

		it('문제가 있는 연결 상태를 올바르게 감지해야 함', async () => {
			updateConnectionStatus('error', '연결 오류');
			updateNetworkStatus(false);

			const result = await performAdvancedHealthCheck();

			expect(result.isHealthy).toBe(false);
			expect(result.issues.length).toBeGreaterThan(0);
			expect(result.recommendations.length).toBeGreaterThan(0);
			expect(result.issues).toContain('연결 상태: error');
			expect(result.issues).toContain('네트워크 연결 없음');
		});

		it('과도한 재연결 시도를 감지해야 함', async () => {
			updateConnectionStatus('connected');

			// 재연결 시도 횟수를 6회로 설정
			for (let i = 0; i < 6; i++) {
				incrementReconnectAttempts();
			}

			const result = await performAdvancedHealthCheck();

			expect(result.isHealthy).toBe(false);
			expect(result.issues.some((issue) => issue.includes('과도한 재연결 시도'))).toBe(true);
		});
	});

	describe('디버그 정보', () => {
		it('디버그 정보를 올바르게 제공해야 함', () => {
			updateConnectionStatus('connected');
			updateLatency(150);

			const debugInfo = getDebugInfo();

			expect(debugInfo.status).toBe('connected');
			expect(debugInfo.latency).toBe(150);
			expect(debugInfo.messageCount).toBe(1);
			expect(debugInfo.timestamp).toBeDefined();
			expect(debugInfo.userAgent).toBeDefined();
		});
	});
});
