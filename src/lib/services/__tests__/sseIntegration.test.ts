/**
 * SSE 통합 테스트
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { createDefaultSseConnection, manualReconnect, closeSseConnection } from '../sseConnection';
import { getCurrentConnectionState, resetConnectionState } from '../sseConnectionState';
import { resetStats } from '../sseStats';

/**
 * 모킹된 SSE 서버
 */
class MockSseServer {
	private clients: Set<MockEventSource> = new Set();
	private isRunning: boolean = false;
	private messageQueue: any[] = [];

	start(): void {
		this.isRunning = true;
		console.log('Mock SSE 서버 시작');
	}

	stop(): void {
		this.isRunning = false;
		this.clients.clear();
		console.log('Mock SSE 서버 중지');
	}

	addClient(client: MockEventSource): void {
		if (this.isRunning) {
			this.clients.add(client);
			// 연결 성공 시뮬레이션
			setTimeout(() => client.simulateOpen(), 100);
		}
	}

	removeClient(client: MockEventSource): void {
		this.clients.delete(client);
	}

	sendMessage(message: any): void {
		if (this.isRunning) {
			this.clients.forEach((client) => {
				client.simulateMessage(message);
			});
		} else {
			this.messageQueue.push(message);
		}
	}

	disconnect(): void {
		this.clients.forEach((client) => {
			client.simulateError();
		});
	}

	reconnect(): void {
		// 재연결 시뮬레이션
		setTimeout(() => {
			this.clients.forEach((client) => {
				client.simulateOpen();
			});
		}, 500);
	}

	getClientCount(): number {
		return this.clients.size;
	}
}

/**
 * 통합 테스트용 MockEventSource
 */
class MockEventSource {
	url: string;
	readyState: number = 0;
	onopen: ((event: Event) => void) | null = null;
	onmessage: ((event: MessageEvent) => void) | null = null;
	onerror: ((event: Event) => void) | null = null;

	private listeners: Map<string, ((event: Event) => void)[]> = new Map();
	private server: MockSseServer;

	constructor(url: string, options?: EventSourceInit) {
		this.url = url;
		this.server = mockServer;
		this.server.addClient(this);
	}

	simulateOpen(): void {
		this.readyState = 1;
		const event = new Event('open');
		this.onopen?.(event);
		this.dispatchEvent('open', event);
	}

	simulateMessage(data: any): void {
		if (this.readyState === 1) {
			const event = new MessageEvent('message', {
				data: typeof data === 'string' ? data : JSON.stringify(data)
			});
			this.onmessage?.(event);
			this.dispatchEvent('message', event);
		}
	}

	simulateError(): void {
		this.readyState = 2;
		const event = new Event('error');
		this.onerror?.(event);
		this.dispatchEvent('error', event);
	}

	close(): void {
		this.readyState = 2;
		this.server.removeClient(this);
	}

	addEventListener(type: string, listener: (event: Event) => void): void {
		if (!this.listeners.has(type)) {
			this.listeners.set(type, []);
		}
		this.listeners.get(type)!.push(listener);
	}

	removeEventListener(type: string, listener: (event: Event) => void): void {
		const listeners = this.listeners.get(type);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	private dispatchEvent(type: string, event: Event): void {
		const listeners = this.listeners.get(type);
		if (listeners) {
			listeners.forEach((listener) => listener(event));
		}
	}
}

// 전역 모킹 서버
let mockServer: MockSseServer;

describe('SSE Integration Tests', () => {
	beforeAll(async () => {
		// 모킹된 SSE 서버 시작
		mockServer = new MockSseServer();
		mockServer.start();

		// EventSource 모킹
		global.EventSource = MockEventSource as any;
	});

	afterAll(async () => {
		// 테스트 서버 종료
		mockServer.stop();

		// EventSource 복원
		global.EventSource = EventSource;
	});

	beforeEach(() => {
		// 각 테스트 전에 상태 초기화
		resetConnectionState();
		resetStats();
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.useRealTimers();
		vi.clearAllMocks();
	});

	describe('Connection Lifecycle', () => {
		it('기본 SSE 연결 생성 및 연결 확인', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			// 연결 완료 대기
			await waitForConnection(eventSource!);

			const state = getCurrentConnectionState();
			expect(state.status).toBe('connected');
			expect(mockServer.getClientCount()).toBe(1);

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});

		it('연결 종료 시 서버에서 클라이언트 제거', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			// 연결 완료 대기
			await waitForConnection(eventSource!);
			expect(mockServer.getClientCount()).toBe(1);

			// 연결 종료
			if (eventSource) {
				closeSseConnection(eventSource);
			}

			expect(mockServer.getClientCount()).toBe(0);
		});
	});

	describe('Message Communication', () => {
		it('서버에서 클라이언트로 메시지 전송', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			// 연결 완료 대기
			await waitForConnection(eventSource!);

			// 메시지 수신 대기 설정
			const receivedMessages: any[] = [];
			eventSource!.addEventListener('message', (event: MessageEvent) => {
				receivedMessages.push(JSON.parse(event.data));
			});

			// 테스트 메시지 전송
			const testMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 1,
					title: '테스트',
					message: '통합 테스트 메시지'
				}
			};

			mockServer.sendMessage(testMessage);

			// 메시지 수신 확인
			await new Promise((resolve) => setTimeout(resolve, 100));
			expect(receivedMessages).toHaveLength(1);
			expect(receivedMessages[0].data.title).toBe('테스트');

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});

		it('여러 메시지 타입 처리', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			await waitForConnection(eventSource!);

			const receivedMessages: any[] = [];
			eventSource!.addEventListener('message', (event: MessageEvent) => {
				receivedMessages.push(JSON.parse(event.data));
			});

			// 다양한 타입의 메시지 전송
			const messages = [
				{
					type: 'notification',
					data: { id: 1, title: '알림', message: '테스트 알림' }
				},
				{
					type: 'data_update',
					data: { model: 'employees', action: 'updated', payload: { id: 1 } }
				},
				{
					type: 'heartbeat',
					data: { timestamp: new Date().toISOString() }
				}
			];

			messages.forEach((message) => {
				mockServer.sendMessage(message);
			});

			await new Promise((resolve) => setTimeout(resolve, 200));
			expect(receivedMessages).toHaveLength(3);

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});
	});

	describe('Network Resilience', () => {
		it('네트워크 중단 및 복구 시나리오', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			// 초기 연결 확인
			await waitForConnection(eventSource!);
			let state = getCurrentConnectionState();
			expect(state.status).toBe('connected');

			// 네트워크 중단 시뮬레이션
			mockServer.disconnect();

			// 오류 상태 확인
			await new Promise((resolve) => setTimeout(resolve, 100));
			state = getCurrentConnectionState();
			expect(['error', 'reconnecting']).toContain(state.status);

			// 네트워크 복구
			mockServer.reconnect();

			// 재연결 확인
			await new Promise((resolve) => setTimeout(resolve, 600));
			state = getCurrentConnectionState();
			expect(state.status).toBe('connected');

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});

		it('수동 재연결 기능', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			await waitForConnection(eventSource!);

			// 연결 중단
			mockServer.disconnect();
			await new Promise((resolve) => setTimeout(resolve, 100));

			// 수동 재연결 시도
			await manualReconnect();

			// 재연결 확인
			await new Promise((resolve) => setTimeout(resolve, 200));
			const state = getCurrentConnectionState();
			expect(['connected', 'connecting']).toContain(state.status);

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});
	});

	describe('Performance and Load', () => {
		it('대량 메시지 처리 성능', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			await waitForConnection(eventSource!);

			const receivedMessages: any[] = [];
			eventSource!.addEventListener('message', (event: MessageEvent) => {
				receivedMessages.push(JSON.parse(event.data));
			});

			const messageCount = 50; // 통합 테스트에서는 적당한 수
			const startTime = performance.now();

			// 대량 메시지 전송
			for (let i = 0; i < messageCount; i++) {
				mockServer.sendMessage({
					type: 'notification',
					data: {
						id: i,
						title: `메시지 ${i}`,
						message: `테스트 메시지 ${i}`
					}
				});
			}

			// 모든 메시지 수신 대기
			await new Promise((resolve) => setTimeout(resolve, 500));
			const endTime = performance.now();

			expect(receivedMessages).toHaveLength(messageCount);
			expect(endTime - startTime).toBeLessThan(2000); // 2초 이내

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});

		it('동시 다중 연결 처리', async () => {
			const connections: EventSource[] = [];
			const connectionCount = 3;

			// 여러 연결 생성
			for (let i = 0; i < connectionCount; i++) {
				const eventSource = createDefaultSseConnection();
				if (eventSource) {
					connections.push(eventSource);
				}
			}

			// 모든 연결 완료 대기
			await Promise.all(connections.map((conn) => waitForConnection(conn)));

			expect(mockServer.getClientCount()).toBe(connectionCount);

			// 브로드캐스트 메시지 전송
			const testMessage = {
				type: 'broadcast',
				data: { message: '모든 클라이언트에게' }
			};

			mockServer.sendMessage(testMessage);

			// 메시지 수신 확인
			await new Promise((resolve) => setTimeout(resolve, 200));

			// 모든 연결 정리
			connections.forEach((conn) => closeSseConnection(conn));
			expect(mockServer.getClientCount()).toBe(0);
		});
	});

	describe('Error Handling', () => {
		it('서버 오류 시 적절한 오류 처리', async () => {
			const eventSource = createDefaultSseConnection();
			expect(eventSource).toBeDefined();

			await waitForConnection(eventSource!);

			// 서버 중지
			mockServer.stop();

			// 오류 발생 시뮬레이션
			(eventSource as any).simulateError();

			// 오류 상태 확인
			await new Promise((resolve) => setTimeout(resolve, 100));
			const state = getCurrentConnectionState();
			expect(['error', 'reconnecting']).toContain(state.status);

			// 서버 재시작
			mockServer.start();

			// 연결 정리
			if (eventSource) {
				closeSseConnection(eventSource);
			}
		});
	});
});

/**
 * 헬퍼 함수들
 */
async function waitForConnection(eventSource: EventSource): Promise<void> {
	return new Promise((resolve) => {
		if (eventSource.readyState === EventSource.OPEN) {
			resolve();
		} else {
			const onOpen = () => {
				eventSource.removeEventListener('open', onOpen);
				resolve();
			};
			eventSource.addEventListener('open', onOpen);

			// 타임아웃 설정 (5초)
			setTimeout(() => {
				eventSource.removeEventListener('open', onOpen);
				resolve(); // 타임아웃 시에도 resolve (테스트 계속 진행)
			}, 5000);
		}
	});
}

async function waitForMessage(eventSource: EventSource): Promise<MessageEvent> {
	return new Promise((resolve) => {
		const onMessage = (event: MessageEvent) => {
			eventSource.removeEventListener('message', onMessage);
			resolve(event);
		};
		eventSource.addEventListener('message', onMessage);

		// 타임아웃 설정
		setTimeout(() => {
			eventSource.removeEventListener('message', onMessage);
			resolve(new MessageEvent('message', { data: '{}' }));
		}, 3000);
	});
}

function getConnectionStatus(): string {
	return getCurrentConnectionState().status;
}
