/**
 * SSE 데이터 핸들러 - 직원 및 그룹 업데이트 테스트
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import {
	initializeDataHandlers,
	forceEmployeeUpdate,
	forceGroupUpdate,
	getEmployeeSyncStatus,
	getGroupSyncStatus
} from '../sseDataHandlers';
import { initializeWithMigration } from '../indexedDBManager';

// 데이터 구조에 대한 인터페이스 정의
interface Employee {
	id: number;
	name: string;
	email?: string;
	position?: string;
	group_ids?: number[];
	updated_at?: string;
	membership_changes?: {
		added: number[];
		removed: number[];
	};
}

interface Group {
	id: number;
	name: string;
	description?: string;
	member_ids?: number[];
	updated_at?: string;
	membership_changes?: {
		added: number[];
		removed: number[];
	};
}

// 모킹
vi.mock('../storeUpdaters', () => ({
	updateEmployeeStore: vi.fn(),
	updateGroupStore: vi.fn(),
	updateMessageStore: {
		update: vi.fn()
	},
	highlightedItemsStore: {
		update: vi.fn()
	},
	updatedItemsStore: {
		update: vi.fn()
	}
}));

describe('SSE 데이터 핸들러 - 직원 및 그룹 업데이트', () => {
	let db: IDBDatabase;

	beforeEach(async () => {
		// IndexedDB 초기화
		db = await initializeWithMigration();
		initializeDataHandlers(db);
	});

	afterEach(() => {
		if (db) {
			db.close();
		}
	});

	describe('직원 업데이트 처리', () => {
		it('단일 직원 생성을 처리해야 함', async () => {
			const payload: Employee = {
				id: 1,
				name: '김철수',
				email: '<EMAIL>',
				position: '개발자',
				group_ids: [1, 2]
			};

			await forceEmployeeUpdate('created', payload, [1]);

			// 데이터베이스에서 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');
			const result = await new Promise<Employee | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toEqual(payload);
		});

		it('다중 직원 생성을 처리해야 함', async () => {
			const payload: Employee[] = [
				{
					id: 1,
					name: '김철수',
					email: '<EMAIL>',
					group_ids: [1]
				},
				{
					id: 2,
					name: '이영희',
					email: '<EMAIL>',
					group_ids: [2]
				}
			];

			await forceEmployeeUpdate('created', payload, [1, 2]);

			// 데이터베이스에서 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');

			const results = await Promise.all([
				new Promise<Employee | undefined>((resolve) => {
					const request = store.get(1);
					request.onsuccess = () => resolve(request.result);
				}),
				new Promise<Employee | undefined>((resolve) => {
					const request = store.get(2);
					request.onsuccess = () => resolve(request.result);
				})
			]);

			expect(results[0]).toEqual(payload[0]);
			expect(results[1]).toEqual(payload[1]);
		});

		it('직원 정보 업데이트를 처리해야 함', async () => {
			// 먼저 직원 생성
			const originalEmployee: Employee = {
				id: 1,
				name: '김철수',
				email: '<EMAIL>',
				position: '개발자',
				group_ids: [1]
			};

			await forceEmployeeUpdate('created', originalEmployee, [1]);

			// 업데이트
			const updatedEmployee: Employee = {
				...originalEmployee,
				name: '김철수(수정)',
				position: '시니어 개발자',
				group_ids: [1, 2] // 그룹 추가
			};

			await forceEmployeeUpdate('updated', updatedEmployee, [1]);

			// 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');
			const result = await new Promise<Employee | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toEqual(updatedEmployee);
		});

		it('직원 삭제를 처리해야 함', async () => {
			// 먼저 직원 생성
			const employee: Partial<Employee> = {
				id: 1,
				name: '김철수',
				email: '<EMAIL>'
			};

			await forceEmployeeUpdate('created', employee, [1]);

			// 삭제
			await forceEmployeeUpdate('deleted', null, [1]);

			// 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');
			const result = await new Promise<Employee | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toBeUndefined();
		});

		it('그룹 멤버십 변경을 처리해야 함', async () => {
			// 직원과 그룹 생성
			const employee: Employee = {
				id: 1,
				name: '김철수',
				email: '<EMAIL>',
				group_ids: [1]
			};

			const group: Group = {
				id: 1,
				name: '개발팀',
				member_ids: [1]
			};

			await forceEmployeeUpdate('created', employee, [1]);
			await forceGroupUpdate('created', group, [1]);

			// 멤버십 변경
			const updatedEmployee = {
				...employee,
				group_ids: [1, 2], // 새 그룹 추가
				membership_changes: {
					added: [2],
					removed: []
				}
			};

			await forceEmployeeUpdate('group_membership_changed', updatedEmployee, [1]);

			// 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');
			const result = await new Promise<Employee | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result!.group_ids).toEqual([1, 2]);
		});
	});

	describe('그룹 업데이트 처리', () => {
		it('단일 그룹 생성을 처리해야 함', async () => {
			const payload: Group = {
				id: 1,
				name: '개발팀',
				description: '소프트웨어 개발팀',
				member_ids: [1, 2]
			};

			await forceGroupUpdate('created', payload, [1]);

			// 데이터베이스에서 확인
			const transaction = db.transaction(['groups'], 'readonly');
			const store = transaction.objectStore('groups');
			const result = await new Promise<Group | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toEqual(payload);
		});

		it('그룹 정보 업데이트를 처리해야 함', async () => {
			// 먼저 그룹 생성
			const originalGroup: Group = {
				id: 1,
				name: '개발팀',
				member_ids: [1]
			};

			await forceGroupUpdate('created', originalGroup, [1]);

			// 업데이트
			const updatedGroup = {
				...originalGroup,
				name: '소프트웨어 개발팀',
				member_ids: [1, 2] // 멤버 추가
			};

			await forceGroupUpdate('updated', updatedGroup, [1]);

			// 확인
			const transaction = db.transaction(['groups'], 'readonly');
			const store = transaction.objectStore('groups');
			const result = await new Promise<Group | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toEqual(updatedGroup);
		});

		it('그룹 삭제를 처리해야 함', async () => {
			// 먼저 그룹 생성
			const group: Partial<Group> = {
				id: 1,
				name: '개발팀'
			};

			await forceGroupUpdate('created', group, [1]);

			// 삭제
			await forceGroupUpdate('deleted', null, [1]);

			// 확인
			const transaction = db.transaction(['groups'], 'readonly');
			const store = transaction.objectStore('groups');
			const result = await new Promise<Group | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result).toBeUndefined();
		});

		it('그룹 멤버십 변경을 처리해야 함', async () => {
			// 그룹과 직원 생성
			const group: Group = {
				id: 1,
				name: '개발팀',
				member_ids: [1]
			};

			const employee: Partial<Employee> = {
				id: 1,
				name: '김철수',
				group_ids: [1]
			};

			await forceGroupUpdate('created', group, [1]);
			await forceEmployeeUpdate('created', employee, [1]);

			// 멤버십 변경
			const updatedGroup = {
				...group,
				member_ids: [1, 2], // 새 멤버 추가
				membership_changes: {
					added: [2],
					removed: []
				}
			};

			await forceGroupUpdate('membership_changed', updatedGroup, [1]);

			// 확인
			const transaction = db.transaction(['groups'], 'readonly');
			const store = transaction.objectStore('groups');
			const result = await new Promise<Group | undefined>((resolve) => {
				const request = store.get(1);
				request.onsuccess = () => resolve(request.result);
			});

			expect(result!.member_ids).toEqual([1, 2]);
		});
	});

	describe('동기화 상태 확인', () => {
		it('직원 동기화 상태를 확인해야 함', async () => {
			// 테스트 데이터 생성
			const employees: Employee[] = [
				{ id: 1, name: '김철수', group_ids: [1], updated_at: '2024-01-01T00:00:00Z' },
				{ id: 2, name: '이영희', group_ids: [1], updated_at: '2024-01-02T00:00:00Z' }
			];

			const groups: Group[] = [{ id: 1, name: '개발팀', member_ids: [1, 2] }];

			// 데이터 저장
			for (const emp of employees) {
				await forceEmployeeUpdate('created', emp, [emp.id]);
			}
			for (const group of groups) {
				await forceGroupUpdate('created', group, [group.id]);
			}

			const status = await getEmployeeSyncStatus();

			expect(status.totalEmployees).toBe(2);
			expect(status.lastUpdate).toBe('2024-01-02T00:00:00Z');
			expect(status.groupMembershipConsistency).toBe(true);
		});

		it('그룹 동기화 상태를 확인해야 함', async () => {
			// 테스트 데이터 생성
			const groups: Group[] = [
				{ id: 1, name: '개발팀', member_ids: [1, 2], updated_at: '2024-01-01T00:00:00Z' },
				{ id: 2, name: '디자인팀', member_ids: [3], updated_at: '2024-01-02T00:00:00Z' }
			];

			const employees: Employee[] = [
				{ id: 1, name: '김철수', group_ids: [1] },
				{ id: 2, name: '이영희', group_ids: [1] },
				{ id: 3, name: '박민수', group_ids: [2] }
			];

			// 데이터 저장
			for (const group of groups) {
				await forceGroupUpdate('created', group, [group.id]);
			}
			for (const emp of employees) {
				await forceEmployeeUpdate('created', emp, [emp.id]);
			}

			const status = await getGroupSyncStatus();

			expect(status.totalGroups).toBe(2);
			expect(status.lastUpdate).toBe('2024-01-02T00:00:00Z');
			expect(status.membershipConsistency).toBe(true);
			expect(status.averageMembersPerGroup).toBe(1.5); // (2 + 1) / 2 = 1.5
		});

		it('멤버십 불일치를 감지해야 함', async () => {
			// 불일치 데이터 생성
			const employee: Employee = { id: 1, name: '김철수', group_ids: [1, 2] }; // 그룹 2는 존재하지 않음
			const group: Group = { id: 1, name: '개발팀', member_ids: [1] };

			await forceEmployeeUpdate('created', employee, [1]);
			await forceGroupUpdate('created', group, [1]);

			const employeeStatus = await getEmployeeSyncStatus();
			expect(employeeStatus.groupMembershipConsistency).toBe(false);
		});
	});

	describe('배치 업데이트', () => {
		it('직원 배치 업데이트를 처리해야 함', async () => {
			const payload: Partial<Employee>[] = [
				{ id: 1, name: '김철수', email: '<EMAIL>' },
				{ id: 2, name: '이영희', email: '<EMAIL>' },
				{ id: 3, name: '박민수', email: '<EMAIL>' }
			];

			await forceEmployeeUpdate('batch_updated', payload, [1, 2, 3]);

			// 모든 직원이 저장되었는지 확인
			const transaction = db.transaction(['employees'], 'readonly');
			const store = transaction.objectStore('employees');

			const results = await Promise.all(
				payload.map(
					(emp) =>
						new Promise<Employee | undefined>((resolve) => {
							const request = store.get(emp.id!);
							request.onsuccess = () => resolve(request.result);
						})
				)
			);

			results.forEach((result, index) => {
				expect(result).toEqual(payload[index]);
			});
		});

		it('그룹 배치 업데이트를 처리해야 함', async () => {
			const payload: Group[] = [
				{ id: 1, name: '개발팀', member_ids: [1, 2] },
				{ id: 2, name: '디자인팀', member_ids: [3] },
				{ id: 3, name: '기획팀', member_ids: [4, 5] }
			];

			await forceGroupUpdate('batch_updated', payload, [1, 2, 3]);

			// 모든 그룹이 저장되었는지 확인
			const transaction = db.transaction(['groups'], 'readonly');
			const store = transaction.objectStore('groups');

			const results = await Promise.all(
				payload.map(
					(group) =>
						new Promise<Group | undefined>((resolve) => {
							const request = store.get(group.id);
							request.onsuccess = () => resolve(request.result);
						})
				)
			);

			results.forEach((result, index) => {
				expect(result).toEqual(payload[index]);
			});
		});
	});
});
