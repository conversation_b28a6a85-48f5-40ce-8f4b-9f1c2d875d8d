/**
 * SSE 연결 통합 테스트
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { initializeSseConnection, createDefaultSseConnection } from '../sseConnection';
import type { SseConnectionOptions } from '$lib/types/sseTypes';

// EventSource 모킹
class MockEventSource {
	url: string;
	withCredentials: boolean;
	readyState: number;
	onopen: ((event: Event) => void) | null = null;
	onmessage: ((event: MessageEvent) => void) | null = null;
	onerror: ((event: Event) => void) | null = null;

	private listeners: Map<string, ((event: Event) => void)[]> = new Map();

	constructor(url: string, options?: { withCredentials?: boolean }) {
		this.url = url;
		this.withCredentials = options?.withCredentials ?? false;
		this.readyState = 0; // CONNECTING

		// 연결 성공 시뮬레이션
		setTimeout(() => {
			this.readyState = 1; // OPEN
			const openEvent = new Event('open');
			this.onopen?.(openEvent);
			this.dispatchEvent('open', openEvent);
		}, 10);
	}

	addEventListener(type: string, listener: (event: Event) => void) {
		if (!this.listeners.has(type)) {
			this.listeners.set(type, []);
		}
		this.listeners.get(type)!.push(listener);
	}

	removeEventListener(type: string, listener: (event: Event) => void) {
		const listeners = this.listeners.get(type);
		if (listeners) {
			const index = listeners.indexOf(listener);
			if (index > -1) {
				listeners.splice(index, 1);
			}
		}
	}

	dispatchEvent(type: string, event: Event) {
		const listeners = this.listeners.get(type);
		if (listeners) {
			listeners.forEach((listener) => listener(event));
		}
	}

	close() {
		this.readyState = 2; // CLOSED
	}

	// 메시지 전송 시뮬레이션 (테스트용)
	simulateMessage(data: any, type: string = 'message') {
		const event = new MessageEvent(type, { data: JSON.stringify(data) });
		if (type === 'message') {
			this.onmessage?.(event);
		}
		this.dispatchEvent(type, event);
	}

	// 에러 시뮬레이션 (테스트용)
	simulateError() {
		const event = new Event('error');
		this.onerror?.(event);
		this.dispatchEvent('error', event);
	}
}

// 전역 EventSource 모킹
global.EventSource = MockEventSource as any;

// 기타 모킹
vi.mock('$lib/services/sseStats', () => ({
	recordConnection: vi.fn(),
	recordMessage: vi.fn(),
	recordError: vi.fn(),
	updateConnectionUptime: vi.fn(),
	recordReconnection: vi.fn(),
	startPerformanceMonitoring: vi.fn(),
	stopPerformanceMonitoring: vi.fn()
}));

vi.mock('$lib/services/sseConnectionState', () => ({
	updateConnectionStatus: vi.fn(),
	updateLatency: vi.fn(),
	updateHeartbeat: vi.fn(),
	startAllMonitoring: vi.fn(),
	stopAllMonitoring: vi.fn(),
	getCurrentConnectionState: vi.fn(() => ({
		status: 'connected',
		reconnectAttempts: 0,
		latency: 100
	})),
	incrementReconnectAttempts: vi.fn(),
	emitConnectionStatusChange: vi.fn(),
	updateNetworkStatus: vi.fn()
}));

vi.mock('$lib/services/sseMessageRouter', () => ({
	handleSseMessage: vi.fn(),
	handleNotificationEvent: vi.fn(),
	handleDataUpdateEvent: vi.fn(),
	routeMessage: vi.fn()
}));

vi.mock('$lib/services/sseDataSync', () => ({
	requestMissedData: vi.fn(() => Promise.resolve())
}));

vi.mock('$lib/services/sseMessageHistory', () => ({
	startHistoryManagement: vi.fn(),
	stopHistoryManagement: vi.fn()
}));

vi.mock('$lib/utils/authUtils', () => ({
	getAuthToken: vi.fn(() => 'test-token'),
	getCurrentUserId: vi.fn(() => 1)
}));

describe('SSE 연결 관리', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('initializeSseConnection', () => {
		it('SSE 연결을 초기화해야 함', () => {
			const url = 'http://localhost:3000/sse';
			const options: SseConnectionOptions = {
				withCredentials: true,
				autoReconnect: true
			};

			const eventSource = initializeSseConnection(url, options);

			expect(eventSource).toBeInstanceOf(MockEventSource);
			expect(eventSource.url).toBe(url);
			expect(eventSource.withCredentials).toBe(true);
		});

		it('이벤트 리스너가 올바르게 설정되어야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const mockOnOpen = vi.fn();
			const mockOnMessage = vi.fn();
			const mockOnError = vi.fn();

			const options: SseConnectionOptions = {
				onOpen: mockOnOpen,
				onMessage: mockOnMessage,
				onError: mockOnError
			};

			const eventSource = initializeSseConnection(url, options) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			expect(mockOnOpen).toHaveBeenCalled();
		});

		it('메시지를 올바르게 처리해야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const eventSource = initializeSseConnection(url) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			// 메시지 시뮬레이션
			const testMessage = {
				type: 'notification',
				timestamp: new Date().toISOString(),
				data: {
					id: 1,
					title: '테스트 알림',
					message: '테스트 메시지',
					priority: 'normal',
					target_type: 'all'
				}
			};

			eventSource.simulateMessage(testMessage);

			// routeMessage가 호출되었는지 확인
			const { routeMessage } = await import('../sseMessageRouter');
			expect(routeMessage).toHaveBeenCalledWith(testMessage);
		});

		it('빈 메시지를 올바르게 처리해야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const eventSource = initializeSseConnection(url) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			// 빈 메시지 시뮬레이션
			const emptyMessage = {};
			eventSource.simulateMessage(emptyMessage);

			// routeMessage가 호출되었는지 확인
			const { routeMessage } = await import('../sseMessageRouter');
			expect(routeMessage).toHaveBeenCalledWith({
				type: 'unknown',
				timestamp: expect.any(String),
				data: emptyMessage
			});
		});

		it('커스텀 이벤트를 올바르게 처리해야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const eventSource = initializeSseConnection(url) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			// 알림 이벤트 시뮬레이션
			const notificationData = {
				id: 1,
				title: '테스트 알림',
				message: '테스트 메시지',
				priority: 'high',
				target_type: 'all'
			};

			eventSource.simulateMessage(notificationData, 'notification');

			// routeMessage가 호출되었는지 확인
			const { routeMessage } = await import('../sseMessageRouter');
			expect(routeMessage).toHaveBeenCalledWith({
				type: 'notification',
				timestamp: expect.any(String),
				data: notificationData
			});
		});

		it('하트비트 이벤트를 올바르게 처리해야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const eventSource = initializeSseConnection(url) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			// 하트비트 이벤트 시뮬레이션
			const heartbeatData = {
				timestamp: new Date().toISOString()
			};

			eventSource.simulateMessage(heartbeatData, 'heartbeat');

			// routeMessage가 호출되었는지 확인
			const { routeMessage } = await import('../sseMessageRouter');
			expect(routeMessage).toHaveBeenCalledWith({
				type: 'heartbeat',
				timestamp: expect.any(String),
				data: heartbeatData
			});
		});

		it('연결 오류를 처리해야 함', async () => {
			const url = 'http://localhost:3000/sse';
			const mockOnError = vi.fn();
			const options: SseConnectionOptions = {
				onError: mockOnError
			};

			const eventSource = initializeSseConnection(url, options) as MockEventSource;

			// 연결 성공 대기
			await new Promise((resolve) => setTimeout(resolve, 20));

			const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

			// 에러 시뮬레이션
			eventSource.simulateError();

			expect(consoleErrorSpy).toHaveBeenCalledWith(
				expect.stringContaining('SSE 연결 오류'),
				expect.any(Event)
			);
			expect(mockOnError).toHaveBeenCalled();

			consoleErrorSpy.mockRestore();
		});
	});

	describe('createDefaultSseConnection', () => {
		it('기본 SSE 연결을 생성해야 함', () => {
			// 환경 변수 모킹
			vi.stubEnv('VITE_NODE_ENV', 'development');
			vi.stubEnv('VITE_SSE_ENDPOINT', 'http://localhost:3000/api/sse');

			const eventSource = createDefaultSseConnection();

			expect(eventSource).toBeInstanceOf(MockEventSource);
			expect(eventSource?.url).toContain('http://localhost:3000/api/sse');
		});

		it('EventSource를 지원하지 않는 환경에서 null을 반환해야 함', () => {
			// EventSource를 undefined로 설정
			const originalEventSource = global.EventSource;
			(global as any).EventSource = undefined;

			const eventSource = createDefaultSseConnection();

			expect(eventSource).toBeNull();

			// 복원
			global.EventSource = originalEventSource;
		});
	});
});
