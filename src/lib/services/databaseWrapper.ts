/**
 * 기존 PrinterDatabase.ts와의 호환성을 위한 래퍼 클래스
 * 기존 코드의 변경을 최소화하면서 새로운 통합 DB 시스템을 사용할 수 있도록 함
 */

import {
	initializeWithMigration,
	getData,
	getAllData,
	putData,
	clearStore,
	executeWithRetry
} from './indexedDBManager';
import type { PrintSetting } from '$lib/types/indexedDB';

/**
 * 데이터베이스 상태 관리
 */
let db: IDBDatabase | null = null;
let initPromise: Promise<IDBDatabase> | null = null;

/**
 * 데이터베이스 초기화
 */
async function initialize(): Promise<IDBDatabase> {
	if (db) {
		return db;
	}

	if (initPromise) {
		return initPromise;
	}

	initPromise = executeWithRetry(
		async () => {
			const database = await initializeWithMigration();
			db = database;
			return database;
		},
		3,
		1000
	);

	return initPromise;
}

/**
 * 데이터베이스 인스턴스 반환 (초기화 포함)
 */
async function getDB(): Promise<IDBDatabase> {
	if (!db) {
		await initialize();
	}
	return db!;
}

/**
 * 인쇄 설정 저장 (기존 PrinterDatabase 호환)
 *
 * @param settingName 설정 이름
 * @param settings 설정 데이터
 */
export async function savePrintSettingInternal(
	settingName: string,
	settings: any[]
): Promise<void> {
	const database = await getDB();

	const printSetting: PrintSetting = {
		settingName,
		settings,
		updatedAt: new Date().toISOString()
	};

	await putData(database, 'print_settings', printSetting);
	console.log(`인쇄 설정 저장 완료: ${settingName}`);
}

/**
 * 인쇄 설정 조회 (기존 PrinterDatabase 호환)
 *
 * @param settingName 설정 이름
 * @returns Promise<PrintSetting | null>
 */
export async function getPrintSettingInternal(settingName: string): Promise<PrintSetting | null> {
	const database = await getDB();
	const result = await getData(database, 'print_settings', settingName);
	return result || null;
}

/**
 * 모든 인쇄 설정 조회 (기존 PrinterDatabase 호환)
 *
 * @returns Promise<PrintSetting[]>
 */
export async function getAllPrintSettingsInternal(): Promise<PrintSetting[]> {
	const database = await getDB();
	return await getAllData(database, 'print_settings');
}

/**
 * 인쇄 설정 삭제 (기존 PrinterDatabase 호환)
 *
 * @param settingName 설정 이름
 */
export async function deletePrintSettingInternal(settingName: string): Promise<void> {
	const database = await getDB();
	await executeWithRetry(async () => {
		return new Promise<void>((resolve, reject) => {
			const transaction = database.transaction(['print_settings'], 'readwrite');
			const store = transaction.objectStore('print_settings');
			const request = store.delete(settingName);

			request.onerror = () => {
				console.error(`인쇄 설정 삭제 실패: ${settingName}`, request.error);
				reject(request.error);
			};

			request.onsuccess = () => {
				console.log(`인쇄 설정 삭제 완료: ${settingName}`);
				resolve();
			};
		});
	});
}

/**
 * 모든 인쇄 설정 삭제 (기존 PrinterDatabase 호환)
 */
export async function clearAllPrintSettingsInternal(): Promise<void> {
	const database = await getDB();
	await clearStore(database, 'print_settings');
	console.log('모든 인쇄 설정 삭제 완료');
}

/**
 * 카테고리 데이터 저장
 *
 * @param key 카테고리 키 (예: 'cate4', 'cate5')
 * @param categories 카테고리 데이터
 */
export async function saveCategories(key: string, categories: any[]): Promise<void> {
	const database = await getDB();

	const categoryData = {
		key,
		cate4: key === 'cate4' ? categories : [],
		cate5: key === 'cate5' ? categories : [],
		updated_at: new Date().toISOString()
	};

	await putData(database, 'categories', categoryData);
	console.log(`카테고리 데이터 저장 완료: ${key}`);
}

/**
 * 카테고리 데이터 조회
 *
 * @param key 카테고리 키
 * @returns Promise<any[] | null>
 */
export async function getCategories(key: string): Promise<any[] | null> {
	const database = await getDB();
	const result = await getData(database, 'categories', key);

	if (!result) return null;

	return key === 'cate4' ? result.cate4 : result.cate5;
}

/**
 * 설정 저장
 *
 * @param key 설정 키
 * @param value 설정 값
 */
export async function saveSetting(key: string, value: any): Promise<void> {
	const database = await getDB();

	const setting = {
		key,
		value,
		updated_at: new Date().toISOString()
	};

	await putData(database, 'settings', setting);
	console.log(`설정 저장 완료: ${key}`);
}

/**
 * 설정 조회
 *
 * @param key 설정 키
 * @returns Promise<any | null>
 */
export async function getSetting(key: string): Promise<any | null> {
	const database = await getDB();
	const result = await getData(database, 'settings', key);
	return result ? result.value : null;
}

/**
 * 데이터베이스 연결 종료
 */
export function closeDatabase(): void {
	if (db) {
		db.close();
		db = null;
		initPromise = null;
		console.log('데이터베이스 연결 종료');
	}
}

/**
 * 데이터베이스 상태 확인
 */
export function isDatabaseConnected(): boolean {
	return db !== null;
}

/**
 * 데이터베이스 정보 반환
 */
export async function getDatabaseInfo(): Promise<{
	name: string;
	version: number;
	stores: string[];
}> {
	const database = await getDB();

	return {
		name: database.name,
		version: database.version,
		stores: Array.from(database.objectStoreNames)
	};
}

// 기존 PrinterDatabase.ts의 함수들과 호환되는 래퍼 함수들
export async function savePrintSetting(settingName: string, settings: any[]): Promise<void> {
	return savePrintSettingInternal(settingName, settings);
}

export async function getPrintSetting(settingName: string): Promise<PrintSetting | null> {
	return getPrintSettingInternal(settingName);
}

export async function getAllPrintSettings(): Promise<PrintSetting[]> {
	return getAllPrintSettingsInternal();
}

export async function deletePrintSetting(settingName: string): Promise<void> {
	return deletePrintSettingInternal(settingName);
}

export async function clearAllPrintSettings(): Promise<void> {
	return clearAllPrintSettingsInternal();
}
