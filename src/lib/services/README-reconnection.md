# SSE 재연결 및 오류 복구 시스템

## 개요

SSE(Server-Sent Events) 연결의 안정성을 보장하기 위한 재연결 및 오류 복구 시스템입니다. 지수 백오프 알고리즘을 사용하여 서버 부하를 최소화하면서 안정적인 재연결을 제공합니다.

## 주요 기능

### 1. 지수 백오프 재연결 알고리즘

- **기본 원리**: 재연결 시도 간격을 지수적으로 증가시켜 서버 부하 감소
- **공식**: `지연시간 = 기본간격 × (배수 ^ (시도횟수 - 1))`
- **지터(Jitter)**: 동시 재연결 시도 분산을 위한 랜덤 지연 추가
- **최대 제한**: 설정된 최대 지연시간을 초과하지 않음

### 2. 적응형 재연결 전략

#### 적극적 전략 (Aggressive)

- 기본 간격: 500ms
- 최대 간격: 5초
- 최대 시도: 20회
- 배수: 1.5
- 사용 시나리오: 고속 네트워크, 업무 시간

#### 일반 전략 (Normal)

- 기본 간격: 1초
- 최대 간격: 30초
- 최대 시도: 10회
- 배수: 2
- 사용 시나리오: 일반적인 상황

#### 보수적 전략 (Conservative)

- 기본 간격: 2초
- 최대 간격: 60초
- 최대 시도: 5회
- 배수: 3
- 사용 시나리오: 저속 네트워크, 서버 부하 상황

### 3. 오류 복구 메커니즘

#### 네트워크 오류

- 온라인 상태 감지
- 오프라인 모드 자동 전환
- 온라인 복귀 시 자동 재연결

#### 서버 오류

- 서버 상태 확인
- 보수적 전략 자동 적용
- 단계적 복구 시도

#### 인증 오류

- 토큰 갱신 시도
- 로그인 페이지 리다이렉트
- 세션 복구

### 4. 최종 실패 처리

- 최대 재시도 횟수 도달 시 실행
- 사용자에게 수동 재연결 옵션 제공
- 오프라인 모드 활성화
- 알림 저장 모드 전환

## API 사용법

### 기본 재연결 스케줄링

```typescript
import { scheduleReconnection } from '$lib/services/sseConnection';

// 자동 재연결 스케줄링
scheduleReconnection('https://api.example.com/sse', {
	autoReconnect: true,
	maxReconnectAttempts: 10,
	reconnectInterval: 1000
});
```

### 수동 재연결

```typescript
import { manualReconnect } from '$lib/services/sseConnection';

// 사용자 요청에 의한 수동 재연결
try {
	await manualReconnect();
	console.log('재연결 성공');
} catch (error) {
	console.error('재연결 실패:', error);
}
```

### 재연결 전략 설정

```typescript
import { setReconnectionStrategy } from '$lib/services/sseConnection';

// 네트워크 상태에 따른 전략 변경
if (connection.effectiveType === '4g') {
	setReconnectionStrategy('aggressive');
} else if (connection.effectiveType === '2g') {
	setReconnectionStrategy('conservative');
}
```

### 재연결 취소

```typescript
import { cancelReconnection } from '$lib/services/sseConnection';

// 진행 중인 재연결 취소
cancelReconnection();
```

## 이벤트 시스템

### 재연결 진행 상황

```typescript
window.addEventListener('sse-reconnection-progress', (event) => {
	const { attempt, maxAttempts, delay } = event.detail;
	console.log(`재연결 시도 ${attempt}/${maxAttempts} (${delay}ms 후)`);
});
```

### 최종 실패

```typescript
window.addEventListener('sse-reconnection-final-failure', (event) => {
	const { attempts, strategy, message } = event.detail;
	console.error(`재연결 최종 실패: ${message}`);
	// 사용자에게 알림 표시
});
```

### 수동 재연결 요구

```typescript
window.addEventListener('sse-manual-reconnect-required', (event) => {
	const { message, reconnectAttempts } = event.detail;
	// 사용자에게 수동 재연결 옵션 제공
	showReconnectDialog(message);
});
```

## 성능 최적화

### 1. 서버 부하 분산

- 지터를 통한 동시 재연결 방지
- 지수 백오프로 점진적 간격 증가
- 최대 시도 횟수 제한

### 2. 네트워크 효율성

- 연결 품질 기반 전략 선택
- 시간대별 적응형 전략
- 불필요한 재연결 시도 방지

### 3. 사용자 경험

- 재연결 진행 상황 실시간 표시
- 오프라인 모드 자동 전환
- 수동 재연결 옵션 제공

## 모니터링 및 디버깅

### 로그 출력

```typescript
// 재연결 시도 로그
console.log('재연결 시도 1/10 (1500ms 후, 전략: 일반)');

// 지연시간 계산 로그
console.log('지연시간 계산: 시도=1, 기본=1000ms, 배수=2, 최종=1500ms');

// 전략 변경 로그
console.log('재연결 전략 변경: aggressive');
```

### 통계 수집

- 재연결 시도 횟수
- 성공/실패 비율
- 평균 재연결 시간
- 전략별 성능 지표

## 요구사항 충족

### 요구사항 1.4: 연결 오류 복구

- ✅ 자동 재연결 시스템
- ✅ 지수 백오프 알고리즘
- ✅ 최대 재시도 횟수 제한
- ✅ 최종 실패 처리
- ✅ 오프라인 모드 지원
- ✅ 수동 재연결 옵션

## 테스트 커버리지

- 지수 백오프 알고리즘 검증
- 재연결 전략별 특성 확인
- 최대 지연시간 제한 테스트
- 오류 복구 시나리오 검증
- 이벤트 시스템 동작 확인

## 향후 개선 사항

1. **머신러닝 기반 전략 선택**: 사용자 패턴 학습을 통한 최적 전략 자동 선택
2. **서버 부하 감지**: 서버 응답 시간 기반 동적 전략 조정
3. **지역별 최적화**: 지역별 네트워크 특성을 고려한 전략 커스터마이징
4. **배터리 최적화**: 모바일 디바이스의 배터리 상태 고려한 재연결 간격 조정
