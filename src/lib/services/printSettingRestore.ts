/**
 * PrintSetting 데이터 복구 유틸리티
 */

import { initializeWithMigration, putData } from './indexedDBManager';

/**
 * JSON 백업 데이터를 PrintSetting 형식으로 변환
 */
function convertBackupToPrintSettings(backupData: Record<string, any[]>): any[] {
	const printSettings: any[] = [];

	for (const [settingName, settings] of Object.entries(backupData)) {
		const printSetting = {
			settingName: settingName,
			settings: settings,
			updatedAt: new Date().toISOString(),
			migrated: false,
			restored: true,
			restoredAt: new Date().toISOString()
		};

		printSettings.push(printSetting);
	}

	return printSettings;
}

/**
 * 백업 데이터를 IndexedDB에 복원
 */
export async function restorePrintSettings(backupData: Record<string, any[]>): Promise<void> {
	try {
		console.log('🔄 PrintSetting 데이터 복원 시작...');

		// IndexedDB 초기화
		const db = await initializeWithMigration();

		// 백업 데이터를 PrintSetting 형식으로 변환
		const printSettings = convertBackupToPrintSettings(backupData);

		console.log(`📦 복원할 설정: ${printSettings.length}개`);

		// 각 설정을 DB에 저장
		for (const setting of printSettings) {
			await putData(db, 'print_settings', setting);
			console.log(`✅ "${setting.settingName}" 복원 완료`);
		}

		db.close();
		console.log('🎉 모든 PrintSetting 데이터 복원 완료!');

		// 복원 완료 알림
		alert(
			`PrintSetting 데이터 복원 완료!\n복원된 설정: ${printSettings.map((s) => s.settingName).join(', ')}`
		);
	} catch (error) {
		console.error('❌ PrintSetting 복원 실패:', error);
		alert('데이터 복원 중 오류가 발생했습니다: ' + error);
	}
}

/**
 * 브라우저 콘솔에서 사용할 수 있는 전역 함수
 */
declare global {
	interface Window {
		restorePrintSettingsFromBackup: (backupData: Record<string, any[]>) => Promise<void>;
	}
}

// 전역 함수로 등록
if (typeof window !== 'undefined') {
	window.restorePrintSettingsFromBackup = restorePrintSettings;
}

export default restorePrintSettings;
