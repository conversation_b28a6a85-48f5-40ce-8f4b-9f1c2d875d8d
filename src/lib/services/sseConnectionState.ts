/**
 * SSE 연결 상태 관리
 *
 * SSE 연결의 상태를 추적하고 관리합니다.
 */

import { writable, derived } from 'svelte/store';
import type { SseConnectionState, SseConnectionStatus } from '$lib/types/sseTypes';

/**
 * 초기 연결 상태
 */
const initialState: SseConnectionState = {
	status: 'disconnected',
	lastConnected: null,
	lastError: null,
	reconnectAttempts: 0,
	isOnline: navigator.onLine,
	latency: null,
	lastHeartbeat: null,
	connectionQuality: 'unknown'
};

/**
 * SSE 연결 상태 스토어
 */
export const sseConnectionState = writable<SseConnectionState>(initialState);

/**
 * 연결 상태 업데이트
 */
export function updateConnectionStatus(status: SseConnectionStatus, errorMessage?: string): void {
	sseConnectionState.update((state) => ({
		...state,
		status,
		lastConnected: status === 'connected' ? new Date() : state.lastConnected,
		lastError: errorMessage || (status === 'error' ? state.lastError : null),
		reconnectAttempts: status === 'connected' ? 0 : state.reconnectAttempts
	}));
}

/**
 * 재연결 시도 횟수 증가
 */
export function incrementReconnectAttempts(): void {
	sseConnectionState.update((state) => ({
		...state,
		reconnectAttempts: state.reconnectAttempts + 1
	}));
}

/**
 * 네트워크 상태 업데이트
 */
export function updateNetworkStatus(isOnline: boolean): void {
	sseConnectionState.update((state) => ({
		...state,
		isOnline
	}));
}

/**
 * 연결 상태 초기화
 */
export function resetConnectionState(): void {
	sseConnectionState.set(initialState);
}

/**
 * 연결 상태 파생 스토어들
 */
export const isConnected = derived(sseConnectionState, ($state) => $state.status === 'connected');

export const isConnecting = derived(sseConnectionState, ($state) => $state.status === 'connecting');

export const hasError = derived(sseConnectionState, ($state) => $state.status === 'error');

export const canReconnect = derived(
	sseConnectionState,
	($state) => $state.isOnline && $state.status !== 'connecting'
);

/**
 * 네트워크 상태 모니터링 초기화
 */
export function initializeNetworkMonitoring(): void {
	// 온라인/오프라인 이벤트 리스너
	window.addEventListener('online', () => {
		console.log('네트워크 연결됨');
		updateNetworkStatus(true);
	});

	window.addEventListener('offline', () => {
		console.log('네트워크 연결 끊김');
		updateNetworkStatus(false);
	});

	// 초기 네트워크 상태 설정
	updateNetworkStatus(navigator.onLine);
}

/**
 * 지연시간 업데이트
 */
export function updateLatency(latency: number): void {
	sseConnectionState.update((state) => {
		const quality = getConnectionQuality(latency);
		return {
			...state,
			latency,
			connectionQuality: quality,
			lastHeartbeat: new Date()
		};
	});
}

/**
 * 연결 품질 계산
 */
function getConnectionQuality(latency: number): 'excellent' | 'good' | 'poor' | 'unknown' {
	if (latency < 100) return 'excellent';
	if (latency < 300) return 'good';
	if (latency < 1000) return 'poor';
	return 'poor';
}

/**
 * 하트비트 업데이트
 */
export function updateHeartbeat(): void {
	sseConnectionState.update((state) => ({
		...state,
		lastHeartbeat: new Date()
	}));
}

/**
 * 연결 상태 이벤트 발생
 */
export function emitConnectionStatusChange(state: SseConnectionState): void {
	// 커스텀 이벤트 발생
	const event = new CustomEvent('sse-connection-status-change', {
		detail: state
	});
	window.dispatchEvent(event);

	// 로그 출력
	console.log(`SSE 연결 상태 변경: ${state.status}`);
}

/**
 * 디버그 및 헬스체크 표준화
 */
const HEALTH_CHECK_THRESHOLDS = {
	HEARTBEAT_TIMEOUT_MS: 120000, // 2분
	LATENCY_UNHEALTHY_THRESHOLD_MS: 5000,
	ADVANCED_LATENCY_HIGH_THRESHOLD_MS: 5000,
	ADVANCED_LATENCY_MEDIUM_THRESHOLD_MS: 1000,
	RECONNECT_ATTEMPTS_WARNING_THRESHOLD: 5
	// NO_MESSAGE_SINCE_CONNECTION_THRESHOLD_MS: 300000 // 5분. messageCount가 이동되어 이 로직은 sseStats와 결합해야 하므로 일단 주석 처리.
};

/**
 * 연결 건강성 검사
 */
export function checkConnectionHealth(): boolean {
	const state = getCurrentConnectionState();

	// 연결되어 있지 않으면 건강하지 않음
	if (state.status !== 'connected') {
		return false;
	}

	// 마지막 하트비트가 2분 이상 전이면 건강하지 않음
	if (state.lastHeartbeat) {
		const timeSinceHeartbeat = Date.now() - state.lastHeartbeat.getTime();
		if (timeSinceHeartbeat > HEALTH_CHECK_THRESHOLDS.HEARTBEAT_TIMEOUT_MS) {
			return false;
		}
	}

	// 지연시간이 너무 높으면 건강하지 않음
	if (state.latency && state.latency > HEALTH_CHECK_THRESHOLDS.LATENCY_UNHEALTHY_THRESHOLD_MS) {
		return false;
	}

	return true;
}

/**
 * 현재 연결 상태 가져오기 (동기)
 */
export function getCurrentConnectionState(): SseConnectionState {
	let currentState: SseConnectionState = initialState;
	sseConnectionState.subscribe((state) => {
		currentState = state;
	})();
	return currentState;
}

/**
 * 연결 상태별 파생 스토어들 추가
 */
export const isReconnecting = derived(
	sseConnectionState,
	($state) => $state.status === 'reconnecting'
);

export const connectionQuality = derived(sseConnectionState, ($state) => $state.connectionQuality);

export const currentLatency = derived(sseConnectionState, ($state) => $state.latency);

export const lastHeartbeat = derived(sseConnectionState, ($state) => $state.lastHeartbeat);

/**
 * 연결 상태 헬퍼 함수들
 */
export function getConnectionStatus(): SseConnectionStatus {
	return getCurrentConnectionState().status;
}

export function isConnectedState(): boolean {
	return getCurrentConnectionState().status === 'connected';
}

export function hasConnectionError(): boolean {
	return getCurrentConnectionState().status === 'error';
}

export function getLastError(): string | null {
	return getCurrentConnectionState().lastError;
}

export function getCurrentLatency(): number | null {
	return getCurrentConnectionState().latency;
}

export function getConnectionStats(): SseConnectionState {
	return { ...getCurrentConnectionState() };
}
/**
 * 페이지 가시성 모니터링
 */
let visibilityMonitoringActive = false;

/**
 * 페이지 가시성 모니터링 시작
 */
export function startVisibilityMonitoring(): void {
	if (visibilityMonitoringActive) {
		return;
	}

	document.addEventListener('visibilitychange', handleVisibilityChange);
	visibilityMonitoringActive = true;
	console.log('페이지 가시성 모니터링 시작');
}

/**
 * 페이지 가시성 모니터링 중지
 */
export function stopVisibilityMonitoring(): void {
	if (!visibilityMonitoringActive) {
		return;
	}

	document.removeEventListener('visibilitychange', handleVisibilityChange);
	visibilityMonitoringActive = false;
	console.log('페이지 가시성 모니터링 중지');
}

/**
 * 페이지 가시성 변경 처리
 */
function handleVisibilityChange(): void {
	if (document.visibilityState === 'visible') {
		console.log('페이지가 다시 보임 - 연결 상태 확인');

		// 페이지가 다시 보이면 연결 건강성 검사
		const isHealthy = checkConnectionHealth();
		if (!isHealthy) {
			console.warn('연결 상태가 좋지 않음 - 재연결 필요');
			// 재연결 이벤트 발생
			const event = new CustomEvent('sse-reconnection-needed', {
				detail: { reason: 'unhealthy_connection' }
			});
			window.dispatchEvent(event);
		}
	} else {
		console.log('페이지가 숨겨짐');
	}
}

/**
 * 고급 연결 건강성 검사
 */
export async function performAdvancedHealthCheck(): Promise<{
	isHealthy: boolean;
	issues: string[];
	recommendations: string[];
}> {
	const state = getCurrentConnectionState();
	const issues: string[] = [];
	const recommendations: string[] = [];

	// 기본 연결 상태 확인
	if (state.status !== 'connected') {
		issues.push(`연결 상태: ${state.status}`);
		recommendations.push('SSE 연결을 다시 시도하세요');
	}

	// 네트워크 상태 확인
	if (!state.isOnline) {
		issues.push('네트워크 연결 없음');
		recommendations.push('인터넷 연결을 확인하세요');
	}

	// 하트비트 확인
	if (state.lastHeartbeat) {
		const timeSinceHeartbeat = Date.now() - state.lastHeartbeat.getTime();
		if (timeSinceHeartbeat > HEALTH_CHECK_THRESHOLDS.HEARTBEAT_TIMEOUT_MS) {
			issues.push(`마지막 하트비트: ${Math.round(timeSinceHeartbeat / 1000)}초 전`);
			recommendations.push('서버 연결을 확인하세요');
		}
	} else if (state.status === 'connected') {
		issues.push('하트비트 데이터 없음');
		recommendations.push('연결을 재시작하세요');
	}

	// 지연시간 확인
	if (state.latency !== null) {
		if (state.latency > HEALTH_CHECK_THRESHOLDS.ADVANCED_LATENCY_HIGH_THRESHOLD_MS) {
			issues.push(`높은 지연시간: ${state.latency}ms`);
			recommendations.push('네트워크 연결을 확인하세요');
		} else if (state.latency > HEALTH_CHECK_THRESHOLDS.ADVANCED_LATENCY_MEDIUM_THRESHOLD_MS) {
			issues.push(`보통 지연시간: ${state.latency}ms`);
			recommendations.push('네트워크 상태를 모니터링하세요');
		}
	}

	// 재연결 시도 횟수 확인
	if (state.reconnectAttempts > HEALTH_CHECK_THRESHOLDS.RECONNECT_ATTEMPTS_WARNING_THRESHOLD) {
		issues.push(`과도한 재연결 시도: ${state.reconnectAttempts}회`);
		recommendations.push('서버 상태를 확인하거나 잠시 후 다시 시도하세요');
	}

	// 메시지 수신 확인 로직은 sseStats와 연동 필요하여 주석 처리
	// if (state.status === 'connected' && state.lastConnected) {
	// 	const connectionDuration = Date.now() - state.lastConnected.getTime();
	// 	if (connectionDuration > 300000 && state.messageCount === 0) {
	// 		// 5분
	// 		issues.push('메시지 수신 없음');
	// 		recommendations.push('서버에서 메시지를 전송하는지 확인하세요');
	// 	}
	// }

	const isHealthy = issues.length === 0;

	return {
		isHealthy,
		issues,
		recommendations
	};
}

/**
 * 연결 품질 모니터링 시작
 */
let qualityMonitoringInterval: NodeJS.Timeout | null = null;

export function startConnectionQualityMonitoring(): void {
	if (qualityMonitoringInterval) {
		clearInterval(qualityMonitoringInterval);
	}

	// 5분마다 연결 품질 확인 (300초)
	qualityMonitoringInterval = setInterval(async () => {
		const healthCheck = await performAdvancedHealthCheck();

		if (!healthCheck.isHealthy) {
			console.warn('연결 품질 문제 감지:', healthCheck.issues);

			// 연결 품질 문제 이벤트 발생
			const event = new CustomEvent('sse-connection-quality-issue', {
				detail: healthCheck
			});
			window.dispatchEvent(event);
		}
	}, 300000); // 5분 = 300,000ms

	console.log('연결 품질 모니터링 시작 (5분 간격)');
}

export function stopConnectionQualityMonitoring(): void {
	if (qualityMonitoringInterval) {
		clearInterval(qualityMonitoringInterval);
		qualityMonitoringInterval = null;
		console.log('연결 품질 모니터링 중지');
	}
}

/**
 * 전체 모니터링 시작
 */
export function startAllMonitoring(): void {
	initializeNetworkMonitoring();
	startVisibilityMonitoring();
	startConnectionQualityMonitoring();
	console.log('모든 연결 모니터링 시작');
}

/**
 * 전체 모니터링 중지
 */
export function stopAllMonitoring(): void {
	stopVisibilityMonitoring();
	stopConnectionQualityMonitoring();
	console.log('모든 연결 모니터링 중지');
}

/**
 * 연결 상태 디버그 정보
 */
export function getDebugInfo(): Record<string, any> {
	const state = getCurrentConnectionState();

	return {
		status: state.status,
		isOnline: state.isOnline,
		lastConnected: state.lastConnected?.toISOString(),
		lastError: state.lastError,
		reconnectAttempts: state.reconnectAttempts,
		latency: state.latency,
		lastHeartbeat: state.lastHeartbeat?.toISOString(),
		connectionQuality: state.connectionQuality,
		visibilityMonitoring: visibilityMonitoringActive,
		qualityMonitoring: qualityMonitoringInterval !== null,
		pageVisible: document.visibilityState === 'visible',
		userAgent: navigator.userAgent,
		timestamp: new Date().toISOString()
	};
}
