import type { NotificationData } from '$lib/types/notificationTypes';
import { handleNotificationClick } from '$lib/services/notifications/actions';
import { getNotificationIcon, getDisplayDuration, getNotificationStyle, isVibrationSupported } from '$lib/services/notifications/utils';
import { requestNotificationPermission } from '$lib/services/notifications/permissions';

// Map to track active urgent notification effects
export const activeUrgentNotifications = new Map<
	number,
	{ titleInterval: ReturnType<typeof setInterval>; originalTitle: string; faviconHref?: string }
>();

/**
 * 긴급 알림 효과 중지
 * @param notificationId 알림 ID
 */
export function stopUrgentNotificationEffects(notificationId: number) {
	if (activeUrgentNotifications.has(notificationId)) {
		const { titleInterval, originalTitle, faviconHref } =
			activeUrgentNotifications.get(notificationId)!;

		// 제목 깜빡임 중지 및 원래 제목으로 복원
		clearInterval(titleInterval);
		document.title = originalTitle;

		// 파비콘 복원
		if (faviconHref) {
			try {
				const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
				if (favicon) {
					favicon.href = faviconHref;
				}
			} catch (error) {
				console.warn('파비콘 복원 실패:', error);
			}
		}

		activeUrgentNotifications.delete(notificationId);
		console.log(`긴급 알림 효과 중지: ${notificationId}`);
	}
}

/**
 * 긴급 알림 추가 효과
 * 요구사항 7.5: 사용자가 명시적으로 확인할 때까지 알림 유지
 *
 * @param notification 알림 객체
 * @returns Promise<void>
 */
export async function showUrgentNotificationEffects(notification: NotificationData): Promise<void> {
	// 이미 해당 알림에 대한 효과가 실행 중이면 중복 실행 방지
	if (activeUrgentNotifications.has(notification.id)) {
		return;
	}

	// 페이지 제목 깜빡임 효과
	const originalTitle = document.title;
	const titleInterval = setInterval(() => {
		document.title = document.title === originalTitle ? `🚨 ${notification.title}` : originalTitle;
	}, 1000);

	let originalFaviconHref: string | undefined;

	// 브라우저 탭 파비콘 변경 (가능한 경우)
	try {
		const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
		if (favicon) {
			originalFaviconHref = favicon.href;
			favicon.href = '/icons/notification-urgent.png';
		}
	} catch (error) {
		console.warn('파비콘 변경 실패:', error);
	}

	// 실행중인 효과 정보 저장
	activeUrgentNotifications.set(notification.id, {
		titleInterval,
		originalTitle,
		faviconHref: originalFaviconHref
	});
}

/**
 * 알림 표시 (토스트)
 * 요구사항 1.6, 1.7: 우선순위별 알림 표시 스타일
 *
 * @param notification 알림 객체
 */
export function displayNotification(notification: NotificationData): void {
	try {
		// 우선순위별 표시 설정
		const displayConfig = {
			priority: notification.priority,
			duration: getDisplayDuration(notification.priority),
			style: getNotificationStyle(notification.priority),
			requireInteraction: notification.priority === 'urgent'
		};

		// 커스텀 이벤트로 UI 컴포넌트에 알림 전달
		const event = new CustomEvent('show-notification-toast', {
			detail: {
				...notification,
				displayConfig
			}
		});

		window.dispatchEvent(event);

		console.log(`토스트 알림 표시: ${notification.title} (우선순위: ${notification.priority})`);
	} catch (error) {
		console.error('토스트 알림 표시 실패:', error);
	}
}

/**
 * 브라우저 알림 표시
 * 요구사항 1.5, 7.4: 우선순위별 브라우저 알림 처리
 *
 * @param notification 알림 객체
 * @returns Promise<void>
 */
export async function showBrowserNotification(notification: NotificationData): Promise<void> {
	// 권한 확인 및 요청
	const hasPermission = await requestNotificationPermission();
	if (!hasPermission) {
		console.warn('브라우저 알림 권한이 없습니다');
		return;
	}

	try {
		const options: NotificationOptions = {
			body: notification.message,
			icon: getNotificationIcon(notification.priority),
			tag: notification.id.toString(),
			requireInteraction: ['urgent', 'high'].includes(notification.priority),
			silent: false
		};

		// 긴급 알림의 경우 추가 옵션
		if (notification.priority === 'urgent') {
			options.requireInteraction = true;
		}

		const browserNotification = new Notification(notification.title, options);

		// 이벤트 핸들러 설정
		browserNotification.onclick = () => {
			handleNotificationClick(notification);
			browserNotification.close();
		};

		browserNotification.onclose = () => {
			console.log(`브라우저 알림 닫힘: ${notification.id}`);
		};

		browserNotification.onerror = (error) => {
			console.error('브라우저 알림 오류:', error);
		};

		// 자동 닫기 (긴급 알림 제외)
		if (notification.priority !== 'urgent') {
			const duration = getDisplayDuration(notification.priority);

			if (duration > 0) {
				setTimeout(() => {
					browserNotification.close();
				}, duration);
			}
		}

		console.log(`브라우저 알림 표시: ${notification.title} (우선순위: ${notification.priority})`);
	} catch (error) {
		console.error('브라우저 알림 생성 실패:', error);
	}
}

/**
 * 알림 사운드 재생
 * 요구사항 6.6, 7.4: 우선순위별 사운드 처리
 *
 * @param priority 우선순위
 * @param customSoundFile 사용자 지정 사운드 파일 (선택사항)
 */
export function playNotificationSound(priority: string, customSoundFile?: string): void {
	try {
		const soundFiles: Record<string, string> = {
			low: '/sounds/notification-low.mp3',
			normal: '/sounds/notification.mp3',
			high: '/sounds/notification-high.mp3',
			urgent: '/sounds/notification-urgent.mp3'
		};

		// 사용자 지정 사운드가 있고 낮음/보통 우선순위인 경우 사용
		let soundFile: string;
		if (customSoundFile && ['low', 'normal'].includes(priority)) {
			soundFile = customSoundFile;
		} else {
			soundFile = soundFiles[priority] || soundFiles.normal;
		}

		const audio = new Audio(soundFile);

		// 우선순위별 볼륨 설정
		const volumes: Record<string, number> = {
			low: 0.3,
			normal: 0.5,
			high: 0.7,
			urgent: 0.9
		};

		audio.volume = volumes[priority] || 0.5;

		// 오디오 로드 완료 후 재생
		audio.addEventListener('canplaythrough', () => {
			audio.play().catch((error) => {
				console.error(`사운드 재생 실패 (${priority}):`, error);
				// 기본 사운드로 재시도
				tryFallbackSound(priority, soundFiles, volumes);
			});
		});

		// 로드 오류 시 기본 사운드로 재시도
		audio.addEventListener('error', () => {
			console.warn(`사운드 파일 로드 실패: ${soundFile}`);
			tryFallbackSound(priority, soundFiles, volumes);
		});

		// 긴급 알림의 경우 반복 재생
		if (priority === 'urgent') {
			audio.addEventListener('ended', () => {
				setTimeout(() => {
					const repeatAudio = new Audio(soundFile);
					repeatAudio.volume = 0.7;
					repeatAudio.play().catch(console.error);
				}, 1000);
			});
		}

		// 오디오 로드 시작
		audio.load();

		console.log(`알림 사운드 재생 시작: ${soundFile} (우선순위: ${priority})`);
	} catch (error) {
		console.error('사운드 재생 중 오류:', error);
	}
}

/**
 * 기본 사운드로 재시도
 *
 * @param priority 우선순위
 * @param soundFiles 사운드 파일 맵
 * @param volumes 볼륨 맵
 */
function tryFallbackSound(
	priority: string,
	soundFiles: Record<string, string>,
	volumes: Record<string, number>
): void {
	try {
		const fallbackAudio = new Audio(soundFiles.normal);
		fallbackAudio.volume = volumes[priority] || 0.5;
		fallbackAudio.play().catch((error) => {
			console.error('기본 사운드 재생도 실패:', error);
		});
	} catch (error) {
		console.error('기본 사운드 재시도 실패:', error);
	}
}

/**
 * 사운드 파일 사전 로드
 * 성능 최적화를 위한 사운드 파일 미리 로드
 *
 * @returns Promise<void>
 */
export async function preloadNotificationSounds(): Promise<void> {
	const soundFiles = [
		'/sounds/notification-low.mp3',
		'/sounds/notification.mp3',
		'/sounds/notification-high.mp3',
		'/sounds/notification-urgent.mp3'
	];

	const loadPromises = soundFiles.map((soundFile) => {
		return new Promise<void>((resolve) => {
			const audio = new Audio(soundFile);
			audio.addEventListener('canplaythrough', () => resolve());
			audio.addEventListener('error', () => {
				console.warn(`사운드 파일 사전 로드 실패: ${soundFile}`);
				resolve(); // 실패해도 계속 진행
			});
			audio.load();
		});
	});

	try {
		await Promise.all(loadPromises);
		console.log('알림 사운드 파일 사전 로드 완료');
	} catch (error) {
		console.error('사운드 파일 사전 로드 중 오류:', error);
	}
}

/**
 * 디바이스 진동
 * 요구사항 6.7, 7.4: 우선순위별 진동 패턴 처리
 *
 * @param priority 우선순위
 */
export function vibrateDevice(priority: string): void {
	if (!isVibrationSupported()) {
		console.warn('이 디바이스는 진동을 지원하지 않습니다');
		return;
	}

	try {
		const patterns: Record<string, number[]> = {
			low: [100], // 짧은 진동
			normal: [200], // 보통 진동
			high: [300, 100, 300], // 긴-짧은-긴 패턴
			urgent: [500, 200, 500, 200, 500, 200, 500] // 반복적인 강한 진동
		};

		const pattern = patterns[priority] || patterns.normal;

		// 진동 실행
		const success = navigator.vibrate(pattern);

		if (success) {
			console.log(`디바이스 진동 실행: ${priority} 패턴 [${pattern.join(', ')}]`);
		} else {
			console.warn('진동 실행 실패 - 디바이스에서 거부됨');
		}

		// 긴급 알림의 경우 3초 후 한 번 더 진동
		if (priority === 'urgent') {
			setTimeout(() => {
				const success = navigator.vibrate([300, 100, 300]);
				if (success) {
					console.log('긴급 알림 추가 진동 실행');
				}
			}, 3000);
		}
	} catch (error) {
		console.error('진동 실행 중 오류:', error);
	}
}

/**
 * 진동 중지
 * 현재 실행 중인 진동을 중지합니다
 */
export function stopVibration(): void {
	if (isVibrationSupported()) {
		try {
			navigator.vibrate(0);
			console.log('진동 중지됨');
		} catch (error) {
			console.error('진동 중지 실패:', error);
		}
	}
}

/**
 * 커스텀 진동 패턴 실행
 *
 * @param pattern 진동 패턴 (밀리초 배열)
 * @param description 패턴 설명 (로깅용)
 */
export function vibrateCustomPattern(pattern: number[], description?: string): void {
	if (!isVibrationSupported()) {
		console.warn('이 디바이스는 진동을 지원하지 않습니다');
		return;
	}

	try {
		const success = navigator.vibrate(pattern);
		if (success) {
			console.log(
				`커스텀 진동 실행${description ? ` (${description})` : ''}: [${pattern.join(', ')}]`
			);
		} else {
			console.warn('커스텀 진동 실행 실패');
		}
	} catch (error) {
		console.error('커스텀 진동 실행 중 오류:', error);
	}
}
