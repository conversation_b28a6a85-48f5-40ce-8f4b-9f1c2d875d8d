import type { NotificationData } from '$lib/types/notificationTypes';
import { initializeIndexedDB, getData, putData, getAllData, putManyData, deleteData } from '$lib/services/indexedDBManager';
import { updateNotificationStore } from '$lib/services/storeUpdaters';
import { stopUrgentNotificationEffects } from '$lib/services/notifications/display';

/**
 * 알림 클릭 처리
 * 요구사항 2.1, 2.5: 알림 상호작용 처리
 *
 * @param notification 알림 객체
 */
export function handleNotificationClick(notification: NotificationData): void {
	console.log(`알림 클릭 처리 시작: ${notification.id}`);

	// 긴급 알림 효과 중지
	if (notification.priority === 'urgent') {
		stopUrgentNotificationEffects(notification.id);
	}

	// 읽음 처리
	markNotificationAsRead(notification.id);

	// 액션 URL 처리
	if (notification.action_url) {
		handleNotificationActionUrl(notification.action_url, notification.id);
	}

	// 알림 클릭 이벤트 발생
	const event = new CustomEvent('notification-clicked', {
		detail: notification
	});
	window.dispatchEvent(event);

	console.log(`알림 클릭 처리 완료: ${notification.id}`);
}

/**
 * 알림 액션 URL 처리
 * 요구사항 2.5: 알림에 액션 버튼이 포함되면 해당 액션을 실행하고 결과를 표시
 *
 * @param actionUrl 액션 URL
 * @param notificationId 알림 ID (로깅용)
 */
export function handleNotificationActionUrl(actionUrl: string, notificationId?: number): void {
	try {
		console.log(`액션 URL 처리 시작: ${actionUrl} (알림 ID: ${notificationId})`);

		// URL 유효성 검사
		if (!actionUrl) {
			console.warn('유효하지 않은 액션 URL:', actionUrl);
			return;
		}

		// 상대 경로 처리
		let processedUrl = actionUrl;
		if (actionUrl.startsWith('/')) {
			processedUrl = window.location.origin + actionUrl;
		}

		// URL 객체 생성 및 검증
		const url = new URL(processedUrl, window.location.origin);

		// 보안 검사 - javascript: 프로토콜 차단
		if (url.protocol === 'javascript:') {
			console.error('보안상 javascript: 프로토콜은 허용되지 않습니다');
			showActionUrlError('보안상 허용되지 않는 링크입니다');
			return;
		}

		// 내부 링크와 외부 링크 구분 처리
		if (url.origin === window.location.origin) {
			// 내부 링크 - 현재 창에서 이동
			console.log(`내부 링크로 이동: ${url.href}`);

			// SPA 라우팅 지원 (SvelteKit의 경우)
			if (window.history && window.history.pushState) {
				// 페이지 새로고침 없이 라우팅
				window.history.pushState({}, '', url.pathname + url.search + url.hash);

				// popstate 이벤트 발생으로 라우터에 알림
				window.dispatchEvent(new PopStateEvent('popstate', { state: {} }));
			} else {
				// 일반적인 페이지 이동
				window.location.href = url.href;
			}
		} else {
			// 외부 링크 - 새 창에서 열기
			console.log(`외부 링크를 새 창에서 열기: ${url.href}`);

			const newWindow = window.open(url.href, '_blank', 'noopener,noreferrer');

			if (!newWindow) {
				console.warn('팝업이 차단되었습니다');
				showActionUrlError('팝업이 차단되었습니다. 브라우저 설정을 확인해주세요');
			}
		}

		// 성공 로그
		console.log(`액션 URL 처리 완료: ${url.href}`);

		// 액션 실행 이벤트 발생
		const event = new CustomEvent('notification-action-executed', {
			detail: {
				notificationId,
				actionUrl: url.href,
				isExternal: url.origin !== window.location.origin,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(event);
	} catch (error: any) {
		console.error('액션 URL 처리 실패:', error);

		// 에러 처리 - 사용자에게 알림
		showActionUrlError('링크를 열 수 없습니다. URL을 확인해주세요');

		// 에러 이벤트 발생
		const errorEvent = new CustomEvent('notification-action-error', {
			detail: {
				notificationId,
				actionUrl,
				error: error.message,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(errorEvent);
	}
}

/**
 * 액션 URL 처리 에러 표시
 *
 * @param message 에러 메시지
 */
function showActionUrlError(message: string): void {
	// 토스트 메시지로 에러 표시
	const event = new CustomEvent('show-notification-toast', {
		detail: {
			id: crypto.randomUUID(),
			title: '링크 오류',
			message,
			priority: 'normal',
			type: 'error',
			displayConfig: {
				priority: 'normal',
				duration: 5000,
				style: {
					backgroundColor: '#fee2e2',
					borderColor: '#ef4444',
					textColor: '#991b1b',
					iconColor: '#ef4444'
				}
			}
		}
	});
	window.dispatchEvent(event);
}

/**
 * 알림 읽음 처리
 * 요구사항 2.1: 직원이 알림을 클릭하면 시스템은 알림을 읽음 상태로 변경
 *
 * @param notificationId 알림 ID
 * @returns Promise<boolean> 처리 성공 여부
 */
export async function markNotificationAsRead(notificationId: number): Promise<boolean> {
	try {
		console.log(`알림 읽음 처리 시작: ${notificationId}`);

		const db = await initializeIndexedDB();
		const notification = await getData(db, 'notifications', notificationId);

		if (!notification) {
			console.warn(`알림을 찾을 수 없습니다: ${notificationId}`);
			return false;
		}

		// 이미 읽음 상태인 경우
		if (notification.read) {
			console.log(`이미 읽음 상태인 알림: ${notificationId}`);
			return true;
		}

		// 읽음 상태로 업데이트
		notification.read = true;
		notification.read_at = new Date().toISOString();

		// IndexedDB에 저장
		await putData(db, 'notifications', notification);

		// 스토어 업데이트
		await updateNotificationStore(db);

		// 읽음 처리 이벤트 발생
		const event = new CustomEvent('notification-read', {
			detail: {
				notificationId,
				notification,
				timestamp: notification.read_at
			}
		});
		window.dispatchEvent(event);

		console.log(`알림 읽음 처리 완료: ${notificationId}`);
		return true;
	} catch (error: any) {
		console.error(`알림 읽음 처리 실패 (${notificationId}):`, error);

		// 에러 이벤트 발생
		const errorEvent = new CustomEvent('notification-read-error', {
			detail: {
				notificationId,
				error: error.message,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(errorEvent);

		return false;
	}
}

/**
 * 다중 알림 읽음 처리
 * 요구사항 2.1: 여러 알림을 한 번에 읽음 처리
 *
 * @param notificationIds 알림 ID 배열
 * @returns Promise<{ success: number[], failed: number[] }> 처리 결과
 */
export async function markMultipleNotificationsAsRead(
	notificationIds: number[]
): Promise<{ success: number[]; failed: number[] }> {
	const results: { success: number[]; failed: number[] } = { success: [], failed: [] };

	console.log(`다중 알림 읽음 처리 시작: ${notificationIds.length}개`);

	for (const notificationId of notificationIds) {
		try {
			const success = await markNotificationAsRead(notificationId);
			if (success) {
				results.success.push(notificationId);
			} else {
				results.failed.push(notificationId);
			}
		} catch (error) {
			console.error(`알림 읽음 처리 실패 (${notificationId}):`, error);
			results.failed.push(notificationId);
		}
	}

	console.log(
		`다중 알림 읽음 처리 완료 - 성공: ${results.success.length}개, 실패: ${results.failed.length}개`
	);

	// 다중 읽음 처리 이벤트 발생
	const event = new CustomEvent('multiple-notifications-read', {
		detail: {
			results,
			timestamp: new Date().toISOString()
		}
	});
	window.dispatchEvent(event);

	return results;
}

/**
 * 모든 알림 읽음 처리
 * 요구사항 2.1: 모든 읽지 않은 알림을 읽음 상태로 변경
 *
 * @returns Promise<number> 처리된 알림 수
 */
export async function markAllNotificationsAsRead(): Promise<number> {
	try {
		console.log('모든 알림 읽음 처리 시작');

		const db = await initializeIndexedDB();
		const allNotifications = await getAllData(db, 'notifications');

		// 읽지 않은 알림만 필터링
		const unreadNotifications = allNotifications.filter((notification) => !notification.read);

		if (unreadNotifications.length === 0) {
			console.log('읽지 않은 알림이 없습니다');
			return 0;
		}

		// 모든 읽지 않은 알림을 읽음 상태로 변경
		const updatedNotifications = unreadNotifications.map((notification) => ({
			...notification,
			read: true,
			read_at: new Date().toISOString()
		}));

		// 배치 업데이트
		await putManyData(db, 'notifications', updatedNotifications);

		// 스토어 업데이트
		await updateNotificationStore(db);

		// 모든 알림 읽음 처리 이벤트 발생
		const event = new CustomEvent('all-notifications-read', {
			detail: {
				count: updatedNotifications.length,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(event);

		console.log(`모든 알림 읽음 처리 완료: ${updatedNotifications.length}개`);
		return updatedNotifications.length;
	} catch (error) {
		console.error('모든 알림 읽음 처리 실패:', error);
		return 0;
	}
}

/**
 * 알림 읽음 상태 토글
 * 읽음 상태를 반대로 변경
 *
 * @param notificationId 알림 ID
 * @returns Promise<boolean> 변경 후 읽음 상태
 */
export async function toggleNotificationReadStatus(
	notificationId: number
): Promise<boolean | null> {
	try {
		console.log(`알림 읽음 상태 토글: ${notificationId}`);

		const db = await initializeIndexedDB();
		const notification = await getData(db, 'notifications', notificationId);

		if (!notification) {
			console.warn(`알림을 찾을 수 없습니다: ${notificationId}`);
			return null;
		}

		// 읽음 상태 토글
		const newReadStatus = !notification.read;
		notification.read = newReadStatus;

		if (newReadStatus) {
			notification.read_at = new Date().toISOString();
		} else {
			delete notification.read_at;
		}

		// IndexedDB에 저장
		await putData(db, 'notifications', notification);

		// 스토어 업데이트
		await updateNotificationStore(db);

		// 상태 변경 이벤트 발생
		const event = new CustomEvent('notification-read-status-changed', {
			detail: {
				notificationId,
				newReadStatus,
				notification,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(event);

		console.log(`알림 읽음 상태 토글 완료: ${notificationId} -> ${newReadStatus}`);
		return newReadStatus;
	} catch (error) {
		console.error(`알림 읽음 상태 토글 실패 (${notificationId}):`, error);
		return null;
	}
}

/**
 * 알림 삭제
 * 요구사항 2.3: 직원이 알림을 삭제하면 시스템은 해당 알림을 목록에서 제거
 *
 * @param db IDBDatabase 객체
 * @param notificationId 알림 ID
 * @returns Promise<boolean> 삭제 성공 여부
 */
export async function deleteNotification(
	db: IDBDatabase,
	notificationId: number
): Promise<boolean> {
	try {
		console.log(`알림 삭제 시작: ${notificationId}`);

		// 알림 존재 여부 확인
		const notification = await getData(db, 'notifications', notificationId);
		if (!notification) {
			console.warn(`삭제할 알림을 찾을 수 없습니다: ${notificationId}`);
			return false;
		}

		// IndexedDB에서 삭제
		await deleteData(db, 'notifications', notificationId);

		// 스토어 업데이트
		await updateNotificationStore(db);

		// 삭제 이벤트 발생
		const event = new CustomEvent('notification-deleted', {
			detail: {
				notificationId,
				notification,
				timestamp: new Date().toISOString()
			}
		});
		window.dispatchEvent(event);

		console.log(`알림 삭제 완료: ${notificationId}`);
		return true;
	} catch (error) {
		console.error(`알림 삭제 실패 (${notificationId}):`, error);
		return false;
	}
}

/**
 * 다중 알림 삭제
 * 요구사항 2.3: 여러 알림을 한 번에 삭제
 *
 * @param db IDBDatabase 객체
 * @param notificationIds 알림 ID 배열
 * @returns Promise<{ success: number[], failed: number[] }> 삭제 결과
 */
export async function deleteMultipleNotifications(
	db: IDBDatabase,
	notificationIds: number[]
): Promise<{ success: number[]; failed: number[] }> {
	const results: { success: number[]; failed: number[] } = { success: [], failed: [] };

	console.log(`다중 알림 삭제 시작: ${notificationIds.length}개`);

	for (const notificationId of notificationIds) {
		try {
			const success = await deleteNotification(db, notificationId);
			if (success) {
				results.success.push(notificationId);
			} else {
				results.failed.push(notificationId);
			}
		} catch (error) {
			console.error(`알림 삭제 실패 (${notificationId}):`, error);
			results.failed.push(notificationId);
		}
	}

	console.log(
		`다중 알림 삭제 완료 - 성공: ${results.success.length}개, 실패: ${results.failed.length}개`
	);

	// 다중 삭제 이벤트 발생
	const event = new CustomEvent('multiple-notifications-deleted', {
		detail: {
			results,
			timestamp: new Date().toISOString()
		}
	});
	window.dispatchEvent(event);

	return results;
}
