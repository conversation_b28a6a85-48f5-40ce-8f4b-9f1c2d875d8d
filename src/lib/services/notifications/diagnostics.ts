import type { NotificationData } from '$lib/types/notificationTypes';
import type { NotificationSettings } from '$lib/services/notificationSettings';
import { getNotificationSettings } from '$lib/services/notificationSettings';
import { initializeIndexedDB } from '$lib/services/indexedDBManager';
import { showBrowserNotification, playNotificationSound, vibrateDevice } from '$lib/services/notifications/display';
import { isVibrationSupported } from '$lib/services/notifications/utils';
import { getNotificationPermissionStatus } from '$lib/services/notifications/permissions';

export function validateNotificationSettings(settings: NotificationSettings): {
	isValid: boolean;
	errors: string[];
	warnings: string[];
} {
	const errors: string[] = [];
	const warnings: string[] = [];

	// 근무 시간 형식 검증
	if (settings.workingHours.enabled) {
		const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

		if (!timeRegex.test(settings.workingHours.start)) {
			errors.push(`잘못된 근무 시작 시간 형식: ${settings.workingHours.start}`);
		}

		if (!timeRegex.test(settings.workingHours.end)) {
			errors.push(`잘못된 근무 종료 시간 형식: ${settings.workingHours.end}`);
		}

		// 시작 시간이 종료 시간보다 늦은 경우 경고
		if (settings.workingHours.start >= settings.workingHours.end) {
			warnings.push('근무 시작 시간이 종료 시간보다 늦습니다. 야간 근무인지 확인해주세요.');
		}
	}

	// 사운드 파일 경로 검증
	if (
		settings.soundFile &&
		!settings.soundFile.startsWith('/') &&
		!settings.soundFile.startsWith('http')
	) {
		warnings.push(`사운드 파일 경로가 상대 경로입니다: ${settings.soundFile}`);
	}

	// 표시 지속시간 검증
	Object.entries(settings.displayDuration).forEach(([priority, duration]) => {
		const durationValue = duration as number;
		if (priority !== 'urgent' && (durationValue < 1000 || durationValue > 30000)) {
			warnings.push(
				`${priority} 우선순위 표시 지속시간이 권장 범위(1-30초)를 벗어납니다: ${durationValue}ms`
			);
		}
	});

	// 모든 알림이 비활성화된 경우 경고
	if (!settings.enableBrowserNotifications && !settings.enableSounds && !settings.enableVibration) {
		warnings.push('모든 알림 방식이 비활성화되어 있습니다. 긴급 알림을 놓칠 수 있습니다.');
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings
	};
}

/**
 * 알림 시스템 테스트 함수
 * 개발 및 디버깅용 테스트 알림 생성
 *
 * @param priority 테스트할 우선순위
 * @param testType 테스트 타입 ('all' | 'browser' | 'sound' | 'vibration')
 */
export async function testNotificationSystem(
	priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal',
	testType: 'all' | 'browser' | 'sound' | 'vibration' = 'all'
): Promise<void> {
	console.log(`알림 시스템 테스트 시작 - 우선순위: ${priority}, 타입: ${testType}`);

	const testNotification: NotificationData = {
		read: false, received_at: '', target_type: 'group',
		id: Date.now(),
		title: `테스트 알림 (${priority})`,
		message: `이것은 ${priority} 우선순위 테스트 알림입니다.`,
		type: 'test',
		priority
	};

	// const db = await initializeIndexedDB();
	// const settings = await getNotificationSettings(db);

	try {
		if (testType === 'all' || testType === 'browser') {
			console.log('브라우저 알림 테스트...');
			await showBrowserNotification(testNotification);
		}

		if (testType === 'all' || testType === 'sound') {
			console.log('사운드 알림 테스트...');
			playNotificationSound(priority);
		}

		if (testType === 'all' || testType === 'vibration') {
			console.log('진동 알림 테스트...');
			vibrateDevice(priority);
		}

		console.log('알림 시스템 테스트 완료');
	} catch (error) {
		console.error('알림 시스템 테스트 실패:', error);
	}
}

/**
 * 알림 시스템 상태 진단
 * 현재 알림 시스템의 상태와 설정을 확인합니다
 *
 * @returns Promise<object> 진단 결과
 */
export async function diagnoseNotificationSystem(): Promise<{
	browser: {
		supported: boolean;
		permission: string;
		canRequest: boolean;
	};
	sound: {
		supported: boolean;
		context: string;
	};
	vibration: {
		supported: boolean;
	};
	settings: NotificationSettings;
	recommendations: string[];
}> {
	const browserStatus = getNotificationPermissionStatus();
	const db = await initializeIndexedDB();
	const settings = await getNotificationSettings(db);
	const recommendations: string[] = [];

	// 브라우저 알림 상태
	const browser = {
		supported: browserStatus.supported,
		permission: browserStatus.permission || 'unknown',
		canRequest: browserStatus.canRequest
	};

	if (!browser.supported) {
		recommendations.push('브라우저가 알림을 지원하지 않습니다');
	} else if (browser.permission === 'denied') {
		recommendations.push('브라우저 알림 권한이 거부되었습니다. 브라우저 설정에서 허용해주세요');
	} else if (browser.permission === 'default') {
		recommendations.push('브라우저 알림 권한을 요청하세요');
	}

	// 사운드 상태
	const sound = {
		supported: typeof Audio !== 'undefined',
		context: typeof AudioContext !== 'undefined' ? 'available' : 'not available'
	};

	if (!sound.supported) {
		recommendations.push('이 환경에서는 사운드 재생이 지원되지 않습니다');
	}

	// 진동 상태
	const vibration = {
		supported: isVibrationSupported()
	};

	if (!vibration.supported) {
		recommendations.push('이 디바이스는 진동을 지원하지 않습니다');
	}

	// 설정 검증
	const validation = validateNotificationSettings(settings);
	if (!validation.isValid) {
		recommendations.push(...validation.errors);
	}
	recommendations.push(...validation.warnings);

	// 모든 알림이 비활성화된 경우
	if (!settings.enableBrowserNotifications && !settings.enableSounds && !settings.enableVibration) {
		recommendations.push('모든 알림 방식이 비활성화되어 있습니다');
	}

	return {
		browser,
		sound,
		vibration,
		settings,
		recommendations
	};
}

/**
 * 간단한 브라우저 알림 표시
 * @param title 알림 제목
 * @param message 알림 메시지
 * @param icon 알림 아이콘 경로
 * @param tag 알림 태그
 */
export function showSimpleBrowserNotification(
	title: string,
	message: string,
	icon: string,
	tag: string
): void {
	try {
		if (
			typeof window !== 'undefined' &&
			'Notification' in window &&
			Notification.permission === 'granted'
		) {
			const notification = new Notification(title, {
				body: message,
				icon: icon,
				tag: tag,
				silent: true,
				requireInteraction: false
			});

			// 5초 후 자동 닫기
			setTimeout(() => {
				notification.close();
			}, 5000);

			console.log('브라우저 알림 표시:', title, message);
		} else {
			console.log('브라우저 알림 (시뮬레이션):', title, message);
		}
	} catch (error) {
		console.error('브라우저 알림 표시 실패:', error);
	}
}
