import { writable } from 'svelte/store';

// 권한 상태 스토어
export const notificationPermissionStore = writable<NotificationPermission>('default');
export const permissionRequestInProgressStore = writable<boolean>(false);
export const alternativeNotificationModeStore = writable<boolean>(false);

/**
 * 브라우저 알림 권한 요청 및 확인
 * 요구사항 1.5: 브라우저 알림 권한 관리
 *
 * @returns Promise<boolean>
 */
export async function requestNotificationPermission(): Promise<boolean> {
	if (!('Notification' in window)) {
		console.warn('이 브라우저는 알림을 지원하지 않습니다');
		return false;
	}

	if (Notification.permission === 'granted') {
		return true;
	}

	if (Notification.permission === 'denied') {
		console.warn('알림 권한이 거부되었습니다');
		return false;
	}

	try {
		const permission = await Notification.requestPermission();
		const granted = permission === 'granted';

		if (granted) {
			console.log('브라우저 알림 권한이 승인되었습니다');
		} else {
			console.warn('브라우저 알림 권한이 거부되었습니다');
		}

		return granted;
	} catch (error) {
		console.error('알림 권한 요청 실패:', error);
		return false;
	}
}

/**
 * 향상된 브라우저 알림 권한 요청
 * 요구사항 1.5, 6.1, 6.2, 6.3: 권한 관리 및 대체 알림 방식
 *
 * @returns Promise<NotificationPermission> 권한 상태
 */
export async function requestNotificationPermissionEnhanced(): Promise<NotificationPermission> {
	// 브라우저가 알림을 지원하지 않는 경우
	if (typeof window === 'undefined' || !('Notification' in window) || !window.Notification) {
		console.warn('이 브라우저는 알림을 지원하지 않습니다.');
		alternativeNotificationModeStore.set(true);
		return 'denied';
	}

	// 이미 권한이 부여된 경우
	if (window.Notification.permission === 'granted') {
		notificationPermissionStore.set('granted');
		alternativeNotificationModeStore.set(false);
		return 'granted';
	}

	// 권한이 거부된 경우 (재요청 불가)
	if (window.Notification.permission === 'denied') {
		console.warn('브라우저 알림 권한이 거부되었습니다.');
		alternativeNotificationModeStore.set(true);
		notificationPermissionStore.set('denied');
		return 'denied';
	}

	try {
		permissionRequestInProgressStore.set(true);

		// 권한 요청
		const permission = await window.Notification.requestPermission();

		notificationPermissionStore.set(permission);

		if (permission === 'granted') {
			console.log('브라우저 알림 권한이 부여되었습니다.');
			alternativeNotificationModeStore.set(false);
		} else {
			console.warn('브라우저 알림 권한이 거부되었습니다.');
			alternativeNotificationModeStore.set(true);
		}

		return permission;
	} catch (error) {
		console.error('알림 권한 요청 중 오류 발생:', error);
		alternativeNotificationModeStore.set(true);
		return 'denied';
	} finally {
		permissionRequestInProgressStore.set(false);
	}
}

/**
 * 브라우저 알림 권한 상태 확인
 * 요구사항 1.5: 알림 권한 관리 및 사용자 상호작용
 *
 * @returns object 권한 상태 정보
 */
export function getNotificationPermissionStatus(): {
	supported: boolean;
	permission: NotificationPermission | null;
	canRequest: boolean;
	message: string;
} {
	if (!('Notification' in window)) {
		return {
			supported: false,
			permission: null,
			canRequest: false,
			message: '이 브라우저는 알림을 지원하지 않습니다'
		};
	}

	const permission = Notification.permission;

	switch (permission) {
		case 'granted':
			return {
				supported: true,
				permission,
				canRequest: false,
				message: '알림 권한이 승인되었습니다'
			};
		case 'denied':
			return {
				supported: true,
				permission,
				canRequest: false,
				message: '알림 권한이 거부되었습니다. 브라우저 설정에서 수동으로 허용해주세요'
			};
		case 'default':
			return {
				supported: true,
				permission,
				canRequest: true,
				message: '알림 권한을 요청할 수 있습니다'
			};
		default:
			return {
				supported: true,
				permission,
				canRequest: false,
				message: '알 수 없는 권한 상태입니다'
			};
	}
}

/**
 * 사용자 친화적인 권한 요청
 * 요구사항 1.5: 사용자 상호작용을 통한 권한 관리
 *
 * @param showExplanation 설명 메시지 표시 여부
 * @returns Promise<boolean>
 */
export async function requestNotificationPermissionWithExplanation(
	showExplanation: boolean = true
): Promise<boolean> {
	const status = getNotificationPermissionStatus();

	if (!status.supported) {
		if (showExplanation) {
			alert('이 브라우저는 알림 기능을 지원하지 않습니다.');
		}
		return false;
	}

	if (status.permission === 'granted') {
		return true;
	}

	if (status.permission === 'denied') {
		if (showExplanation) {
			alert(
				'알림 권한이 거부되어 있습니다.\n' +
					'중요한 알림을 받으려면 브라우저 설정에서 알림을 허용해주세요.\n\n' +
					'Chrome: 주소창 왼쪽 자물쇠 아이콘 → 알림 허용\n' +
					'Firefox: 주소창 왼쪽 방패 아이콘 → 알림 허용'
			);
		}
		return false;
	}

	if (showExplanation) {
		const userConfirmed = confirm(
			'중요한 업무 알림을 실시간으로 받기 위해 브라우저 알림 권한이 필요합니다.\n\n' +
				'권한을 허용하시겠습니까?'
		);

		if (!userConfirmed) {
			console.log('사용자가 알림 권한 요청을 취소했습니다');
			return false;
		}
	}

	return await requestNotificationPermission();
}

/**
 * 현재 알림 권한 상태 확인 (향상된 버전)
 * 요구사항 1.5: 권한 상태 확인 및 사용자 안내
 *
 * @returns NotificationPermission 현재 권한 상태
 */
export function getNotificationPermissionStatusEnhanced(): NotificationPermission {
	if (typeof window === 'undefined' || !('Notification' in window) || !window.Notification) {
		return 'denied';
	}

	const permission = window.Notification.permission;
	notificationPermissionStore.set(permission);

	// 대체 알림 모드 설정
	alternativeNotificationModeStore.set(permission !== 'granted');

	return permission;
}

/**
 * 브라우저 알림 사용 가능 여부 확인
 * 요구사항 1.5: 브라우저 알림 권한 관리
 *
 * @returns boolean 브라우저 알림 사용 가능 여부
 */
export function isBrowserNotificationAvailable(): boolean {
	return (
		typeof window !== 'undefined' &&
		'Notification' in window &&
		window.Notification &&
		window.Notification.permission === 'granted'
	);
}

/**
 * 권한 요청이 가능한지 확인
 * 요구사항 1.5: 권한 요청 가능 여부 확인
 *
 * @returns boolean 권한 요청 가능 여부
 */
export function canRequestPermission(): boolean {
	if (typeof window === 'undefined' || !('Notification' in window) || !window.Notification) {
		return false;
	}

	// 'default' 상태일 때만 권한 요청 가능
	return window.Notification.permission === 'default';
}

/**
 * 사용자에게 권한 안내 메시지 생성
 * 요구사항 6.1, 6.2, 6.3: 사용자 안내 및 대체 알림 방식
 *
 * @returns PermissionGuideMessage 권한 안내 메시지 객체
 */
export function getPermissionGuideMessage(): PermissionGuideMessage {
	// 브라우저 지원 여부 먼저 확인
	if (typeof window === 'undefined' || !('Notification' in window) || !window.Notification) {
		return {
			type: 'error',
			title: '알림 지원 안됨',
			message: '이 브라우저는 알림을 지원하지 않습니다.',
			actionRequired: false,
			actionText: null
		};
	}

	const permission = getNotificationPermissionStatusEnhanced();

	switch (permission) {
		case 'granted':
			return {
				type: 'success',
				title: '알림 권한 허용됨',
				message: '브라우저 알림을 받을 수 있습니다.',
				actionRequired: false,
				actionText: null
			};

		case 'denied':
			return {
				type: 'warning',
				title: '알림 권한 거부됨',
				message: '브라우저 설정에서 알림 권한을 허용해주세요. 현재는 화면 내 알림만 표시됩니다.',
				actionRequired: true,
				actionText: '브라우저 설정 열기'
			};

		case 'default':
			return {
				type: 'info',
				title: '알림 권한 필요',
				message: '중요한 알림을 놓치지 않으려면 브라우저 알림을 허용해주세요.',
				actionRequired: true,
				actionText: '알림 허용하기'
			};

		default:
			return {
				type: 'error',
				title: '알림 지원 안됨',
				message: '이 브라우저는 알림을 지원하지 않습니다.',
				actionRequired: false,
				actionText: null
			};
	}
}

/**
 * 브라우저 설정 페이지 열기 (권한 거부 시 사용자 안내)
 * 요구사항 6.2: 권한 거부 시 대체 알림 방식 제공
 */
export function openBrowserNotificationSettings(): void {
	const userAgent = navigator.userAgent.toLowerCase();

	if (userAgent.includes('chrome')) {
		// Chrome 설정 페이지
		window.open('chrome://settings/content/notifications', '_blank');
	} else if (userAgent.includes('firefox')) {
		// Firefox 설정 안내
		alert(
			'Firefox에서 알림 설정을 변경하려면:\n1. 주소창 왼쪽의 자물쇠 아이콘을 클릭하세요\n2. "알림" 설정을 "허용"으로 변경하세요'
		);
	} else if (userAgent.includes('safari')) {
		// Safari 설정 안내
		alert(
			'Safari에서 알림 설정을 변경하려면:\n1. Safari > 환경설정 > 웹사이트 > 알림으로 이동하세요\n2. 이 사이트의 알림을 "허용"으로 설정하세요'
		);
	} else {
		// 일반적인 안내
		alert('브라우저 설정에서 이 사이트의 알림을 허용해주세요.');
	}
}

/**
 * 대체 알림 방식 활성화
 * 요구사항 6.3: 대체 알림 방식 제공
 *
 * @param enable 대체 알림 모드 활성화 여부
 */
export function setAlternativeNotificationMode(enable: boolean): void {
	alternativeNotificationModeStore.set(enable);

	if (enable) {
		console.log('대체 알림 모드가 활성화되었습니다. 화면 내 토스트 알림을 사용합니다.');
	} else {
		console.log('브라우저 알림 모드가 활성화되었습니다.');
	}
}

/**
 * 권한 상태 모니터링 시작
 * 요구사항 1.5: 권한 상태 확인 및 사용자 안내
 *
 * 페이지가 포커스될 때마다 권한 상태를 확인합니다.
 */
export function startPermissionMonitoring(): () => void {
	// 초기 상태 확인
	getNotificationPermissionStatusEnhanced();

	// 페이지 포커스 시 권한 상태 재확인
	const handleVisibilityChange = () => {
		if (!document.hidden) {
			getNotificationPermissionStatusEnhanced();
		}
	};

	document.addEventListener('visibilitychange', handleVisibilityChange);

	// 정리 함수 반환
	return () => {
		document.removeEventListener('visibilitychange', handleVisibilityChange);
	};
}

/**
 * 알림 테스트 (권한 확인용)
 * 요구사항 1.5: 브라우저 알림 권한 관리
 *
 * @returns Promise<boolean> 테스트 알림 표시 성공 여부
 */
export async function testNotification(): Promise<boolean> {
	if (!isBrowserNotificationAvailable()) {
		console.warn('브라우저 알림을 사용할 수 없습니다.');
		return false;
	}

	try {
		const notification = new Notification('알림 테스트', {
			body: '브라우저 알림이 정상적으로 작동합니다.',
			icon: '/icons/notification.png',
			tag: 'test-notification'
		});

		// 3초 후 자동 닫기
		setTimeout(() => {
			notification.close();
		}, 3000);

		return true;
	} catch (error) {
		console.error('테스트 알림 표시 실패:', error);
		return false;
	}
}

// 타입 정의
export interface PermissionGuideMessage {
	type: 'success' | 'warning' | 'info' | 'error';
	title: string;
	message: string;
	actionRequired: boolean;
	actionText: string | null;
}
