import type { NotificationData } from '$lib/types/notificationTypes';
import type { NotificationSettings } from '$lib/services/notificationSettings';
import { initializeIndexedDB, putData } from '$lib/services/indexedDBManager';
import { isOfflineMode } from '$lib/services/sseConnection';
import { updateNotificationStore } from '$lib/services/storeUpdaters';
import { getNotificationSettings } from '$lib/services/notificationSettings';
import {
	displayNotification,
	playNotificationSound,
	showBrowserNotification,
	showUrgentNotificationEffects,
	vibrateDevice
} from './display';
import {
	shouldShowBrowserNotification,
	shouldShowNotification,
	shouldPlaySound,
	shouldVibrate
} from './rules';

/**
 * 알림 메시지 처리
 *
 * @param db IDBDatabase 객체
 * @param notificationData 알림 데이터
 * @returns Promise<void>
 */
export async function handleNotificationMessage(
	db: IDBDatabase,
	notificationData: NotificationData
): Promise<void> {
	try {
		// 오프라인 모드인 경우 오프라인 스토어에 저장
		if (isOfflineMode()) {
			await storeOfflineNotification(db, notificationData);
			console.log('오프라인 모드: 알림을 오프라인 스토어에 저장했습니다');
			return;
		}

		// 일반 알림 처리
		const notification = {
			...notificationData,
			read: false,
			received_at: new Date().toISOString()
		};

		// IndexedDB에 저장
		await putData(db, 'notifications', notification);

		// 우선순위별 처리
		await processNotificationByPriority(notification);

		// 스토어 업데이트
		await updateNotificationStore(db);

		console.log('알림 처리 완료:', notification.id);
	} catch (error) {
		console.error('알림 처리 실패:', error);
	}
}

/**
 * 오프라인 알림 저장
 *
 * 요구사항 1.4: 오프라인 상태에서 알림을 저장합니다.
 *
 * @param db IDBDatabase 객체
 * @param notificationData 알림 데이터
 * @returns Promise<void>
 */
async function storeOfflineNotification(
	db: IDBDatabase,
	notificationData: NotificationData
): Promise<void> {
	try {
		const offlineNotification = {
			...notificationData,
			stored_at: new Date().toISOString(),
			offline_stored: true
		};

		await putData(db, 'offline_notifications', offlineNotification);
		console.log('오프라인 알림 저장 완료:', offlineNotification.id);
	} catch (error) {
		console.error('오프라인 알림 저장 실패:', error);
	}
}

/**
 * 우선순위별 알림 처리
 *
 * 요구사항 7.1-7.5: 우선순위별 강제 전송 시스템 구현
 * - 낮음: 개인 설정 완전 준수
 * - 보통: 기본 알림 설정 따르되 완전 비활성화 무시
 * - 높음: 개인 설정 무시하고 모든 대상자에게 표시
 * - 긴급: 브라우저 알림, 사운드, 진동 강제 활성화
 *
 * @param notification 알림 객체
 * @returns Promise<void>
 */
export async function processNotificationByPriority(notification: NotificationData): Promise<void> {
	const { priority } = notification;

	// 개인 설정 확인
	const db = await initializeIndexedDB();
	const settings = await getNotificationSettings(db);

	console.log(`알림 처리 시작 - 우선순위: ${priority}, ID: ${notification.id}`);

	// 우선순위별 처리 로직
	switch (priority) {
		case 'low':
			// 요구사항 7.1: 낮음 우선순위는 개인 설정을 완전히 따름
			await processLowPriorityNotification(notification, settings);
			break;

		case 'normal':
			// 요구사항 7.2: 보통 우선순위는 기본 설정을 따르되 완전 비활성화는 무시
			await processNormalPriorityNotification(notification, settings);
			break;

		case 'high':
			// 요구사항 7.3: 높음 우선순위는 개인 설정 무시하고 모든 대상자에게 표시
			await processHighPriorityNotification(notification, settings);
			break;

		case 'urgent':
			// 요구사항 7.4, 7.5: 긴급 우선순위는 모든 알림 방식 강제 활성화
			await processUrgentPriorityNotification(notification, settings);
			break;

		default:
			console.warn(`알 수 없는 우선순위: ${priority}, 보통 우선순위로 처리`);
			await processNormalPriorityNotification(notification, settings);
	}

	console.log(`알림 처리 완료 - 우선순위: ${priority}, ID: ${notification.id}`);
}

/**
 * 낮음 우선순위 알림 처리
 * 요구사항 7.1: 개인 설정을 완전히 따름
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns Promise<void>
 */
async function processLowPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): Promise<void> {
	// 개인 설정 완전 준수 - 모든 알림이 비활성화된 경우 아무것도 하지 않음
	if (!settings.enableBrowserNotifications && !settings.enableSounds && !settings.enableVibration) {
		console.log(`낮음 우선순위 알림 숨김 - 모든 알림이 비활성화됨: ${notification.id}`);
		return;
	}

	// 기본 설정 확인 (타입, 근무시간 등)
	if (!shouldShowNotification(notification, settings)) {
		console.log(`낮음 우선순위 알림 숨김 - 개인 설정에 따라: ${notification.id}`);
		return;
	}

	// 토스트 알림 표시 (개인 설정에 따라 표시 가능한 경우에만)
	displayNotification(notification);

	// 브라우저 알림 (새 함수 사용)
	if (shouldShowBrowserNotification(notification, settings)) {
		await showBrowserNotification(notification);
	}

	// 사운드 재생 (새 함수 사용)
	if (shouldPlaySound(notification, settings)) {
		playNotificationSound('low', settings.soundFile);
	}

	// 진동 (새 함수 사용)
	if (shouldVibrate(notification, settings)) {
		vibrateDevice('low');
	}
}

/**
 * 보통 우선순위 알림 처리
 * 요구사항 7.2: 기본 설정을 따르되 완전 비활성화는 무시
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns Promise<void>
 */
async function processNormalPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): Promise<void> {
	// 보통 우선순위는 완전 비활성화를 무시하고 최소한 토스트는 표시
	displayNotification(notification);

	// 브라우저 알림 (새 함수 사용)
	if (shouldShowBrowserNotification(notification, settings)) {
		await showBrowserNotification(notification);
	}

	// 사운드 재생 (새 함수 사용)
	if (shouldPlaySound(notification, settings)) {
		playNotificationSound('normal', settings.soundFile);
	}

	// 진동 (새 함수 사용)
	if (shouldVibrate(notification, settings)) {
		vibrateDevice('normal');
	}
}

/**
 * 높음 우선순위 알림 처리
 * 요구사항 7.3: 개인 설정 무시하고 모든 대상자에게 표시
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns Promise<void>
 */
async function processHighPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): Promise<void> {
	console.log(`높음 우선순위 알림 - 개인 설정 무시하고 강제 표시: ${notification.id}`);

	// 토스트 알림 강제 표시
	displayNotification(notification);

	// 브라우저 알림 강제 표시
	await showBrowserNotification(notification);

	// 사운드 재생 (새 함수 사용 - 높음 우선순위는 강제 재생)
	if (shouldPlaySound(notification, settings)) {
		playNotificationSound('high', settings.soundFile);
	}

	// 진동 (새 함수 사용 - 높음 우선순위 처리)
	if (shouldVibrate(notification, settings)) {
		vibrateDevice('high');
	}
}

/**
 * 긴급 우선순위 알림 처리
 * 요구사항 7.4, 7.5: 브라우저 알림, 사운드, 진동 강제 활성화
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns Promise<void>
 */
async function processUrgentPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): Promise<void> {
	console.log(`긴급 우선순위 알림 - 모든 알림 방식 강제 활성화: ${notification.id}`);

	// 토스트 알림 강제 표시
	displayNotification(notification);

	// 브라우저 알림 강제 표시 (requireInteraction: true)
	await showBrowserNotification(notification);

	// 사운드 강제 재생 (새 함수 사용 - 긴급 우선순위는 항상 재생)
	if (shouldPlaySound(notification, settings)) {
		playNotificationSound('urgent', settings.soundFile);
	}

	// 진동 강제 실행 (새 함수 사용 - 긴급 우선순위는 항상 실행)
	if (shouldVibrate(notification, settings)) {
		vibrateDevice('urgent');
	}

	// 긴급 알림은 추가적인 주의 환기 효과
	await showUrgentNotificationEffects(notification);
}
/**
 * 통합 알림 처리 함수 (권한 관리 포함)
 * 요구사항 1.5, 6.1, 6.2, 6.3: 권한 관리 및 대체 알림 방식
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 */
export function processNotificationWithPermission(notification: any, settings: any = {}): void {
	import('./permissions').then(({ isBrowserNotificationAvailable }) => {
		const { priority } = notification;

		// 브라우저 알림 시도
		if (shouldShowBrowserNotification(notification, settings)) {
			if (isBrowserNotificationAvailable()) {
				showBrowserNotification(notification);
			} else {
				// 권한이 없는 경우 대체 알림 표시
				console.warn('브라우저 알림 권한이 없습니다. 대체 알림을 사용합니다.');
				showAlternativeNotification(notification);
			}
		} else {
			// 권한이 없거나 설정에서 비활성화된 경우 대체 알림 사용
			showAlternativeNotification(notification);
		}

		// 사운드 재생
		if (shouldPlaySound(notification, settings)) {
			playNotificationSound(
				priority as 'low' | 'normal' | 'high' | 'urgent',
				settings.customSoundFile
			);
		}

		// 진동
		if (shouldVibrate(notification, settings)) {
			vibrateDevice(priority as 'low' | 'normal' | 'high' | 'urgent');
		}
	});
}

/**
 * 대체 알림 표시 (권한이 없을 때)
 * 요구사항 6.3: 대체 알림 방식 제공
 *
 * @param notification 알림 객체
 */
function showAlternativeNotification(notification: any): void {
	// 토스트 알림 표시 (기존 토스트 시스템 사용)
	displayNotification(notification);

	// 긴급한 경우에만 alert 사용
	if (notification.priority === 'urgent') {
		alert(`긴급 알림: ${notification.title}\n${notification.message}`);
	}
}
