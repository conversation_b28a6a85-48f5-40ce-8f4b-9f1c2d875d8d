import type { NotificationData } from '$lib/types/notificationTypes';
import type { NotificationSettings } from '$lib/services/notificationSettings';
import { isWorkingHours } from '$lib/services/notificationSettings';

/**
 * 알림 표시 여부 확인 함수들
 * 작업 5.3: 개인 설정 및 근무 시간을 고려한 알림 표시 여부 결정
 * 요구사항 6.1, 6.2, 6.3, 6.4, 6.5, 6.6, 6.7
 */

/**
 * 알림 표시 여부 확인 (메인 함수)
 *
 * 요구사항 6.1, 6.2, 6.3: 개인 설정에 따른 알림 활성화/비활성화
 * 요구사항 6.5: 근무 시간 외 알림 제어
 * 요구사항 7.1-7.5: 우선순위별 강제 전송 규칙
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean 알림 표시 여부
 */
export function shouldShowNotification(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	const { priority, type } = notification;

	// 우선순위별 강제 전송 규칙 적용
	switch (priority) {
		case 'urgent':
		case 'high':
			// 요구사항 7.3, 7.4: 높음/긴급 우선순위는 개인 설정 무시하고 항상 표시
			console.log(`높음/긴급 우선순위 알림 - 강제 표시: ${notification.id}`);
			return true;

		case 'normal':
			// 요구사항 7.2: 보통 우선순위는 기본 설정을 따르되 완전 비활성화는 무시
			return shouldShowNormalPriorityNotification(notification, settings);

		case 'low':
		default:
			// 요구사항 7.1: 낮음 우선순위는 개인 설정을 완전히 따름
			return shouldShowLowPriorityNotification(notification, settings);
	}
}

/**
 * 낮음 우선순위 알림 표시 여부 확인
 * 요구사항 7.1: 개인 설정을 완전히 따름
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean
 */
function shouldShowLowPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	// 모든 알림이 비활성화된 경우
	if (!settings.enableBrowserNotifications && !settings.enableSounds && !settings.enableVibration) {
		console.log(`낮음 우선순위 알림 숨김 - 모든 알림 비활성화: ${notification.id}`);
		return false;
	}

	// 알림 타입이 비활성화된 경우
	if (settings.disabledTypes.includes(notification.type)) {
		console.log(
			`낮음 우선순위 알림 숨김 - 타입 비활성화 (${notification.type}): ${notification.id}`
		);
		return false;
	}

	// 근무 시간 확인
	if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
		console.log(`낮음 우선순위 알림 숨김 - 근무 시간 외: ${notification.id}`);
		return false;
	}

	return true;
}

/**
 * 보통 우선순위 알림 표시 여부 확인
 * 요구사항 7.2: 기본 설정을 따르되 완전 비활성화는 무시
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean
 */
function shouldShowNormalPriorityNotification(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	// 알림 타입이 비활성화된 경우에도 보통 우선순위는 표시
	// (완전 비활성화 무시 정책)

	// 근무 시간 확인 (보통 우선순위도 근무 시간은 고려)
	if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
		// 근무 시간 외에도 보통 우선순위는 조용히 표시
		console.log(`보통 우선순위 알림 - 근무 시간 외 조용히 표시: ${notification.id}`);
		return true; // 표시하되 사운드/진동은 제한될 수 있음
	}

	return true;
}

/**
 * 브라우저 알림 표시 여부 확인
 *
 * 요구사항 6.1: 브라우저 알림 개인 설정 확인
 * 요구사항 7.3, 7.4: 높음/긴급 우선순위 강제 표시
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean 브라우저 알림 표시 여부
 */
export function shouldShowBrowserNotification(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	const { priority } = notification;

	// 브라우저 알림 권한 확인
	if (!('Notification' in window) || Notification.permission !== 'granted') {
		console.log(`브라우저 알림 불가 - 권한 없음: ${notification.id}`);
		return false;
	}

	// 우선순위별 처리
	switch (priority) {
		case 'urgent':
		case 'high':
			// 요구사항 7.3, 7.4: 높음/긴급 우선순위는 강제 표시
			console.log(`높음/긴급 우선순위 - 브라우저 알림 강제 표시: ${notification.id}`);
			return true;

		case 'normal':
			// 요구사항 7.2: 보통 우선순위는 완전 비활성화 무시
			return settings.enableBrowserNotifications || shouldShowNotification(notification, settings);

		case 'low':
		default:
			// 요구사항 7.1: 낮음 우선순위는 개인 설정 완전 준수
			if (!settings.enableBrowserNotifications) {
				console.log(`낮음 우선순위 - 브라우저 알림 비활성화: ${notification.id}`);
				return false;
			}

			return shouldShowNotification(notification, settings);
	}
}

/**
 * 사운드 재생 여부 확인
 *
 * 요구사항 6.2: 사운드 알림 개인 설정 확인
 * 요구사항 6.5: 근무 시간 외 사운드 제어
 * 요구사항 6.6: 사용자 지정 사운드 적용 (낮음/보통 우선순위만)
 * 요구사항 7.4: 긴급 우선순위 강제 사운드
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean 사운드 재생 여부
 */
export function shouldPlaySound(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	const { priority } = notification;

	// 우선순위별 처리
	switch (priority) {
		case 'urgent':
			// 요구사항 7.4: 긴급 우선순위는 사운드 강제 재생
			console.log(`긴급 우선순위 - 사운드 강제 재생: ${notification.id}`);
			return true;

		case 'high':
			// 요구사항 7.3: 높음 우선순위는 개인 설정 무시하고 재생
			console.log(`높음 우선순위 - 사운드 강제 재생: ${notification.id}`);
			return true;

		case 'normal':
			// 보통 우선순위는 근무 시간과 개인 설정 고려
			if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
				// 근무 시간 외에는 조용히
				console.log(`보통 우선순위 - 근무 시간 외 사운드 비활성화: ${notification.id}`);
				return false;
			}
			return settings.enableSounds && shouldShowNotification(notification, settings);

		case 'low':
		default:
			// 요구사항 7.1: 낮음 우선순위는 개인 설정 완전 준수
			if (!settings.enableSounds) {
				console.log(`낮음 우선순위 - 사운드 비활성화: ${notification.id}`);
				return false;
			}

			// 근무 시간 확인
			if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
				console.log(`낮음 우선순위 - 근무 시간 외 사운드 비활성화: ${notification.id}`);
				return false;
			}

			return shouldShowNotification(notification, settings);
	}
}

/**
 * 진동 실행 여부 확인
 *
 * 요구사항 6.3: 진동 알림 개인 설정 확인
 * 요구사항 6.5: 근무 시간 외 진동 제어
 * 요구사항 7.4: 긴급 우선순위 강제 진동
 *
 * @param notification 알림 객체
 * @param settings 알림 설정
 * @returns boolean 진동 실행 여부
 */
export function shouldVibrate(
	notification: NotificationData,
	settings: NotificationSettings
): boolean {
	const { priority } = notification;

	// 디바이스 진동 지원 확인
	if (!('vibrate' in navigator)) {
		console.log(`진동 불가 - 디바이스 미지원: ${notification.id}`);
		return false;
	}

	// 우선순위별 처리
	switch (priority) {
		case 'urgent':
			// 요구사항 7.4: 긴급 우선순위는 진동 강제 실행
			console.log(`긴급 우선순위 - 진동 강제 실행: ${notification.id}`);
			return true;

		case 'high':
			// 높음 우선순위는 개인 설정 고려하되 우선 적용
			if (settings.enableVibration) {
				console.log(`높음 우선순위 - 진동 실행: ${notification.id}`);
				return true;
			}
			// 개인 설정이 비활성화되어 있어도 높음 우선순위는 약한 진동
			console.log(`높음 우선순위 - 약한 진동 실행 (설정 무시): ${notification.id}`);
			return true;

		case 'normal':
			// 보통 우선순위는 근무 시간과 개인 설정 고려
			if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
				// 근무 시간 외에는 진동 비활성화
				console.log(`보통 우선순위 - 근무 시간 외 진동 비활성화: ${notification.id}`);
				return false;
			}
			return settings.enableVibration && shouldShowNotification(notification, settings);

		case 'low':
			// 요구사항 7.1: 낮음 우선순위는 개인 설정 완전 준수
			if (!settings.enableVibration) {
				console.log(`낮음 우선순위 - 진동 비활성화: ${notification.id}`);
				return false;
			}

			// 근무 시간 확인
			if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
				console.log(`낮음 우선순위 - 근무 시간 외 진동 비활성화: ${notification.id}`);
				return false;
			}

			return shouldShowNotification(notification, settings);
		default:
			// 기본값 처리 (예: low와 동일하게 처리)
			if (!settings.enableVibration) {
				console.log(`알 수 없는 우선순위 - 진동 비활성화: ${notification.id}`);
				return false;
			}

			// 근무 시간 확인
			if (settings.workingHours.enabled && !isWorkingHours(settings.workingHours)) {
				console.log(`알 수 없는 우선순위 - 근무 시간 외 진동 비활성화: ${notification.id}`);
				return false;
			}

			return shouldShowNotification(notification, settings);
	}
}

/**
 * 알림 타입별 표시 여부 확인
 *
 * 요구사항 6.4: 알림 타입별 활성화/비활성화 처리
 *
 * @param notificationType 알림 타입
 * @param settings 알림 설정
 * @returns boolean 해당 타입 알림 표시 여부
 */
export function shouldShowNotificationType(
	notificationType: string,
	settings: NotificationSettings
): boolean {
	// 비활성화된 타입 목록에 포함되어 있는지 확인
	const isDisabled = settings.disabledTypes.includes(notificationType);

	if (isDisabled) {
		console.log(`알림 타입 비활성화: ${notificationType}`);
		return false;
	}

	return true;
}

/**
 * 근무 시간 기반 알림 제어 확인
 *
 * 요구사항 6.5: 근무 시간 외 알림 제어
 *
 * @param settings 알림 설정
 * @param priority 알림 우선순위 (선택사항)
 * @returns object 근무 시간 기반 알림 제어 정보
 */
export function getWorkingHoursNotificationControl(
	settings: NotificationSettings,
	priority?: string
): {
	isWorkingHours: boolean;
	allowNotifications: boolean;
	allowSounds: boolean;
	allowVibration: boolean;
	quietMode: boolean;
} {
	const isWorking = isWorkingHours(settings.workingHours);

	// 근무 시간 설정이 비활성화된 경우 모든 알림 허용
	if (!settings.workingHours.enabled) {
		return {
			isWorkingHours: true, // 설정이 비활성화되면 항상 근무 시간으로 간주
			allowNotifications: true,
			allowSounds: true,
			allowVibration: true,
			quietMode: false
		};
	}

	// 긴급/높음 우선순위는 근무 시간과 관계없이 모든 알림 허용
	if (priority && ['urgent', 'high'].includes(priority)) {
		return {
			isWorkingHours: isWorking,
			allowNotifications: true,
			allowSounds: true,
			allowVibration: true,
			quietMode: false
		};
	}

	// 근무 시간 내
	if (isWorking) {
		return {
			isWorkingHours: true,
			allowNotifications: true,
			allowSounds: true,
			allowVibration: true,
			quietMode: false
		};
	}

	// 근무 시간 외
	return {
		isWorkingHours: false,
		allowNotifications: true, // 알림은 표시하되
		allowSounds: false, // 사운드는 비활성화
		allowVibration: false, // 진동도 비활성화
		quietMode: true // 조용한 모드
	};
}
