/**
 * 알림 아이콘 조회
 * 요구사항 2.1, 2.2, 2.3, 2.4, 2.5: 우선순위별 아이콘 조회
 *
 * @param priority 우선순위
 * @param type 알림 타입 (선택사항)
 * @returns string 아이콘 경로
 */
export function getNotificationIcon(priority: string, type?: string): string {
	// 우선순위별 기본 아이콘
	const priorityIcons: Record<string, string> = {
		low: '/icons/notification-low.png',
		normal: '/icons/notification.png',
		high: '/icons/notification-high.png',
		urgent: '/icons/notification-urgent.png'
	};

	// 알림 타입별 특수 아이콘 (우선순위보다 우선)
	const typeIcons: Record<string, string> = {
		system: '/icons/notification-system.png',
		maintenance: '/icons/notification-maintenance.png',
		security: '/icons/notification-security.png',
		update: '/icons/notification-update.png',
		reminder: '/icons/notification-reminder.png',
		message: '/icons/notification-message.png',
		task: '/icons/notification-task.png',
		warning: '/icons/notification-warning.png',
		error: '/icons/notification-error.png',
		success: '/icons/notification-success.png'
	};

	// 타입별 아이콘이 있으면 우선 사용
	if (type && typeIcons[type]) {
		console.log(`타입별 아이콘 사용: ${type} -> ${typeIcons[type]}`);
		return typeIcons[type];
	}

	// 우선순위별 아이콘 사용
	const icon = priorityIcons[priority] || priorityIcons.normal;
	console.log(`우선순위별 아이콘 사용: ${priority} -> ${icon}`);

	return icon;
}

/**
 * 알림 아이콘 사전 로드
 * 성능 최적화를 위한 아이콘 파일 미리 로드
 *
 * @returns Promise<void>
 */
export async function preloadNotificationIcons(): Promise<void> {
	const iconPaths = [
		// 우선순위별 아이콘
		'/icons/notification-low.png',
		'/icons/notification.png',
		'/icons/notification-high.png',
		'/icons/notification-urgent.png',
		// 타입별 아이콘
		'/icons/notification-system.png',
		'/icons/notification-maintenance.png',
		'/icons/notification-security.png',
		'/icons/notification-update.png',
		'/icons/notification-reminder.png',
		'/icons/notification-message.png',
		'/icons/notification-task.png',
		'/icons/notification-warning.png',
		'/icons/notification-error.png',
		'/icons/notification-success.png'
	];

	const loadPromises = iconPaths.map((iconPath) => {
		return new Promise<void>((resolve) => {
			const img = new Image();
			img.onload = () => {
				console.log(`아이콘 사전 로드 완료: ${iconPath}`);
				resolve();
			};
			img.onerror = () => {
				console.warn(`아이콘 사전 로드 실패: ${iconPath}`);
				resolve(); // 실패해도 계속 진행
			};
			img.src = iconPath;
		});
	});

	try {
		await Promise.all(loadPromises);
		console.log('알림 아이콘 사전 로드 완료');
	} catch (error) {
		console.error('아이콘 사전 로드 중 오류:', error);
	}
}

/**
 * 아이콘 존재 여부 확인
 *
 * @param iconPath 아이콘 경로
 * @returns Promise<boolean> 아이콘 존재 여부
 */
export async function checkIconExists(iconPath: string): Promise<boolean> {
	return new Promise((resolve) => {
		const img = new Image();
		img.onload = () => resolve(true);
		img.onerror = () => resolve(false);
		img.src = iconPath;
	});
}

/**
 * 대체 아이콘 조회
 * 기본 아이콘이 없을 경우 대체 아이콘 반환
 *
 * @param priority 우선순위
 * @param type 알림 타입
 * @returns Promise<string> 사용 가능한 아이콘 경로
 */
export async function getFallbackNotificationIcon(
	priority: string,
	type?: string
): Promise<string> {
	// 1차: 타입별 아이콘 시도
	if (type) {
		const typeIcon = getNotificationIcon(priority, type);
		if (await checkIconExists(typeIcon)) {
			return typeIcon;
		}
	}

	// 2차: 우선순위별 아이콘 시도
	const priorityIcon = getNotificationIcon(priority);
	if (await checkIconExists(priorityIcon)) {
		return priorityIcon;
	}

	// 3차: 기본 아이콘 시도
	const defaultIcon = '/icons/notification.png';
	if (await checkIconExists(defaultIcon)) {
		return defaultIcon;
	}

	// 4차: 브라우저 기본 아이콘 (data URL)
	return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0VjVDMTcuMyA1LjcgMjAgOC4xIDIwIDExVjE3TDIyIDE5VjIwSDJWMTlMNCA3VjExQzQgOC4xIDYuNyA1LjcgMTAgNVY0QzEwIDIuOSAxMC45IDIgMTIgMlpNMTIgMjNDMTMuMSAyMyAxNCAyMi4xIDE0IDIxSDEwQzEwIDIyLjEgMTAuOSAyMyAxMiAyM1oiIGZpbGw9IiM2MzYzNjMiLz4KPC9zdmc+';
}

/**
 * 우선순위별 표시 지속시간 조회
 *
 * @param priority 우선순위
 * @returns number 지속시간 (밀리초)
 */
export function getDisplayDuration(priority: string): number {
	const durations: Record<string, number> = {
		low: 3000, // 3초
		normal: 5000, // 5초
		high: 8000, // 8초
		urgent: 0 // 수동 닫기
	};

	return durations[priority] !== undefined ? durations[priority] : durations.normal;
}

/**
 * 우선순위별 알림 스타일 조회
 *
 * @param priority 우선순위
 * @returns object 스타일 설정
 */
export function getNotificationStyle(priority: string): object {
	const styles: Record<string, object> = {
		low: {
			backgroundColor: '#f3f4f6',
			borderColor: '#d1d5db',
			textColor: '#374151',
			iconColor: '#6b7280'
		},
		normal: {
			backgroundColor: '#dbeafe',
			borderColor: '#3b82f6',
			textColor: '#1e40af',
			iconColor: '#3b82f6'
		},
		high: {
			backgroundColor: '#fef3c7',
			borderColor: '#f59e0b',
			textColor: '#92400e',
			iconColor: '#f59e0b'
		},
		urgent: {
			backgroundColor: '#fee2e2',
			borderColor: '#ef4444',
			textColor: '#991b1b',
			iconColor: '#ef4444',
			animation: 'pulse'
		}
	};

	return styles[priority] || styles.normal;
}

/**
 * 진동 지원 여부 확인
 *
 * @returns boolean 진동 지원 여부
 */
export function isVibrationSupported(): boolean {
	return 'vibrate' in navigator && typeof navigator.vibrate === 'function';
}