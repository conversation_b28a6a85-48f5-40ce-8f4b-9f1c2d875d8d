/**
 * 알림 시스템 초기화 유틸리티
 *
 * 애플리케이션 시작 시 알림 권한을 확인하고 필요한 설정을 초기화합니다.
 */

import {
	requestNotificationPermissionEnhanced as requestNotificationPermission,
	getNotificationPermissionStatusEnhanced as getNotificationPermissionStatus,
	startPermissionMonitoring,
	setAlternativeNotificationMode
} from '$lib/services/notifications/permissions';

/**
 * 알림 시스템 초기화 옵션
 */
export interface NotificationInitOptions {
	/** 자동으로 권한 요청 여부 */
	autoRequestPermission?: boolean;
	/** 권한 요청 지연 시간 (밀리초) */
	requestDelay?: number;
	/** 권한 모니터링 활성화 여부 */
	enableMonitoring?: boolean;
	/** 초기화 완료 콜백 */
	onInitialized?: (status: NotificationPermission) => void;
	/** 권한 변경 콜백 */
	onPermissionChanged?: (status: NotificationPermission) => void;
}

/**
 * 알림 시스템 초기화
 *
 * @param options 초기화 옵션
 * @returns Promise<NotificationPermission> 최종 권한 상태
 */
export async function initializeNotificationSystem(
	options: NotificationInitOptions = {}
): Promise<NotificationPermission> {
	const {
		autoRequestPermission = false,
		requestDelay = 2000,
		enableMonitoring = true,
		onInitialized,
		onPermissionChanged
	} = options;

	console.log('알림 시스템 초기화 시작...');

	try {
		// 1. 현재 권한 상태 확인
		let currentStatus = getNotificationPermissionStatus();
		console.log('현재 알림 권한 상태:', currentStatus);

		// 2. 권한 모니터링 시작 (옵션)
		if (enableMonitoring) {
			startPermissionMonitoring();
		}

		// 3. 자동 권한 요청 (옵션)
		if (autoRequestPermission && currentStatus === 'default') {
			console.log(`${requestDelay}ms 후 알림 권한을 요청합니다...`);

			setTimeout(async () => {
				try {
					const newStatus = await requestNotificationPermission();
					console.log('권한 요청 결과:', newStatus);

					if (onPermissionChanged) {
						onPermissionChanged(newStatus);
					}
				} catch (error) {
					console.error('자동 권한 요청 실패:', error);
				}
			}, requestDelay);
		}

		// 4. 대체 알림 모드 설정
		const useAlternativeMode = currentStatus !== 'granted';
		setAlternativeNotificationMode(useAlternativeMode);

		// 5. 초기화 완료 콜백 호출
		if (onInitialized) {
			onInitialized(currentStatus);
		}

		console.log('알림 시스템 초기화 완료');
		return currentStatus;
	} catch (error) {
		console.error('알림 시스템 초기화 실패:', error);

		// 실패 시 대체 모드로 설정
		setAlternativeNotificationMode(true);

		if (onInitialized) {
			onInitialized('denied');
		}

		return 'denied';
	}
}

/**
 * 사용자 친화적인 권한 요청
 *
 * 사용자에게 알림의 중요성을 설명하고 권한을 요청합니다.
 *
 * @param message 사용자에게 표시할 메시지
 * @returns Promise<NotificationPermission>
 */
export async function requestPermissionWithGuidance(
	message: string = '중요한 알림을 놓치지 않으려면 브라우저 알림을 허용해주세요.'
): Promise<NotificationPermission> {
	const currentStatus = getNotificationPermissionStatus();

	// 이미 권한이 있거나 거부된 경우
	if (currentStatus !== 'default') {
		return currentStatus;
	}

	// 사용자에게 안내 메시지 표시
	const userConfirmed = confirm(`${message}\n\n알림을 허용하시겠습니까?`);

	if (!userConfirmed) {
		console.log('사용자가 알림 권한 요청을 거부했습니다.');
		setAlternativeNotificationMode(true);
		return 'default';
	}

	// 권한 요청
	try {
		const result = await requestNotificationPermission();
		console.log('안내 후 권한 요청 결과:', result);
		return result;
	} catch (error) {
		console.error('안내 후 권한 요청 실패:', error);
		return 'denied';
	}
}

/**
 * 알림 시스템 상태 확인
 *
 * @returns NotificationSystemStatus 시스템 상태 정보
 */
export function getNotificationSystemStatus(): NotificationSystemStatus {
	const permission = getNotificationPermissionStatus();
	const isSupported = 'Notification' in window;
	const isAvailable = isSupported && permission === 'granted';

	return {
		isSupported,
		permission,
		isAvailable,
		needsPermission: permission === 'default',
		isBlocked: permission === 'denied',
		recommendAlternative: !isAvailable
	};
}

/**
 * 알림 시스템 진단
 *
 * 시스템 상태를 확인하고 문제점과 해결방안을 제시합니다.
 *
 * @returns NotificationDiagnostic 진단 결과
 */
export function diagnoseNotificationSystem(): NotificationDiagnostic {
	const status = getNotificationSystemStatus();
	const issues: string[] = [];
	const recommendations: string[] = [];

	// 브라우저 지원 확인
	if (!status.isSupported) {
		issues.push('브라우저가 알림을 지원하지 않습니다.');
		recommendations.push('최신 브라우저로 업데이트하거나 다른 브라우저를 사용해보세요.');
	}

	// 권한 상태 확인
	if (status.needsPermission) {
		issues.push('알림 권한이 요청되지 않았습니다.');
		recommendations.push('알림 권한을 요청하여 브라우저 알림을 활성화하세요.');
	}

	if (status.isBlocked) {
		issues.push('알림 권한이 거부되었습니다.');
		recommendations.push('브라우저 설정에서 이 사이트의 알림을 허용해주세요.');
	}

	// 대체 방안 제시
	if (status.recommendAlternative) {
		recommendations.push('현재는 화면 내 토스트 알림을 사용합니다.');
	}

	return {
		status,
		issues,
		recommendations,
		severity: issues.length > 0 ? (status.isBlocked ? 'high' : 'medium') : 'low'
	};
}

// 타입 정의
export interface NotificationSystemStatus {
	/** 브라우저 알림 지원 여부 */
	isSupported: boolean;
	/** 현재 권한 상태 */
	permission: NotificationPermission;
	/** 알림 사용 가능 여부 */
	isAvailable: boolean;
	/** 권한 요청 필요 여부 */
	needsPermission: boolean;
	/** 권한 차단 여부 */
	isBlocked: boolean;
	/** 대체 방식 권장 여부 */
	recommendAlternative: boolean;
}

export interface NotificationDiagnostic {
	/** 시스템 상태 */
	status: NotificationSystemStatus;
	/** 발견된 문제점들 */
	issues: string[];
	/** 권장 해결방안들 */
	recommendations: string[];
	/** 문제 심각도 */
	severity: 'low' | 'medium' | 'high';
}
