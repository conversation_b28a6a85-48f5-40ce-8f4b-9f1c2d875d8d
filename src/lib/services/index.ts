/**
 * SSE 서비스 통합 인덱스
 *
 * SSE 관련 모든 기능을 한 곳에서 import할 수 있도록 제공합니다.
 */
import {
	startAllMonitoring,
	resetConnectionState,
	stopAllMonitoring
} from '$lib/services/sseConnectionState';
import { initializeDataHandlers } from '$lib/services/sseDataHandlers';
import { resetStats } from '$lib/services/sseStats';

export type {
	SseMessage,
	NotificationMessage,
	DataUpdateMessage,
	MessageHandler,
	SseConnectionOptions,
	SseConnectionStatus,
	SseConnectionState,
	SseConnectionStats
} from '$lib/types/sseTypes';

// 연결 관리 (간단한 버전)
export {
	connect,
	disconnect,
	manualReconnect as manualReconnectDirect,
	getCurrentEventSource,
	createDefaultSseConnection,
	createDefaultSseConnectionSync,
	closeSseConnection,
	getCurrentSseUrl,
	cancelReconnection,
	scheduleReconnection
} from '$lib/services/sseConnection';

// 연결 상태 관리
export {
	sseConnectionState,
	updateConnectionStatus,
	incrementReconnectAttempts,
	updateNetworkStatus,
	resetConnectionState,
	isConnected,
	isConnecting,
	hasError,
	canReconnect,
	initializeNetworkMonitoring,
	startAllMonitoring,
	stopAllMonitoring,
	performAdvancedHealthCheck,
	checkConnectionHealth,
	getDebugInfo,
	getCurrentConnectionState,
	connectionQuality,
	currentLatency,
	lastHeartbeat
} from '$lib/services/sseConnectionState';

// 연결 통계
export {
	sseStats,
	recordConnection,
	recordReconnection,
	recordMessage,
	recordError,
	updateConnectionUptime,
	recordLatency,
	resetStats,
	formatStats,
	getStats,
	startPerformanceMonitoring,
	stopPerformanceMonitoring
} from '$lib/services/sseStats';

// 메시지 라우팅
export {
	handleSseMessage,
	routeMessage,
	validateMessage,
	messageHandlerRegistry,
	handleNotificationEvent,
	handleDataUpdateEvent
} from '$lib/services/sseMessageRouter';

// 데이터 핸들러
export {
	initializeDataHandlers,
	getUpdateHistory,
	getUpdateStatistics,
	cleanupUpdateHistory,
	forceEmployeeUpdate,
	forceGroupUpdate,
	getEmployeeSyncStatus,
	getGroupSyncStatus
} from '$lib/services/sseDataHandlers';

// 데이터 동기화
export {
	requestMissedData,
	retrySyncIfNeeded,
	getSyncState,
	resetSyncState,
	triggerManualSync
} from '$lib/services/sseDataSync';

/**
 * 간단한 SSE 시스템 시작
 */
export async function startSseSystem(): Promise<EventSource | null> {
	// 모든 모니터링 시작
	startAllMonitoring();

	// 데이터 핸들러 초기화 (IndexedDB 자동 초기화)
	await initializeDataHandlers();

	// 간단한 연결 사용
	const { connect } = await import('$lib/services/sseConnection');
	const eventSource = await connect();

	if (eventSource) {
		console.log('SSE 시스템 시작됨');
	} else {
		console.error('SSE 시스템 시작 실패');
	}

	return eventSource;
}

/**
 * SSE 시스템 종료
 */
export function stopSseSystem(): void {
	// 간단한 연결 종료
	import('$lib/services/sseConnection').then(({ disconnect }) => {
		disconnect();
	});

	// 모든 모니터링 중지
	stopAllMonitoring();

	// 상태 초기화
	resetConnectionState();
	resetStats();

	console.log('SSE 시스템 종료됨');
}
