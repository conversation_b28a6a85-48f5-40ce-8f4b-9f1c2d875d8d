/**
 * SSE 데이터 업데이트 핸들러 - 메인 컨트롤러
 *
 * 각 데이터 모델별 업데이트 처리를 담당합니다.
 * IndexedDB 저장, 업데이트 히스토리 관리, UI 피드백을 포함합니다.
 */

import type { DataUpdateMessage } from '$lib/types/sseTypes';
import { registerMessageHandler } from '$lib/services/sseMessageRouter';

// 분리된 핸들러들 import
import { handleEmployeeUpdate } from './dataHandlers/employeeDataHandler';
import { handleGroupUpdate } from './dataHandlers/groupDataHandler';
import { handleCategoryUpdate } from './dataHandlers/categoryDataHandler';
import { handleRepairGradeUpdate } from './dataHandlers/repairGradeDataHandler';
import {
	handleGenericDataUpdate,
	registerDataType,
	getRegisteredDataTypes as getRegisteredDataTypesFromGeneric,
	type DataTypeConfig
} from './dataHandlers/genericDataHandler';
import { showUpdateMessage } from './dataHandlers/uiFeedbackHandler';
import {
	addToUpdateHistory,
	getUpdateHistory as getHistoryFromDB,
	getModelUpdateHistory as getModelHistoryFromDB,
	getUpdateStatistics as getStatsFromDB,
	cleanupUpdateHistory as cleanupHistoryFromDB,
	getDeletionHistory as getDeletionHistoryFromDB,
	restoreDeletedItems as restoreItemsFromDB
} from './dataHandlers/historyManager';
import {
	getDataTypeStatistics as getDataStatsFromDB,
	getEmployeeSyncStatus as getEmpSyncFromDB,
	getGroupSyncStatus as getGroupSyncFromDB
} from './dataHandlers/statisticsManager';
import { updateRepairGradeStore } from '$lib/services/storeUpdaters';

/**
 * 데이터 스토어 인터페이스
 */
interface DataStore<T> {
	addItem: (item: T) => void;
	updateItem: (id: number, item: Partial<T>) => void;
	removeItem: (id: number) => void;
	batchUpdate: (items: T[]) => void;
}

/**
 * 스토어 레지스트리 상태 관리
 */
const stores: Map<string, DataStore<any>> = new Map();

/**
 * IndexedDB 인스턴스 저장
 */
let dbInstance: IDBDatabase | null = null;

/**
 * 데이터 타입 레지스트리 (범용 핸들러용)
 */
const dataTypeRegistry: Map<string, DataTypeConfig> = new Map();

/**
 * 스토어 등록
 */
export function registerStore<T>(modelName: string, store: DataStore<T>): void {
	stores.set(modelName, store);
}

/**
 * 스토어 가져오기
 */
function getStore<T>(modelName: string): DataStore<T> | undefined {
	return stores.get(modelName);
}

/**
 * 등록된 스토어 목록
 */
export function getRegisteredModels(): string[] {
	return Array.from(stores.keys());
}

/**
 * 데이터 타입 등록 (범용 핸들러용)
 */
export function registerDataTypeConfig(modelName: string, config: DataTypeConfig): void {
	dataTypeRegistry.set(modelName, config);
	registerDataType(modelName, config);
}

/**
 * 등록된 데이터 타입 목록 조회 (외부 노출용)
 */
export function getRegisteredDataTypes(): string[] {
	return getRegisteredDataTypesFromGeneric();
}

/**
 * 데이터 업데이트 핸들러 초기화
 *
 * @param db IndexedDB 인스턴스 (선택사항)
 */
export async function initializeDataHandlers(db?: IDBDatabase): Promise<void> {
	if (db) {
		dbInstance = db;
	} else {
		// IndexedDB 자동 초기화
		const { initializeWithMigration } = await import('./indexedDBManager');
		dbInstance = await initializeWithMigration();
	}

	// 기본 데이터 타입들 등록
	initializeDefaultDataTypes();

	// 데이터 업데이트 메시지 핸들러 등록
	registerMessageHandler('data_update', handleDataUpdateWithDB);

	console.log('데이터 업데이트 핸들러 초기화 완료');
}

/**
 * 기본 데이터 타입들 초기화
 */
function initializeDefaultDataTypes(): void {
	// 수리 등급 데이터 타입 등록
	registerDataTypeConfig('repair_grades', {
		storeName: 'repair_grades',
		displayName: '수리 등급',
		storeUpdater: updateRepairGradeStore,
		validatePayload: (payload) => {
			if (Array.isArray(payload)) {
				return payload.every((item) => item.id && item.grade_name);
			}
			return payload && payload.id && payload.grade_name;
		},
		transformPayload: (payload) => {
			// 수리 등급 데이터 정규화
			if (Array.isArray(payload)) {
				return payload.map((item) => ({
					...item,
					updated_at: item.updated_at || new Date().toISOString()
				}));
			}
			return {
				...payload,
				updated_at: payload.updated_at || new Date().toISOString()
			};
		},
		getItemSummary: (item) => `${item.grade_name} (레벨: ${item.level || 'N/A'})`
	});

	console.log('기본 데이터 타입 등록 완료');
}

/**
 * IndexedDB와 함께 데이터 업데이트 처리
 */
async function handleDataUpdateWithDB(update: DataUpdateMessage): Promise<void> {
	if (!dbInstance) {
		console.error('IndexedDB가 초기화되지 않았습니다.');
		return;
	}

	try {
		console.log('데이터 업데이트 메시지 처리 시작:', update);

		const { model, action, payload, affected_ids } = update;

		// 모델별 처리 분기
		switch (model) {
			case 'categories':
				await handleCategoryUpdate(dbInstance, action, payload, affected_ids);
				break;
			case 'employees':
				await handleEmployeeUpdate(dbInstance, action, payload, affected_ids);
				break;
			case 'groups':
				await handleGroupUpdate(dbInstance, action, payload, affected_ids);
				break;
			case 'repair_grades':
				await handleRepairGradeUpdate(dbInstance, action, payload, affected_ids);
				break;
			default:
				// 범용 핸들러로 처리 시도
				if (dataTypeRegistry.has(model)) {
					await handleGenericDataUpdate(dbInstance, model, action, payload, affected_ids);
				} else {
					console.warn('알 수 없는 모델 업데이트:', model);
					return;
				}
		}

		// 기존 스토어 시스템도 함께 처리
		handleDataUpdate(update);

		// 업데이트 히스토리에 추가
		await addToUpdateHistory(dbInstance, update);

		console.log(`${model} 데이터 업데이트 처리 완료:`, action, affected_ids);
	} catch (error) {
		console.error('데이터 업데이트 메시지 처리 실패:', error);
		throw error;
	}
}

/**
 * 기존 스토어 시스템을 위한 데이터 업데이트 처리
 */
function handleDataUpdate(update: DataUpdateMessage): void {
	console.log(`스토어 업데이트 처리: ${update.model} - ${update.action}`);

	const store = getStore(update.model);
	if (!store) {
		console.warn(`등록되지 않은 모델: ${update.model}`);
		return;
	}

	try {
		switch (update.action) {
			case 'created':
				handleCreated(store, update);
				break;
			case 'updated':
				handleUpdated(store, update);
				break;
			case 'deleted':
				handleDeleted(store, update);
				break;
			case 'batch_updated':
				handleBatchUpdated(store, update);
				break;
			default:
				console.warn(`알 수 없는 액션: ${update.action}`);
		}
	} catch (error) {
		console.error(`스토어 업데이트 처리 오류 (${update.model}):`, error);
	}
}

/**
 * 생성 처리
 */
function handleCreated(store: DataStore<any>, update: DataUpdateMessage): void {
	if (Array.isArray(update.payload)) {
		// 여러 항목 생성
		update.payload.forEach((item) => store.addItem(item));
	} else {
		// 단일 항목 생성
		store.addItem(update.payload);
	}
}

/**
 * 업데이트 처리
 */
function handleUpdated(store: DataStore<any>, update: DataUpdateMessage): void {
	if (Array.isArray(update.payload)) {
		// 여러 항목 업데이트
		update.payload.forEach((item) => {
			if (item.id) {
				store.updateItem(item.id, item);
			}
		});
	} else {
		// 단일 항목 업데이트
		if (update.payload.id) {
			store.updateItem(update.payload.id, update.payload);
		}
	}
}

/**
 * 삭제 처리
 */
function handleDeleted(store: DataStore<any>, update: DataUpdateMessage): void {
	update.affected_ids.forEach((id) => store.removeItem(id));
}

/**
 * 배치 업데이트 처리
 */
function handleBatchUpdated(store: DataStore<any>, update: DataUpdateMessage): void {
	if (Array.isArray(update.payload)) {
		store.batchUpdate(update.payload);
	} else {
		console.warn('배치 업데이트 페이로드가 배열이 아닙니다:', update.payload);
	}
}

// ===== 공개 API 함수들 =====

/**
 * 업데이트 히스토리 조회
 */
export async function getUpdateHistory(limit: number = 20): Promise<any[]> {
	if (!dbInstance) {
		console.error('IndexedDB가 초기화되지 않았습니다.');
		return [];
	}

	return getHistoryFromDB(dbInstance, limit);
}

/**
 * 특정 모델의 업데이트 히스토리 조회
 */
export async function getModelUpdateHistory(model: string, limit: number = 10): Promise<any[]> {
	if (!dbInstance) {
		console.error('IndexedDB가 초기화되지 않았습니다.');
		return [];
	}

	return getModelHistoryFromDB(dbInstance, model, limit);
}

/**
 * 업데이트 통계 조회
 */
export async function getUpdateStatistics(): Promise<{
	totalUpdates: number;
	modelCounts: Record<string, number>;
	actionCounts: Record<string, number>;
	recentUpdates: number;
}> {
	if (!dbInstance) {
		console.error('IndexedDB가 초기화되지 않았습니다.');
		return {
			totalUpdates: 0,
			modelCounts: {},
			actionCounts: {},
			recentUpdates: 0
		};
	}

	return getStatsFromDB(dbInstance);
}

/**
 * 업데이트 히스토리 정리
 */
export async function cleanupUpdateHistory(keepDays: number = 7): Promise<void> {
	if (!dbInstance) {
		console.error('IndexedDB가 초기화되지 않았습니다.');
		return;
	}

	return cleanupHistoryFromDB(dbInstance, keepDays);
}

/**
 * 특정 데이터 타입의 통계 조회
 */
export async function getDataTypeStatistics(modelName: string): Promise<{
	totalItems: number;
	lastUpdate: string | null;
	recentChanges: number;
	dataHealth: 'good' | 'warning' | 'error';
}> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	const config = dataTypeRegistry.get(modelName);
	if (!config) {
		throw new Error(`등록되지 않은 데이터 타입: ${modelName}`);
	}

	return getDataStatsFromDB(dbInstance, modelName, config);
}

/**
 * 삭제 히스토리 조회
 */
export async function getDeletionHistory(dataType?: string, limit: number = 20): Promise<any[]> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	return getDeletionHistoryFromDB(dbInstance, dataType, limit);
}

/**
 * 데이터 복구 (삭제 히스토리에서)
 */
export async function restoreDeletedItems(historyId: string): Promise<boolean> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	const success = await restoreItemsFromDB(dbInstance, historyId, dataTypeRegistry);

	if (success) {
		// UI 피드백 표시
		showUpdateMessage('데이터가 성공적으로 복구되었습니다.', 'success');
	} else {
		showUpdateMessage('데이터 복구 중 오류가 발생했습니다.', 'error');
	}

	return success;
}

// ===== 테스트용 공개 API =====

/**
 * 직원 업데이트 강제 실행 (개발/테스트용)
 */
export async function forceEmployeeUpdate(
	action: 'created' | 'updated' | 'deleted' | 'batch_updated' | 'group_membership_changed',
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	console.log('직원 업데이트 강제 실행:', { action, affectedIds });

	try {
		await handleEmployeeUpdate(dbInstance, action, payload, affectedIds);
		console.log('직원 업데이트 강제 실행 완료');
	} catch (error) {
		console.error('직원 업데이트 강제 실행 실패:', error);
		throw error;
	}
}

/**
 * 그룹 업데이트 강제 실행 (개발/테스트용)
 */
export async function forceGroupUpdate(
	action: 'created' | 'updated' | 'deleted' | 'batch_updated' | 'membership_changed',
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	console.log('그룹 업데이트 강제 실행:', { action, affectedIds });

	try {
		await handleGroupUpdate(dbInstance, action, payload, affectedIds);
		console.log('그룹 업데이트 강제 실행 완료');
	} catch (error) {
		console.error('그룹 업데이트 강제 실행 실패:', error);
		throw error;
	}
}

/**
 * 카테고리 업데이트 강제 실행 (개발/테스트용)
 */
export async function forceCategoryUpdate(
	action: 'created' | 'updated' | 'deleted' | 'batch_updated',
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	console.log('카테고리 업데이트 강제 실행:', { action, affectedIds });

	try {
		await handleCategoryUpdate(dbInstance, action, payload, affectedIds);
		console.log('카테고리 업데이트 강제 실행 완료');
	} catch (error) {
		console.error('카테고리 업데이트 강제 실행 실패:', error);
		throw error;
	}
}

/**
 * 수리 등급 업데이트 강제 실행 (개발/테스트용)
 */
export async function forceRepairGradeUpdate(
	action: 'created' | 'updated' | 'deleted' | 'batch_updated' | 'synchronized',
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	console.log('수리 등급 업데이트 강제 실행:', { action, affectedIds });

	try {
		await handleRepairGradeUpdate(dbInstance, action, payload, affectedIds);
		console.log('수리 등급 업데이트 강제 실행 완료');
	} catch (error) {
		console.error('수리 등급 업데이트 강제 실행 실패:', error);
		throw error;
	}
}

/**
 * 범용 데이터 업데이트 강제 실행 (개발/테스트용)
 */
export async function forceGenericDataUpdate(
	modelName: string,
	action: string,
	payload: any,
	affectedIds: number[]
): Promise<void> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	console.log('범용 데이터 업데이트 강제 실행:', { modelName, action, affectedIds });

	try {
		await handleGenericDataUpdate(dbInstance, modelName, action, payload, affectedIds);
		console.log('범용 데이터 업데이트 강제 실행 완료');
	} catch (error) {
		console.error('범용 데이터 업데이트 강제 실행 실패:', error);
		throw error;
	}
}

/**
 * 직원 동기화 상태 확인
 */
export async function getEmployeeSyncStatus(): Promise<{
	totalEmployees: number;
	lastUpdate: string | null;
	groupMembershipConsistency: boolean;
}> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	return getEmpSyncFromDB(dbInstance);
}

/**
 * 그룹 동기화 상태 확인
 */
export async function getGroupSyncStatus(): Promise<{
	totalGroups: number;
	lastUpdate: string | null;
	membershipConsistency: boolean;
	averageMembersPerGroup: number;
}> {
	if (!dbInstance) {
		throw new Error('IndexedDB가 초기화되지 않았습니다.');
	}

	return getGroupSyncFromDB(dbInstance);
}
