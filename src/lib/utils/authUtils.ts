/**
 * 인증 토큰 가져오기 (Sanctum 쿠키 기반에서는 null 반환)
 */
export function getAuthToken(): string | null {
	// Sanctum 쿠키 기반 인증에서는 토큰이 필요 없음
	return null;
}

/**
 * 현재 사용자 ID 가져오기
 */
export function getCurrentUserId(): number | null {
	const userStr = window.sessionStorage.getItem('user');
	if (userStr) {
		try {
			const user = JSON.parse(userStr);
			return user.id;
		} catch (error) {
			console.error('사용자 정보 파싱 오류:', error);
		}
	}
	return null;
}

/**
 * 사용자 인증 상태 확인 (Sanctum SPA 방식)
 *
 * Sanctum SPA에서는 HTTP-only 쿠키로 인증이 처리되므로
 * 클라이언트에서 쿠키를 직접 확인할 수 없습니다.
 * 대신 세션 스토리지의 사용자 정보 존재 여부로 판단합니다.
 */
export function isAuthenticated(): boolean {
	// 세션 스토리지에 사용자 정보가 있으면 인증된 것으로 간주
	const user = window.sessionStorage.getItem('user');
	return !!user;
}

/**
 * 서버에 인증 상태 확인 요청
 *
 * 실제 인증 상태를 서버에서 확인하는 함수입니다.
 * HTTP-only 쿠키가 유효한지 서버에서 검증합니다.
 */
export async function checkAuthenticationStatus(): Promise<boolean> {
	try {
		const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;

		const response = await fetch(`${apiEndpoint}/user`, {
			method: 'GET',
			headers: {
				'Content-Type': 'application/json',
				Accept: 'application/json'
			},
			credentials: 'include' // HTTP-only 쿠키 포함
		});

		if (response.ok) {
			// 서버에서 사용자 정보를 반환하면 인증된 상태
			const userData = await response.json();

			// 세션 스토리지 업데이트
			window.sessionStorage.setItem('user', JSON.stringify(userData));

			return true;
		} else if (response.status === 401) {
			// 인증되지 않은 상태
			window.sessionStorage.removeItem('user');
			return false;
		} else {
			// 기타 오류 (네트워크 오류 등)
			console.warn('인증 상태 확인 실패:', response.status);
			return false;
		}
	} catch (error) {
		console.error('인증 상태 확인 중 오류:', error);
		return false;
	}
}

/**
 * CSRF 토큰 가져오기 (Sanctum SPA용)
 */
export function getCsrfToken(): string | null {
	// 1. 메타 태그에서 CSRF 토큰 확인
	const metaToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
	if (metaToken) return metaToken;

	// 2. XSRF-TOKEN 쿠키에서 가져오기 (Laravel Sanctum 기본 방식)
	const xsrfCookie = document.cookie
		.split(';')
		.find((cookie) => cookie.trim().startsWith('XSRF-TOKEN='));

	if (xsrfCookie) {
		return decodeURIComponent(xsrfCookie.split('=')[1]);
	}

	// 3. laravel_session 쿠키 확인 (세션이 있는지 확인)
	const sessionCookie = document.cookie
		.split(';')
		.find((cookie) => cookie.trim().startsWith('laravel_session='));

	if (!sessionCookie) {
		console.warn('Laravel 세션 쿠키가 없습니다. 로그인이 필요할 수 있습니다.');
	}

	return null;
}

/**
 * Sanctum CSRF 쿠키 초기화
 *
 * SSE 연결 전에 CSRF 쿠키를 먼저 가져와야 합니다.
 */
export async function initializeCsrfToken(): Promise<void> {
	try {
		const baseUrl = import.meta.env.VITE_HOME_URL;

		const response = await fetch(`${baseUrl}/sanctum/csrf-cookie`, {
			method: 'GET',
			credentials: 'include'
		});

		if (!response.ok) {
			throw new Error(`CSRF 쿠키 초기화 실패: ${response.status}`);
		}

		console.log('CSRF 쿠키 초기화 완료');
	} catch (error) {
		console.error('CSRF 쿠키 초기화 오류:', error);
		throw error;
	}
}
