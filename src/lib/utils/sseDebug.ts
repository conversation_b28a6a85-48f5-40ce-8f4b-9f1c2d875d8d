/**
 * 간단한 SSE 429 에러 진단 도구
 *
 * SSE 동작 원리:
 * 1. 클라이언트가 서버에 한 번 연결 요청
 * 2. 서버가 연결을 열어둔 채로 유지
 * 3. 서버에서 데이터가 있을 때만 push
 * 4. 연결이 끊어진 경우에만 재연결 시도
 */

/**
 * 429 에러 원인 진단
 */
export async function diagnose429Error(): Promise<{
	issue: string;
	solution: string;
	details: any;
}> {
	const apiEndpoint = import.meta.env.VITE_API_ENDPOINT;
	const baseUrl = apiEndpoint.replace('/api', '');

	// 1. CSRF 쿠키 확인
	try {
		console.log('1. CSRF 쿠키 확인 중...');
		const csrfResponse = await fetch(`${baseUrl}/sanctum/csrf-cookie`, {
			method: 'GET',
			credentials: 'include'
		});

		if (!csrfResponse.ok) {
			return {
				issue: 'CSRF 쿠키 가져오기 실패',
				solution: '서버 CORS 설정 또는 Sanctum 설정 확인 필요',
				details: { status: csrfResponse.status, url: `${baseUrl}/sanctum/csrf-cookie` }
			};
		}

		// 2. 인증 상태 확인
		console.log('2. 인증 상태 확인 중...');
		const authResponse = await fetch(`${apiEndpoint}/user`, {
			method: 'GET',
			credentials: 'include'
		});

		if (authResponse.status === 401) {
			return {
				issue: '인증되지 않은 상태',
				solution: '로그인이 필요합니다',
				details: { status: 401 }
			};
		}

		if (authResponse.status === 429) {
			return {
				issue: 'API 요청 제한 초과',
				solution: '잠시 기다린 후 다시 시도하세요',
				details: { status: 429, endpoint: '/user' }
			};
		}

		// 3. SSE 엔드포인트 직접 확인
		console.log('3. SSE 엔드포인트 확인 중...');
		const sseEndpoint = import.meta.env.VITE_SSE_ENDPOINT;
		const sseResponse = await fetch(sseEndpoint, {
			method: 'GET',
			credentials: 'include'
		});

		if (sseResponse.status === 429) {
			return {
				issue: 'SSE 엔드포인트 요청 제한 초과',
				solution: '연결 시도 간격을 늘리거나 서버 설정 확인',
				details: { status: 429, endpoint: sseEndpoint }
			};
		}

		return {
			issue: '알 수 없는 문제',
			solution: '서버 로그 확인 필요',
			details: {
				csrf: csrfResponse.status,
				auth: authResponse.status,
				sse: sseResponse.status
			}
		};
	} catch (error) {
		return {
			issue: '네트워크 오류',
			solution: '인터넷 연결 및 서버 상태 확인',
			details: { error: String(error) }
		};
	}
}

/**
 * 쿠키 상태 확인
 */
export function checkCookies(): string[] {
	return document.cookie
		.split(';')
		.map((c) => c.trim())
		.filter((c) => c.length > 0);
}
