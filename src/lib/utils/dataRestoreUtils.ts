/**
 * 데이터 복원 유틸리티
 *
 * 페이지 새로고침 시 로컬스토리지 데이터 복원과 관련된 유틸리티 함수들을 제공합니다.
 */

import { AudioEvent, playAudio } from '../utils/AudioManager';

/**
 * 데이터 복원 결과 인터페이스
 */
export interface DataRestoreResult {
	/** 복원 성공 여부 */
	success: boolean;

	/** 복원된 팔레트 수 */
	palletCount: number;

	/** 복원된 전체 상품 수 */
	totalProductCount: number;

	/** 복원된 대기 중인 상품 수 */
	pendingProductCount: number;

	/** 복원된 실패한 상품 수 */
	failedProductCount: number;

	/** 복원 메시지 */
	message: string;

	/** 복원된 팔레트 목록 */
	restoredPallets: Array<{
		palletId: string;
		productCount: number;
		pendingCount: number;
		failedCount: number;
		lastUpdated: number;
	}>;

	/** 권장 팔레트 ID (가장 최근에 작업한 팔레트) */
	recommendedPalletId?: string;
}

/**
 * 페이지 로드 시 데이터 복원 확인
 * 로컬스토리지에 저장된 배치 데이터가 있는지 확인하고 복원 정보를 반환합니다.
 *
 * @returns 데이터 복원 결과
 */
export async function checkDataRestore(): Promise<DataRestoreResult> {
	try {
		// 배치 서비스에서 팔레트 목록 가져오기
		const { getAllPallets } = await import('../services/batchProductService');
		const pallets = await getAllPallets();

		// 복원할 데이터가 없는 경우
		if (pallets.length === 0) {
			return {
				success: false,
				palletCount: 0,
				totalProductCount: 0,
				pendingProductCount: 0,
				failedProductCount: 0,
				message: '복원할 데이터가 없습니다.',
				restoredPallets: []
			};
		}

		// 통계 계산
		let totalProductCount = 0;
		let pendingProductCount = 0;
		let failedProductCount = 0;

		const restoredPallets = pallets.map((pallet) => {
			totalProductCount += pallet.productCount;
			pendingProductCount += pallet.pendingCount;
			failedProductCount += pallet.failedCount;

			return {
				palletId: pallet.palletId,
				productCount: pallet.productCount,
				pendingCount: pallet.pendingCount,
				failedCount: pallet.failedCount,
				lastUpdated: pallet.lastUpdated
			};
		});

		// 가장 최근에 작업한 팔레트 찾기 (권장 팔레트)
		const recommendedPallet = pallets.reduce((latest, current) =>
			current.lastUpdated > latest.lastUpdated ? current : latest
		);

		// 복원 메시지 생성
		let message = `${pallets.length}개 팔레트의 데이터가 복원되었습니다.`;
		if (pendingProductCount > 0) {
			message += ` (대기 중: ${pendingProductCount}개)`;
		}
		if (failedProductCount > 0) {
			message += ` (실패: ${failedProductCount}개)`;
		}

		return {
			success: true,
			palletCount: pallets.length,
			totalProductCount,
			pendingProductCount,
			failedProductCount,
			message,
			restoredPallets,
			recommendedPalletId: recommendedPallet.palletId
		};
	} catch (error) {
		console.error('데이터 복원 확인 중 오류 발생:', error);
		return {
			success: false,
			palletCount: 0,
			totalProductCount: 0,
			pendingProductCount: 0,
			failedProductCount: 0,
			message: `데이터 복원 확인 실패: ${error}`,
			restoredPallets: []
		};
	}
}

/**
 * 데이터 복원 알림 표시
 * 복원된 데이터에 대한 사용자 알림을 표시합니다.
 *
 * @param restoreResult 데이터 복원 결과
 * @param showModal 모달 표시 함수
 * @param setProdScanMessage 스캔 메시지 설정 함수
 * @returns 사용자가 선택한 팔레트 ID (취소 시 null)
 */
export async function showDataRestoreNotification(
	restoreResult: DataRestoreResult,
	showModal: (message: string, type: 'error' | 'warning' | 'success', title: string) => void,
	setProdScanMessage: (message: string) => void
): Promise<string | null> {
	try {
		// 복원할 데이터가 없는 경우
		if (!restoreResult.success || restoreResult.palletCount === 0) {
			return null;
		}

		// 오디오 피드백
		// await playAudio(AudioEvent.INFO);

		// 단일 팔레트인 경우 간단한 알림
		if (restoreResult.palletCount === 1) {
			const pallet = restoreResult.restoredPallets[0];
			const shouldRestore = confirm(
				`이전 작업 데이터가 발견되었습니다.\n\n` +
					`팔레트: ${pallet.palletId}\n` +
					`상품 수: ${pallet.productCount}개\n` +
					`대기 중: ${pallet.pendingCount}개\n` +
					`실패: ${pallet.failedCount}개\n\n` +
					`해당 팔레트로 이동하시겠습니까?`
			);

			if (shouldRestore) {
				setProdScanMessage(`이전 작업 데이터가 복원되었습니다: ${restoreResult.message}`);
				return pallet.palletId;
			}

			return null;
		}

		// 다중 팔레트인 경우 선택 옵션 제공
		let confirmMessage = `이전 작업 데이터가 발견되었습니다.\n\n`;
		confirmMessage += `총 ${restoreResult.palletCount}개 팔레트:\n`;

		// 최대 5개 팔레트만 표시
		const displayPallets = restoreResult.restoredPallets.slice(0, 5);
		displayPallets.forEach((pallet, index) => {
			const isRecommended = pallet.palletId === restoreResult.recommendedPalletId;
			confirmMessage += `${index + 1}. ${pallet.palletId}${isRecommended ? ' (최근)' : ''} - ${pallet.productCount}개 상품\n`;
		});

		if (restoreResult.restoredPallets.length > 5) {
			confirmMessage += `... 외 ${restoreResult.restoredPallets.length - 5}개 팔레트\n`;
		}

		confirmMessage += `\n가장 최근에 작업한 팔레트(${restoreResult.recommendedPalletId})로 이동하시겠습니까?`;

		const shouldRestore = confirm(confirmMessage);

		if (shouldRestore && restoreResult.recommendedPalletId) {
			setProdScanMessage(`이전 작업 데이터가 복원되었습니다: ${restoreResult.message}`);
			return restoreResult.recommendedPalletId;
		}

		// 복원하지 않는 경우에도 알림 메시지 표시
		setProdScanMessage(
			`${restoreResult.palletCount}개 팔레트의 이전 작업 데이터가 있습니다. 필요시 해당 팔레트로 이동해주세요.`
		);

		return null;
	} catch (error) {
		console.error('데이터 복원 알림 표시 중 오류 발생:', error);
		showModal(`데이터 복원 알림 표시 실패: ${error}`, 'error', '데이터 복원 오류');
		return null;
	}
}

/**
 * 팔레트 ID에서 팔레트 번호 추출
 * locationCode 형식(A-1-1-level-column)에서 팔레트 번호(level-column)를 추출합니다.
 *
 * @param palletId 팔레트 ID (locationCode 형식)
 * @returns 팔레트 번호 (level-column 형식) 또는 null
 */
export function extractPalletNumber(palletId: string): string | null {
	try {
		// A-1-1-level-column 형식에서 level-column 추출
		const parts = palletId.split('-');
		if (parts.length >= 5) {
			return `${parts[3]}-${parts[4]}`;
		}

		// 이미 level-column 형식인 경우
		if (parts.length === 2) {
			return palletId;
		}

		console.warn('팔레트 ID 형식이 올바르지 않습니다:', palletId);
		return null;
	} catch (error) {
		console.error('팔레트 번호 추출 중 오류 발생:', error);
		return null;
	}
}

/**
 * 데이터 복원 통계 정보 생성
 * 복원된 데이터에 대한 상세 통계를 생성합니다.
 *
 * @param restoreResult 데이터 복원 결과
 * @returns 통계 정보 문자열
 */
export function generateRestoreStats(restoreResult: DataRestoreResult): string {
	if (!restoreResult.success) {
		return '복원된 데이터가 없습니다.';
	}

	let stats = `데이터 복원 통계:\n`;
	stats += `- 팔레트 수: ${restoreResult.palletCount}개\n`;
	stats += `- 전체 상품: ${restoreResult.totalProductCount}개\n`;
	stats += `- 대기 중: ${restoreResult.pendingProductCount}개\n`;
	stats += `- 실패: ${restoreResult.failedProductCount}개\n`;

	if (restoreResult.recommendedPalletId) {
		stats += `- 권장 팔레트: ${restoreResult.recommendedPalletId}\n`;
	}

	return stats;
}

/**
 * 자동 데이터 복원 설정
 * 페이지 로드 시 자동으로 데이터 복원을 시도합니다.
 *
 * @param options 복원 옵션
 * @returns 복원 결과
 */
export async function autoRestoreData(options: {
	/** 자동 복원 활성화 여부 */
	enabled: boolean;

	/** 자동으로 권장 팔레트로 이동할지 여부 */
	autoSelectRecommended: boolean;

	/** 복원 알림 표시 여부 */
	showNotification: boolean;

	/** 모달 표시 함수 */
	showModal?: (message: string, type: 'error' | 'warning' | 'success', title: string) => void;

	/** 스캔 메시지 설정 함수 */
	setProdScanMessage?: (message: string) => void;

	/** 팔레트 설정 함수 */
	setPalletNo?: (palletNo: string) => void;
}): Promise<DataRestoreResult> {
	try {
		// 자동 복원이 비활성화된 경우
		if (!options.enabled) {
			return {
				success: false,
				palletCount: 0,
				totalProductCount: 0,
				pendingProductCount: 0,
				failedProductCount: 0,
				message: '자동 데이터 복원이 비활성화되었습니다.',
				restoredPallets: []
			};
		}

		// 데이터 복원 확인
		const restoreResult = await checkDataRestore();

		// 복원할 데이터가 없는 경우
		if (!restoreResult.success || restoreResult.palletCount === 0) {
			return restoreResult;
		}

		// 알림 표시
		if (options.showNotification && options.showModal && options.setProdScanMessage) {
			const selectedPalletId = await showDataRestoreNotification(
				restoreResult,
				options.showModal,
				options.setProdScanMessage
			);

			// 사용자가 팔레트를 선택한 경우
			if (selectedPalletId && options.setPalletNo) {
				const palletNumber = extractPalletNumber(selectedPalletId);
				if (palletNumber) {
					options.setPalletNo(palletNumber);
				}
			}
		} else if (
			options.autoSelectRecommended &&
			restoreResult.recommendedPalletId &&
			options.setPalletNo
		) {
			// 자동으로 권장 팔레트 선택
			const palletNumber = extractPalletNumber(restoreResult.recommendedPalletId);
			if (palletNumber) {
				options.setPalletNo(palletNumber);

				if (options.setProdScanMessage) {
					options.setProdScanMessage(
						`자동으로 이전 작업 팔레트(${palletNumber})가 복원되었습니다.`
					);
				}
			}
		}

		return restoreResult;
	} catch (error) {
		console.error('자동 데이터 복원 중 오류 발생:', error);

		const errorResult: DataRestoreResult = {
			success: false,
			palletCount: 0,
			totalProductCount: 0,
			pendingProductCount: 0,
			failedProductCount: 0,
			message: `자동 데이터 복원 실패: ${error}`,
			restoredPallets: []
		};

		if (options.showModal) {
			options.showModal(
				`자동 데이터 복원 중 오류가 발생했습니다: ${error}`,
				'error',
				'데이터 복원 오류'
			);
		}

		return errorResult;
	}
}
