/**
 * 알림 스토어
 *
 * SSE를 통해 수신된 알림을 관리합니다.
 */

import { writable, derived } from 'svelte/store';
import { type NotificationMessage } from '$lib/types/sseTypes';
import { registerMessageHandler } from '$lib/services/sseMessageRouter';

/**
 * 알림 상태 인터페이스
 */
export interface NotificationState {
	id: number;
	title: string;
	message: string;
	type: string;
	priority: 'low' | 'normal' | 'high' | 'urgent';
	isRead: boolean;
	receivedAt: Date;
	expiresAt?: Date;
	actionUrl?: string;
	actionText?: string;
}

/**
 * 알림 목록 스토어
 */
export const notifications = writable<NotificationState[]>([]);

/**
 * 읽지 않은 알림 수
 */
export const unreadCount = derived(
	notifications,
	($notifications) => $notifications.filter((n) => !n.isRead).length
);

/**
 * 우선순위별 알림 수
 */
export const notificationsByPriority = derived(notifications, ($notifications) => {
	const counts = {
		urgent: 0,
		high: 0,
		normal: 0,
		low: 0
	};

	$notifications.forEach((notification) => {
		if (!notification.isRead) {
			counts[notification.priority]++;
		}
	});

	return counts;
});

/**
 * 최근 알림 (최대 10개)
 */
export const recentNotifications = derived(notifications, ($notifications) =>
	$notifications.sort((a, b) => b.receivedAt.getTime() - a.receivedAt.getTime()).slice(0, 10)
);

/**
 * 알림 추가
 */
export function addNotification(notificationMessage: NotificationMessage): void {
	const notification: NotificationState = {
		id: notificationMessage.id,
		title: notificationMessage.title,
		message: notificationMessage.message,
		type: notificationMessage.type,
		priority: notificationMessage.priority,
		isRead: false,
		receivedAt: new Date(),
		expiresAt: notificationMessage.expires_at
			? new Date(notificationMessage.expires_at)
			: undefined,
		actionUrl: notificationMessage.action_url,
		actionText: notificationMessage.action_text
	};

	notifications.update((items) => {
		// 중복 알림 확인
		const existingIndex = items.findIndex((item) => item.id === notification.id);
		if (existingIndex > -1) {
			// 기존 알림 업데이트
			items[existingIndex] = notification;
			return [...items];
		} else {
			// 새 알림 추가
			return [notification, ...items];
		}
	});

	// 만료된 알림 자동 제거
	cleanupExpiredNotifications();
}

/**
 * 알림 읽음 처리
 */
export function markAsRead(notificationId: number): void {
	notifications.update((items) =>
		items.map((item) => (item.id === notificationId ? { ...item, isRead: true } : item))
	);
}

/**
 * 모든 알림 읽음 처리
 */
export function markAllAsRead(): void {
	notifications.update((items) => items.map((item) => ({ ...item, isRead: true })));
}

/**
 * 알림 제거
 */
export function removeNotification(notificationId: number): void {
	notifications.update((items) => items.filter((item) => item.id !== notificationId));
}

/**
 * 모든 알림 제거
 */
export function clearAllNotifications(): void {
	notifications.set([]);
}

/**
 * 만료된 알림 정리
 */
export function cleanupExpiredNotifications(): void {
	const now = new Date();
	notifications.update((items) => items.filter((item) => !item.expiresAt || item.expiresAt > now));
}

/**
 * 우선순위별 알림 필터링
 */
export function getNotificationsByPriority(priority: 'low' | 'normal' | 'high' | 'urgent') {
	return derived(notifications, ($notifications) =>
		$notifications.filter((n) => n.priority === priority)
	);
}

/**
 * 알림 스토어 초기화
 */
export function initializeNotificationStore(): void {
	// SSE 메시지 핸들러 등록
	registerMessageHandler('notification', addNotification);

	// 주기적으로 만료된 알림 정리 (5분마다)
	setInterval(cleanupExpiredNotifications, 5 * 60 * 1000);

	console.log('알림 스토어 초기화 완료');
}

/**
 * 브라우저 알림 권한 요청
 */
export async function requestNotificationPermission(): Promise<boolean> {
	if (!('Notification' in window)) {
		console.warn('이 브라우저는 알림을 지원하지 않습니다');
		return false;
	}

	if (Notification.permission === 'granted') {
		return true;
	}

	if (Notification.permission === 'denied') {
		console.warn('알림 권한이 거부되었습니다');
		return false;
	}

	try {
		const permission = await Notification.requestPermission();
		return permission === 'granted';
	} catch (error) {
		console.error('알림 권한 요청 오류:', error);
		return false;
	}
}

