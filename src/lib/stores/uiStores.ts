/**
 * UI 상태 관리 스토어들
 */

import { writable } from 'svelte/store';
import type { UpdateMessage } from '$lib/types/messageTypes';
import type { NotificationData } from '$lib/types/notificationTypes';

/**
 * 업데이트 메시지 스토어
 * 실시간 업데이트 알림 메시지를 관리합니다.
 */
export const updateMessageStore = writable<UpdateMessage[]>([]);

/**
 * 하이라이트된 항목 스토어 (새로 추가된 항목)
 */
export const highlightedItemsStore = writable(new Set<number>());

/**
 * 업데이트된 항목 스토어
 */
export const updatedItemsStore = writable(new Set<number>());

/**
 * 삭제된 항목 스토어
 */
export const deletedItemsStore = writable(new Set<number>());

/**
 * 배치 업데이트된 항목 스토어
 */
export const batchUpdatedItemsStore = writable(new Set<number>());

/**
 * 활성 알림 스토어 (현재 화면에 표시 중인 알림들)
 */
export const activeNotificationsStore = writable<NotificationData[]>([]);
