/**
 * 연결 상태 관련 스토어들
 */

import { writable } from 'svelte/store';

/**
 * SSE 연결 상태 스토어
 * 연결 상태, 마지막 업데이트 시간, 오류 정보를 관리합니다.
 */
export const connectionStore = writable({
	status: 'disconnected' as 'connected' | 'connecting' | 'disconnected' | 'error',
	lastUpdate: null as string | null,
	error: null as string | null,
	reconnectAttempts: 0,
	maxReconnectAttempts: 5,
	reconnectDelay: 3000
});

/**
 * 네트워크 상태 스토어
 */
export const networkStore = writable({
	isOnline: navigator.onLine,
	lastOnlineAt: new Date().toISOString(),
	lastOfflineAt: null as string | null
});

/**
 * SSE 통계 스토어
 */
export const sseStatsStore = writable({
	totalMessagesReceived: 0,
	notificationsReceived: 0,
	dataUpdatesReceived: 0,
	errorsCount: 0,
	lastMessageAt: null as string | null,
	connectionUptime: 0,
	averageLatency: 0
});
