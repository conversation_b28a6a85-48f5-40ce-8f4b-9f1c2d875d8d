/**
 * 데이터 스토어들
 */

import { writable } from 'svelte/store';
import type { NotificationData } from '$lib/types/notificationTypes';
import type {
	Employee,
	Group,
	RepairGrade,
	Setting,
	PrintSetting,
	UpdateHistory
} from '$lib/types/indexedDB';
import type { CategoryStoreData } from '$lib/types/types';
import type { NotificationSettings } from '$lib/services/notificationSettings';

/**
 * 알림 데이터 스토어
 * 모든 알림 데이터를 관리합니다.
 */
export const allNotificationsStore = writable<NotificationData[]>([]);

/**
 * 카테고리 데이터 스토어
 * cate4, cate5 카테고리 데이터를 관리합니다.
 */
export const categoryStore = writable<CategoryStoreData>({
	cate4: [],
	cate5: [],
	lastUpdated: null,
	totalCount: 0
});

/**
 * 직원 데이터 스토어
 */
export const employeeStore = writable<Employee[]>([]);

/**
 * 그룹 데이터 스토어
 */
export const groupStore = writable<Group[]>([]);

/**
 * 수리 등급 데이터 스토어
 */
export const repairGradeStore = writable<RepairGrade[]>([]);

/**
 * 설정 데이터 스토어
 */
export const settingsStore = writable<Setting[]>([]);

/**
 * 인쇄 설정 데이터 스토어 (마이그레이션된 데이터)
 */
export const printSettingsStore = writable<PrintSetting[]>([]);

/**
 * 업데이트 히스토리 스토어
 */
export const historyStore = writable<UpdateHistory[]>([]);

/**
 * 알림 설정 스토어
 */
export const notificationSettingsStore = writable<NotificationSettings>({
	enableBrowserNotifications: true,
	enableSounds: true,
	enableVibration: true,
	workingHours: {
		start: '09:00',
		end: '18:00',
		enabled: false
	},
	soundFile: '/sounds/notification.mp3',
	displayDuration: {
		low: 3000,
		normal: 5000,
		high: 8000,
		urgent: 0 // 수동 닫기
	},
	disabledTypes: []
});
