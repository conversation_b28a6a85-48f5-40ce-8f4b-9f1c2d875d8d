/**
 * 스토어 유틸리티 함수들
 *
 * 스토어 상태 관리, UI 업데이트, 검증 등의 유틸리티 함수를 제공합니다.
 */

import { getData } from '$lib/services/indexedDBManager';
import type { NotificationData } from '$lib/types/notificationTypes';
import type { UpdateMessage } from '$lib/types/messageTypes';
import {
	connectionStore,
	networkStore,
	sseStatsStore,
	allNotificationsStore,
	categoryStore,
	employeeStore,
	groupStore,
	updateMessageStore,
	highlightedItemsStore,
	updatedItemsStore,
	deletedItemsStore,
	batchUpdatedItemsStore,
	activeNotificationsStore,
	notificationSettingsStore
} from '$lib/stores/index';

// ===== 연결 상태 관리 =====

/**
 * 연결 상태 스토어 업데이트
 */
export function updateConnectionStatus(
	status: 'connected' | 'connecting' | 'disconnected' | 'error',
	error?: string | null
): void {
	connectionStore.update((current) => ({
		...current,
		status,
		lastUpdate: new Date().toISOString(),
		error: error || null,
		reconnectAttempts: status === 'connected' ? 0 : current.reconnectAttempts
	}));
}

/**
 * 재연결 시도 횟수 증가
 */
export function incrementReconnectAttempts(): number {
	let attempts = 0;
	connectionStore.update((current) => {
		attempts = current.reconnectAttempts + 1;
		return {
			...current,
			reconnectAttempts: attempts
		};
	});
	return attempts;
}

/**
 * 네트워크 상태 업데이트
 */
export function updateNetworkStatus(isOnline: boolean): void {
	networkStore.update((current) => ({
		...current,
		isOnline,
		lastOnlineAt: isOnline ? new Date().toISOString() : current.lastOnlineAt,
		lastOfflineAt: !isOnline ? new Date().toISOString() : current.lastOfflineAt
	}));
}

// ===== 메시지 및 알림 관리 =====

/**
 * 업데이트 메시지 추가
 */
export function addUpdateMessage(
	message: string,
	type: 'success' | 'info' | 'warning' | 'error' = 'info',
	duration: number = 5000
): void {
	const updateMessage: UpdateMessage = {
		id: crypto.randomUUID(),
		message,
		type,
		timestamp: new Date().toISOString(),
		duration
	};

	updateMessageStore.update((current) => [
		updateMessage,
		...current.slice(0, 9) // 최대 10개 유지
	]);

	// 자동 제거
	if (duration > 0) {
		setTimeout(() => {
			removeUpdateMessage(updateMessage.id);
		}, duration);
	}
}

/**
 * 업데이트 메시지 제거
 */
export function removeUpdateMessage(messageId: string): void {
	updateMessageStore.update((current) => current.filter((m) => m.id !== messageId));
}

/**
 * 활성 알림 추가
 */
export function addActiveNotification(notification: NotificationData): void {
	activeNotificationsStore.update((current) => {
		const exists = current.find((n) => n.id === notification.id);
		if (exists) {
			return current;
		}
		return [...current, notification];
	});
}

/**
 * 활성 알림 제거
 */
export function removeActiveNotification(notificationId: number): void {
	activeNotificationsStore.update((current) => current.filter((n) => n.id !== notificationId));
}

// ===== 하이라이트 효과 관리 =====

/**
 * 하이라이트 효과 추가
 */
export function addHighlightEffect(
	ids: number[],
	type: 'new' | 'updated' | 'deleted' | 'batch' = 'new',
	duration: number = 3000
): void {
	const targetStore = {
		new: highlightedItemsStore,
		updated: updatedItemsStore,
		deleted: deletedItemsStore,
		batch: batchUpdatedItemsStore
	}[type];

	targetStore.update((current) => new Set([...current, ...ids]));

	// 자동 제거
	setTimeout(() => {
		targetStore.update((current) => {
			const updated = new Set(current);
			ids.forEach((id) => updated.delete(id));
			return updated;
		});
	}, duration);
}

/**
 * 모든 하이라이트 효과 제거
 */
export function clearAllHighlights(): void {
	highlightedItemsStore.set(new Set());
	updatedItemsStore.set(new Set());
	deletedItemsStore.set(new Set());
	batchUpdatedItemsStore.set(new Set());
}

// ===== 통계 관리 =====

/**
 * SSE 통계 업데이트
 */
export function updateSseStats(
	messageType: 'notification' | 'data_update' | 'error',
	latency?: number
): void {
	sseStatsStore.update((current) => {
		const updated = {
			...current,
			totalMessagesReceived: current.totalMessagesReceived + 1,
			lastMessageAt: new Date().toISOString()
		};

		switch (messageType) {
			case 'notification':
				updated.notificationsReceived = current.notificationsReceived + 1;
				break;
			case 'data_update':
				updated.dataUpdatesReceived = current.dataUpdatesReceived + 1;
				break;
			case 'error':
				updated.errorsCount = current.errorsCount + 1;
				break;
		}

		if (latency !== undefined) {
			updated.averageLatency = current.averageLatency * 0.9 + latency * 0.1;
		}

		return updated;
	});
}

// ===== 스토어 리셋 =====

/**
 * 스토어 상태 리셋 (로그아웃 시 사용)
 */
export function resetAllStores(): void {
	console.log('모든 스토어 리셋 시작...');

	// 데이터 스토어 리셋
	allNotificationsStore.set([]);
	categoryStore.set({ cate4: [], cate5: [], lastUpdated: null, totalCount: 0 });
	employeeStore.set([]);
	groupStore.set([]);

	// UI 상태 스토어 리셋
	updateMessageStore.set([]);
	activeNotificationsStore.set([]);

	// 하이라이트 효과 제거
	clearAllHighlights();

	// 연결 상태 리셋
	connectionStore.set({
		status: 'disconnected',
		lastUpdate: null,
		error: null,
		reconnectAttempts: 0,
		maxReconnectAttempts: 5,
		reconnectDelay: 3000
	});

	// 네트워크 상태 리셋
	networkStore.set({
		isOnline: navigator.onLine,
		lastOnlineAt: new Date().toISOString(),
		lastOfflineAt: null
	});

	// 알림 설정을 기본값으로 리셋
	notificationSettingsStore.set({
		enableBrowserNotifications: true,
		enableSounds: true,
		enableVibration: true,
		workingHours: {
			start: '09:00',
			end: '18:00',
			enabled: false
		},
		soundFile: '/sounds/notification.mp3',
		displayDuration: {
			low: 3000,
			normal: 5000,
			high: 8000,
			urgent: 0
		},
		disabledTypes: []
	});

	// 통계 리셋
	sseStatsStore.set({
		totalMessagesReceived: 0,
		notificationsReceived: 0,
		dataUpdatesReceived: 0,
		errorsCount: 0,
		lastMessageAt: null,
		connectionUptime: 0,
		averageLatency: 0
	});

	console.log('모든 스토어가 리셋되었습니다.');
}

// ===== 스토어 검증 및 모니터링 =====

/**
 * 스토어 데이터 검증
 */
export function validateStoreData(): {
	isValid: boolean;
	errors: string[];
	warnings: string[];
} {
	const errors: string[] = [];
	const warnings: string[] = [];

	try {
		let currentNotifications: NotificationData[] = [];
		let currentCategories: any = { cate4: [], cate5: [], lastUpdated: null, totalCount: 0 };
		let currentEmployees: any[] = [];
		let currentGroups: any[] = [];

		allNotificationsStore.subscribe((value) => (currentNotifications = value))();
		categoryStore.subscribe((value) => (currentCategories = value))();
		employeeStore.subscribe((value) => (currentEmployees = value))();
		groupStore.subscribe((value) => (currentGroups = value))();

		// 알림 데이터 검증
		if (!Array.isArray(currentNotifications)) {
			errors.push('알림 데이터가 배열이 아닙니다.');
		} else {
			const invalidNotifications = currentNotifications.filter((n) => !n.id || !n.title);
			if (invalidNotifications.length > 0) {
				warnings.push(`유효하지 않은 알림 ${invalidNotifications.length}개가 발견되었습니다.`);
			}
		}

		// 카테고리 데이터 검증
		if (!currentCategories || typeof currentCategories !== 'object') {
			errors.push('카테고리 데이터가 객체가 아닙니다.');
		} else {
			if (!Array.isArray(currentCategories.cate4)) {
				errors.push('cate4 데이터가 배열이 아닙니다.');
			}
			if (!Array.isArray(currentCategories.cate5)) {
				errors.push('cate5 데이터가 배열이 아닙니다.');
			}
		}

		// 직원 데이터 검증
		if (!Array.isArray(currentEmployees)) {
			errors.push('직원 데이터가 배열이 아닙니다.');
		} else {
			const invalidEmployees = currentEmployees.filter((e) => !e.id || !e.name);
			if (invalidEmployees.length > 0) {
				warnings.push(`유효하지 않은 직원 ${invalidEmployees.length}명이 발견되었습니다.`);
			}
		}

		// 그룹 데이터 검증
		if (!Array.isArray(currentGroups)) {
			errors.push('그룹 데이터가 배열이 아닙니다.');
		} else {
			const invalidGroups = currentGroups.filter((g) => !g.id || !g.name);
			if (invalidGroups.length > 0) {
				warnings.push(`유효하지 않은 그룹 ${invalidGroups.length}개가 발견되었습니다.`);
			}
		}

		return {
			isValid: errors.length === 0,
			errors,
			warnings
		};
	} catch (error) {
		console.error('스토어 데이터 검증 중 오류:', error);
		return {
			isValid: false,
			errors: ['스토어 데이터 검증 중 오류가 발생했습니다.'],
			warnings: []
		};
	}
}

/**
 * 스토어 동기화 상태 확인
 */
export async function checkStoreSyncStatus(db: IDBDatabase): Promise<{
	isSynced: boolean;
	outOfSyncStores: string[];
	lastSyncTimes: Record<string, string | null>;
}> {
	try {
		const storeNames = ['notifications', 'categories', 'employees', 'groups', 'repair_grades'];
		const outOfSyncStores: string[] = [];
		const lastSyncTimes: Record<string, string | null> = {};

		for (const storeName of storeNames) {
			try {
				const lastUpdate = await getData(db, 'settings', `${storeName}_last_update`);
				const lastUpdateTime = lastUpdate?.value || null;
				lastSyncTimes[storeName] = lastUpdateTime;

				if (lastUpdateTime) {
					const timeDiff = Date.now() - new Date(lastUpdateTime).getTime();
					if (timeDiff > 5 * 60 * 1000) {
						// 5분
						outOfSyncStores.push(storeName);
					}
				} else {
					outOfSyncStores.push(storeName);
				}
			} catch (error) {
				console.error(`${storeName} 동기화 상태 확인 실패:`, error);
				outOfSyncStores.push(storeName);
				lastSyncTimes[storeName] = null;
			}
		}

		return {
			isSynced: outOfSyncStores.length === 0,
			outOfSyncStores,
			lastSyncTimes
		};
	} catch (error) {
		console.error('스토어 동기화 상태 확인 실패:', error);
		return {
			isSynced: false,
			outOfSyncStores: ['all'],
			lastSyncTimes: {}
		};
	}
}
