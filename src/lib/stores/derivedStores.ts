/**
 * 파생 스토어들 (Derived Stores)
 */

import { derived, type Readable } from 'svelte/store';
import {
	allNotificationsStore,
	categoryStore,
	employeeStore,
	groupStore,
	repairGradeStore
} from './dataStores';
import { connectionStore } from './connectionStores';

/**
 * 읽지 않은 알림 개수
 */
export const unreadNotificationCount: Readable<number> = derived(
	allNotificationsStore,
	($notifications) => $notifications.filter((n) => !n.read).length
);

/**
 * 우선순위별 알림 개수
 */
export const notificationCountByPriority: Readable<Record<string, number>> = derived(
	allNotificationsStore,
	($notifications) => {
		const counts = { low: 0, normal: 0, high: 0, urgent: 0 };
		$notifications.forEach((n) => {
			if (!n.read && counts.hasOwnProperty(n.priority)) {
				counts[n.priority]++;
			}
		});
		return counts;
	}
);

/**
 * 연결 상태 요약
 */
export const connectionSummary: Readable<{
	isConnected: boolean;
	statusText: string;
	canReconnect: boolean;
}> = derived(connectionStore, ($connection) => ({
	isConnected: $connection.status === 'connected',
	statusText: getConnectionStatusText($connection.status),
	canReconnect: $connection.status === 'disconnected' || $connection.status === 'error'
}));

/**
 * 전체 데이터 로딩 상태
 */
export const dataLoadingState: Readable<{
	isLoading: boolean;
	loadedStores: string[];
	totalStores: number;
}> = derived(
	[categoryStore, employeeStore, groupStore, repairGradeStore, allNotificationsStore],
	([$category, $employee, $group, $repairGrade]) => {
		const stores = [
			{ name: 'category', loaded: $category.totalCount > 0 || $category.lastUpdated !== null },
			{ name: 'employee', loaded: $employee.length > 0 },
			{ name: 'group', loaded: $group.length > 0 },
			{ name: 'repairGrade', loaded: $repairGrade.length > 0 },
			{ name: 'notification', loaded: true } // 알림은 빈 배열도 로딩 완료로 간주
		];

		const loadedStores = stores.filter((s) => s.loaded).map((s) => s.name);

		return {
			isLoading: loadedStores.length < stores.length,
			loadedStores,
			totalStores: stores.length
		};
	}
);

/**
 * 연결 상태 텍스트 반환
 */
function getConnectionStatusText(status: string): string {
	switch (status) {
		case 'connected':
			return '연결됨';
		case 'connecting':
			return '연결 중...';
		case 'disconnected':
			return '연결 끊김';
		case 'error':
			return '연결 오류';
		default:
			return '알 수 없음';
	}
}
