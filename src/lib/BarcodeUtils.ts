import { tick } from 'svelte';
import {getUser, type User } from '$lib/User';
import { PrinterDatabase } from '$lib/db/PrinterDatabase';
import type { TDocumentDefinitions } from 'pdfmake/interfaces';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from '$lib/vfs_fonts';
import { BaseDirectory, exists, mkdir, writeFile } from '@tauri-apps/plugin-fs';
import { DIR_NAME } from '$stores/constant';
import { documentDir } from '@tauri-apps/api/path';
import { print_file } from 'tauri-plugin-printer-api';
import {
	deleteFile,
	executeMessage,
	getDefaultPrinter,
	handleCatch,
	makeBarcodeImage
} from '$lib/Functions';
import { authClient } from '$lib/services/AxiosBackend';

const db = new PrinterDatabase();
const user: User = getUser();

// 사용할 기본 프린트 설정
const usePrintConfig = window.localStorage.getItem('usePrintConfig') ?? '기본(default)';

async function getDocDefinition(qaid: string) {
	// 데이터베이스 초기화 추가
	await db.initDatabase();

	// 선택된 설정 가져오기
	const settings = await db.getSettings(usePrintConfig);
	await tick();
	console.log(usePrintConfig, settings);

	if (!settings || !Array.isArray(settings)) {
		await executeMessage('프린트 설정을 찾을 수 없습니다.', 'error');
		return;
	}

	// config에서 특정 코드의 설정을 찾는 헬퍼 함수
	const getConfigSettings = (code: string) => {
		const configItem = settings.find((group) => group.code === code);
		return configItem?.settings;
	};

	const fontConfig = getConfigSettings('font');
	const barcodeConfig = getConfigSettings('barcode');
	const qaidConfig = getConfigSettings('qaid');
	const dateConfig = getConfigSettings('date');
	const userConfig = getConfigSettings('user');

	const printFont = fontConfig?.fontFamily ?? 'Roboto';
	const printDateFormat = formatDate(new Date(), dateConfig?.format);

	pdfMake.vfs = pdfFonts;
	pdfMake.fonts = {
		Roboto: {
			normal: 'Roboto-Regular.ttf',
			bold: 'Roboto-Medium.ttf',
			italics: 'Roboto-Italic.ttf',
			bolditalics: 'Roboto-MediumItalic.ttf',
		},
	};

	const point = 2.83465;
	const width = Math.round(50 * point);
	const height = Math.round(30 * point);

	const docDefinition: TDocumentDefinitions = {
		header: '',
		pageSize: {
			width,
			height
		},
		pageOrientation: 'landscape',
		pageMargins: [4, 0, 4, 0],
		content: [
			{
				image: 'barcode',
				absolutePosition: { x: barcodeConfig.x, y: barcodeConfig.y },
				fit: [130, 43], // 캔버스에 딱 맞게 넣으려면 142, 43
				alignment: 'center'
			},
			{
				text: qaid, // 바코드 아래 표시할 텍스트
				absolutePosition: { x: 0, y: barcodeConfig.y + 40 },
				alignment: 'center', // 텍스트도 중앙 정렬
				fontSize: qaidConfig.fontSize,
			},
			{
				columns: [
					{
						text: printDateFormat,
						absolutePosition: { x: dateConfig.x, y: dateConfig.y },
						style: ['date'],
					},
					{
						text: `#${user.id}`,
						absolutePosition: { x: userConfig.x, y: userConfig.y },
						style: ['user'],
					}
				]
			}
		],
		images: {
			barcode: await makeBarcodeImage(qaid)
		},
		defaultStyle: {
			font: printFont,
			margin: [0, 0, 0, 0],
			lineHeight: 1
		},
		styles: {
			date: {
				fontSize: dateConfig.fontSize,
				color: dateConfig?.textColor ?? '#000000',
				bold: dateConfig?.bold ?? false,
				italics: true,
			},
			user: {
				fontSize: userConfig.fontSize,
				color: userConfig?.textColor ?? '#000000',
				bold: userConfig?.bold ?? false,
				italics: true,
			}
		}
	};

	return docDefinition;
}

async function printLabel(qaid: string) {
	// 디렉토리 존재 여부 확인
	const dirExists = await exists(DIR_NAME, { baseDir: BaseDirectory.Document });
	// 디렉토리가 존재하지 않으면 생성
	if (!dirExists) {
		try {
			await mkdir(DIR_NAME, { baseDir: BaseDirectory.Document });
		} catch (error) {
			await executeMessage(`디렉토리 생성 중 오류가 발생했습니다: ${error}`, 'error');
			return; // 디렉토리 생성 실패 시 함수 종료
		}
	}

	const defaultPrint = getDefaultPrinter();

	// 저장될 PDF 파일경로
	const writeFilePath = `${DIR_NAME}/${qaid}.pdf`;

	const documentDirPath = await documentDir();
	const readFilePath = `${documentDirPath}${writeFilePath}`;

	const docDefinition = await getDocDefinition(qaid);
	pdfMake.createPdf(docDefinition).getBuffer((buffer) => {
		const contents = new Uint8Array(buffer);

		// pdf 저장 후 프린트
		writeFile(writeFilePath, contents, { baseDir: BaseDirectory.Document }).then(() => {
			print_file({
				id: defaultPrint.id, // "QklYT0xPTiBTTFAtRDIyMA=="
				name: defaultPrint.name, // "BIXOLON SLP-D220"
				path: readFilePath,
				file: buffer,
				print_setting: {
					orientation: 'landscape',
					method: 'simplex', // duplex | simplex | duplexshort
					paper: 'tabloid', // "A2" | "A3" | "A4" | "A5" | "A6" | "letter" | "legal" | "tabloid"
					scale: 'fit', //"noscale" | "shrink" | "fit"
					repeat: 1,
					range: '1'
				},
				remove_temp: true
			}).then(() => {
				deleteFile(qaid)
					.then(() => {
						console.log(`${qaid}.pdf 파일삭제 완료`);
					})
					.catch(() => {
						console.log(`${qaid}.pdf 파일삭제 실패`);
					});
			});
		});
	});
}

// 날짜 포맷 헬퍼 함수
export function formatDate(date: Date, format?: string) {
	const yy = date.getFullYear().toString().slice(-2);
	const mm = String(date.getMonth() + 1).padStart(2, '0');
	const dd = String(date.getDate()).padStart(2, '0');
	const hours = date.getHours().toString().padStart(2, '0');
	const minutes = date.getMinutes().toString().padStart(2, '0');

	let formattedDate = '';
	switch (format) {
		case 'YY-MM-DD':
			formattedDate = `${yy}-${mm}-${dd}`;
			break;
		case 'MM/DD/YY':
			formattedDate = `${mm}/${dd}/${yy}`;
			break;
		default:
			formattedDate = `${yy}-${mm}-${dd}`;
			break;
	}

	return `${formattedDate} ${hours}:${minutes}`;
}

/**
 * qaid 재발행::일반 작업자<br>
 * 서버에서 qaid 재발행 리스트에 출력 여부 확인하고 없다면 출력
 *
 * @param qaid
 * @param id
 */
export async function isRePrint(qaid: string, id: number) {
	// QAID 재발행 테이블에서 중복확인
	try {
		const { status, data } = await authClient.get(`/wms/qaids/${qaid}/${id}`);

		if (status === 200 && data.success === false) {
			await executeMessage(data.data.message, 'error');
		} else {
			await printLabel(qaid);
		}
	} catch (e) {
		await handleCatch(e);
		return false;
	}
}

/**
 * qaid 재발행::관리자<br>
 *
 * @param qaid
 * @param id
 */
export async function isRePrintFromAdmin(qaid: string, id: number) {
	// QAID 재발행 테이블에서 중복확인
	try {
		const { status, data } = await authClient.put(`/wms/qaids/${qaid}/${id}`);

		if (status === 200 && data.success) {
			await printLabel(qaid);
		} else {
			await executeMessage(data.data.message, 'error');
		}
	} catch (e) {
		await handleCatch(e);
		return false;
	}
}

/**
 * 테스트 라벨 인쇄
 */
export async function testPrint() {
	await printLabel('Q1234567890');
}