{"$schema": "https://schema.tauri.app/config/2", "productName": "CnsproWMS", "version": "0.9.1329", "identifier": "com.cnsprowms.app", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build --mode production", "devUrl": "http://localhost:5173", "frontendDist": "../build"}, "app": {"windows": [{"title": "Cornerstone Project WMS", "width": 1280, "height": 800, "resizable": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "createUpdaterArtifacts": true, "windows": {"wix": {"language": "ko-KR"}, "nsis": {"languages": ["Korean"], "displayLanguageSelector": true}}}, "plugins": {"updater": {"windows": {"installMode": "passive"}, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDlGMEFFRjFFOUNBRkFDNTgKUldSWXJLK2NIdThLbnlnVEpIbmJzaTMzZDVPdEZ6elhDa01CTFM5MjNwSmxGY2paVVFaWWJ4THgK", "endpoints": ["https://cnspro-releases.s3.ap-northeast-2.amazonaws.com/latest.json"]}}}