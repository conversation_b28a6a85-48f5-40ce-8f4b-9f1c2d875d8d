{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main", "Print"], "permissions": ["core:default", "opener:default", {"identifier": "opener:allow-open-path", "allow": [{"path": "$DOWNLOAD/**"}]}, "dialog:default", "fs:default", {"identifier": "fs:allow-exists", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-document-write", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-read-file", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, {"identifier": "fs:allow-remove", "allow": [{"path": "$DOCUMENT/CornerstoneWMS/**"}, {"path": "$DOWNLOAD/**"}]}, "updater:default", "process:default", "printer:default", "core:window:allow-start-dragging", "core:webview:allow-create-webview-window", "core:webview:allow-webview-close", "core:window:allow-close"], "allow": {"windows": ["^coupang-.*"]}}